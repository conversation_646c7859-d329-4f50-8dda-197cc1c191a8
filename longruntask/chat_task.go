package longruntask

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/memory/chains"
	"cosy/remote"
	"cosy/sls"
	"cosy/sse"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"
)

const (
	MaxTaskEventRetryCount = 5
)

// CreateChatTask 创建task
func CreateChatTask(ctx context.Context, createParams *definition.CreateChatTaskParams) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	// 补充workspaceId的信息
	machineId, err := GetMACAddress()
	if err != nil {
		log.Warnf("Get mac error, error=%v", err)
		return buildErrorResponse("", "get mac error")
	}
	workspaceId, err := GetOrInitWorkspace(userInfo.Uid, createParams.Project)
	if err != nil {
		log.Warnf("Get or init workspace error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}

	createParams.WorkspaceId = workspaceId
	createParams.MachineId = machineId
	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(createParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.CreateTaskEndpoint, payload)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", req.URL, err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("create chat task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when create chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()
	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when create chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.CreateChatTaskResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to create chat task response, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to create chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when create chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 开启task event监听
	if result.Code == 200 && result.Data.TaskId != "" {
		EnsureWatchingTaskEvent(ctx, result.Data.TaskId)
		eventData := make(map[string]string)
		eventData["taskId"] = result.Data.TaskId
		eventData["userId"] = userInfo.Uid
		sls.Report(sls.EventTypeQuestTaskCreation, result.RequestId, eventData)
	}

	// 构建响应
	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         TransRemoteTaskToTask(&result.Data),
	}
	return response
}

func UpdateChatTask(taskId string, updateParams *definition.UpdateChatTaskParams) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(updateParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPut, fmt.Sprintf(definition.UpdateTaskEndpoint, taskId), payload)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", req.URL, err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("update chat task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when create chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when update chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.BaseResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to update chat task, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to update chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when update chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	if result.Code != 200 {
		log.Errorf("failed to update chat task,errorCode=%s,errorMsg=%s", result.ErrorCode, result.Message)
		return buildErrorResponse(result.ErrorCode, result.Message)
	}
	// 重新查询下task的详情
	return GetChatTask(taskId)
}

func GetChatTask(taskId string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}
	requestUrl := util.BuildUrlQuery(fmt.Sprintf(definition.GetTaskEndpoint, taskId), map[string]any{
		"withExecution": true,
		"Encode":        config.Remote.MessageEncode,
	})
	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("get task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when get task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when get task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.GetChatTaskResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get chat task, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to update chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when get task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         TransRemoteTaskToTask(&result.Data),
	}
	return response
}

func ListProjectTasks(listTaskParams *definition.ListChatTaskParams) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}
	// 补充workspaceId的信息
	machineId, err := GetMACAddress()
	if err != nil {
		log.Warnf("Get mac error, error=%v", err)
		return buildErrorResponse("", "get mac error")
	}
	// 根据project换算workspace
	workspaceId, err := GetOrInitWorkspace(userInfo.Uid, listTaskParams.Project)
	if err != nil {
		log.Warnf("Get or init workspace error, error=%v", err)
		return buildErrorResponse("", "get or init workspace error")
	}

	excludedWorkspaceId, err := GetOrInitWorkspace(userInfo.Uid, listTaskParams.ExcludedProject)
	if err != nil {
		log.Warnf("Get or init excluded workspace error, error=%v", err)
		return buildErrorResponse("", "get or init excluded workspace error")
	}

	// 判断listTaskParams.InProgress是否为nil
	var requestUrl string
	if listTaskParams.InProgress == nil {
		requestUrl = util.BuildUrlQuery(definition.ListAllTasksEndpoint, map[string]any{
			"workspaceId":         workspaceId,
			"excludedWorkspaceId": excludedWorkspaceId,
			"machineId":           machineId,
			"nameLike":            listTaskParams.NameLike,
			"pageNumber":          listTaskParams.Page,
			"pageSize":            listTaskParams.PageSize,
			"withExecution":       true,
			"timestamp":           time.Now().UnixMilli(),
			"Encode":              config.Remote.MessageEncode,
			"statuses":            listTaskParams.Statuses,
		})
	} else {
		requestUrl = util.BuildUrlQuery(definition.ListTasksEndpoint, map[string]any{
			"workspaceId":         workspaceId,
			"excludedWorkspaceId": excludedWorkspaceId,
			"machineId":           machineId,
			"inProgress":          *listTaskParams.InProgress,
			"nameLike":            listTaskParams.NameLike,
			"pageNumber":          listTaskParams.Page,
			"pageSize":            listTaskParams.PageSize,
			"withExecution":       true,
			"timestamp":           time.Now().UnixMilli(),
			"Encode":              config.Remote.MessageEncode,
			"statuses":            listTaskParams.Statuses,
		})
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("get task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when list project tasks, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()
	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when list project tasks, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	// 解析响应
	var result definition.ListChatTaskResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to list chat task, url=%s, status=%s", requestUrl, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to list chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when list project tasks, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 构建响应
	var chatTasks []definition.ChatTask
	for _, remoteTask := range result.Data.Items {
		chatTasks = append(chatTasks, TransRemoteTaskToTask(&remoteTask))
	}
	// 为了兼容新老版本，加了version的逻辑，等前端上线后，可以去掉这个逻辑 TODO
	if listTaskParams.Version == 2 {
		response := &definition.Response{
			Success:      result.Code == 200,
			ErrorCode:    result.ErrorCode,
			ErrorMessage: result.Message,
			// Data 是一个json object
			Data: map[string]any{
				"pageNumber": result.Data.PageNumber,
				"pageSize":   result.Data.PageSize,
				"totalSize":  result.Data.TotalSize,
				"items":      chatTasks,
			},
		}
		return response
	}
	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         chatTasks,
	}
	return response
}

func EnsureWatchingTaskEvent(ctx context.Context, taskId string) {
	if config.IsRemoteAgentMode() {
		// 远程模式下，不需要同步代码库
		return
	}
	taskWatching := getTaskWatching(taskId)
	if taskWatching {
		log.Infof("watch task events has exist, taskId=%s", taskId)
		return
	}
	go watchTaskEvents(ctx, taskId)
}

// WatchTaskStatus 该接口为SSE
func watchTaskEvents(ctx context.Context, taskId string) {
	setTaskWatching(taskId, true)

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return
	}

	requestUrl := util.BuildUrlQuery(fmt.Sprintf(definition.WatchTaskEventsEndpoint, taskId), map[string]any{"Encode": config.Remote.MessageEncode})
	req, requestError := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if requestError != nil {
		log.Warnf("Build request error, url=%s, error=%v", requestUrl, requestError)
		return
	}
	workspaceOnce := sync.Once{}
	// 创建请求
	sseClient := sse.NewSseRemoteAgentClient(map[string]string{})
	// 定义处理函数
	handleFun := handleTaskEvent(ctx, taskId, &workspaceOnce)
	// 发送请求&读取数据
	err := sseClient.SubscribeWithContext(ctx, time.Duration(definition.WatchTaskEventSSETimeout)*time.Second, req, handleFun, func(req *http.Request, rsp *http.Response) {
		log.Errorf("watch task event timeout, taskId=%s", taskId)
	})
	if err != nil {
		log.Errorf("watch task event error, taskId=%s, err=%v", taskId, err)
		if strings.Contains(err.Error(), "Not Found") || strings.Contains(err.Error(), "Conflict") {
			deleteTaskState(taskId)
			return
		}
		retryCount := getRetryCount(taskId)
		if retryCount <= MaxTaskEventRetryCount {
			incrementRetryCount(taskId)
			// sleep 1  second
			time.Sleep(time.Duration(1) * time.Second)
			log.Warnf("watch task event accuse error，retry, retryCount=%d, taskId=%s", retryCount, taskId)
			watchTaskEvents(ctx, taskId)
		} else {
			log.Warnf("watch task even error, taskId=%s, retry count reached, giving up", taskId)
			deleteTaskState(taskId)
		}
	}
}

func handleTaskEvent(ctx context.Context, taskId string, workspaceOnce *sync.Once) func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
	return func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("get task status triggered panic. taskId: %+v", taskId)

				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Errorf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()
		// 重试次数清0
		resetRetryCount(taskId)
		log.Infof("watch task events. taskId=%s, type=%s, data=%s", taskId, msg.Event, string(msg.Data))
		switch string(msg.Event) {
		case "error":
			log.Warnf("watch task events finish error, taskId=%s, reason=%s", taskId, msg.Data)
			return
		case "finish":
			log.Debugf("watch task events finish, taskId=%s, reason=%s", taskId, msg.Data)
			deleteTaskState(taskId)
			return
		case "close":
			log.Debugf("watch task events close, taskId=%s, reason=%s", taskId, msg.Data)
			deleteTaskState(taskId)
			return
		case definition.ChatTaskTypeStatusChange:
			var taskStatusChangeData = definition.TaskStatusChangeData{}
			err := json.Unmarshal(msg.Data, &taskStatusChangeData)
			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if taskStatusChangeData.NewStatus != "" && taskStatusChangeData.NewStatus != taskStatusChangeData.OldStatus {
				// 同步状态变更的消息给ide
				syncTaskStatus(ctx, taskId, taskStatusChangeData.NewStatus)
			}
			return
		case definition.ChatTaskTypeRuntimeSSHRelay:
			log.Debugf("watch task event ChatTaskTypeRuntimeSSHRelay, taskId=%s, data=%s", taskId, msg.Data)
			// todo 应该需要判断 ssh relay ready才进行代码库同步
			workspaceOnce.Do(func() {
				if CheckIfWorkspaceSynced(taskId) {
					log.Infof("workspace is already synced for task %s, skip to sync workspace", taskId)
					return
				}
				util.GoSafeRoutine(func() {
					bgCtx := context.Background()
					syncCtx, cancel := context.WithTimeout(bgCtx, 10*time.Minute)
					defer cancel()
					localPath := getLocalWorkspacePath(ctx)
					remotePath := "/data/workspace"
					if err := SyncWorkspace(syncCtx, taskId, localPath, remotePath); err != nil {
						log.Errorf("sync workspace error, taskId=%s, error=%v", taskId, err)
						// todo 这里应该透露一个状态异常给到前端
						//msg := fmt.Sprintf("failed to sync workspace to remote, err=%s", err)
					}
					// todo 这里应该透露一个状态给前端
					// 代码库同步完成
					//msg := fmt.Sprintf("sync workspace to remote successfully")
				})
				// todo 这里应该构造一个状态给前端
				// 正在同步代码库
				//msg := fmt.Sprintf("starting to sync workspace to remote")
			})
			return
		case definition.ChatTaskTypeSandboxBootStageChange:
			// 启动阶段变更
			var sandBoxStageChangeData = definition.SandboxBootStageChangeData{}
			err := json.Unmarshal(msg.Data, &sandBoxStageChangeData)
			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if sandBoxStageChangeData.NewStage != "" || sandBoxStageChangeData.NewStatus != "" {
				// 同步状态变更的消息给ide
				syncSandBoxStageChangeLog(ctx, taskId, sandBoxStageChangeData.NewStage, sandBoxStageChangeData.NewStatus)
			}
			return
		case definition.ChatTaskTypeSandboxBootLog:
			// 启动日志
			var sandBoxLogData = definition.SandboxBootLogData{}
			err := json.Unmarshal(msg.Data, &sandBoxLogData)
			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if sandBoxLogData.Output != "" {
				// 同步状态变更的消息给ide
				syncSandBoxLog(ctx, taskId, sandBoxLogData.Output)
			}
			return
		case definition.ChatTaskTypeActionFlowUpdate:
			log.Debugf("watch task event ChatTaskTypeActionFlowUpdate")
			actionFlowUpdate := ActionFlowUpdate{}
			err := json.Unmarshal(msg.Data, &actionFlowUpdate)
			if err != nil {
				log.Error("Unmarshal actionFlowUpdate data error: ", err)
				return
			}
			err = websocket.SendRequestWithTimeout(ctx, "task/actionFlow/content/sync", actionFlowUpdate, nil, 3*time.Second)
			if err != nil {
				log.Error("send request task/actionFlow/content/sync error:", err)
				return
			}
		case definition.ChatTaskTypeTaskMessage:
			log.Warnf("watch task event ChatTaskTypeTaskMessage, taskId=%s, type=%s, data=%s", taskId, msg.Event, string(msg.Data))
		}
	}
}

func syncTaskStatus(ctx context.Context, taskId string, status string) {
	log.Infof("syncTaskStatus, taskId=%s, status=%s", taskId, status)

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("WebSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		request := &definition.SyncTaskStatusRequest{
			Id:     taskId,
			Status: status,
		}
		e := websocket.SendRequestWithTimeout(ctx, "task/status/sync",
			request, nil, 10*time.Second)
		if e != nil {
			log.Error("syncTaskStatus task/status/sync error:", e)
		}
	}
}

func syncSandBoxStageChangeLog(ctx context.Context, taskId string, stage string, status string) {
	log.Debugf("syncSandBoxStageChangeLog, taskId=%s, stage=%s", taskId, stage)

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("WebSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		request := &definition.SyncSandboxBootStageChangeRequest{
			Id:        taskId,
			NewStage:  stage,
			NewStatus: status,
		}
		e := websocket.SendRequestWithTimeout(ctx, "task/sandbox/stage/sync",
			request, nil, 10*time.Second)
		if e != nil {
			log.Error("syncTaskStatus task/sandbox/stage/sync error:", e)
		}
	}
}

func syncSandBoxLog(ctx context.Context, taskId string, output string) {
	log.Debugf("syncSandBoxLog, taskId=%s, output=%s", taskId, output)

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("WebSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		request := &definition.SyncSandboxBootLogRequest{
			Id:     taskId,
			Output: output,
		}
		e := websocket.SendRequestWithTimeout(ctx, "task/sandbox/log/sync",
			request, nil, 10*time.Second)
		if e != nil {
			log.Error("syncTaskStatus task/sandbox/log/sync error:", e)
		}
	}
}

func UpdateChatTaskStatus(ctx context.Context, taskId string, status string, callbacks []func(ctx context.Context, taskId string, status string) error) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	// 监听task的变化
	EnsureWatchingTaskEvent(context.Background(), taskId)

	updateParams := definition.UpdateChatTaskStatusParams{
		Status: status,
	}
	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(updateParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPut, fmt.Sprintf(definition.UpdateTaskStatusEndpoint, taskId), payload)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("update chat task status time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when update chat task status, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when update chat task status, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.BaseResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to update chat task status, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to update chat task status, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when update chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
	}

	for _, callback := range callbacks {
		if err := callback(ctx, taskId, status); err != nil {
			log.Error(err)
		}
	}

	return response
}

func DeleteChatTask(taskId string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodDelete, fmt.Sprintf(definition.DeleteTaskEndpoint, taskId), nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	// 打印下header的内容
	log.Debugf("Delete Chat Task request header: %v", req.Header)
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("Delete chat task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when delete chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when delete chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.BaseResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to delete chat task, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to delete chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when delete chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Code == 200,
	}
	return response
}

func CancelChatTask(taskId string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	payload := remote.HttpPayload{
		Payload:       "",
		EncodeVersion: config.Remote.MessageEncode,
	}
	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, fmt.Sprintf(definition.CancelTaskEndpoint, taskId), payload)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("Cancel chat task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when cancel chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when cancel chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.BaseResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to cancel chat task, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to cancel chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when cancel chat task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Code == 200,
	}
	return response
}

func GetChatTaskBootLog(taskId string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}
	requestUrl := util.BuildUrlQuery(fmt.Sprintf(definition.GetTaskBootLogEndpoint, taskId), map[string]any{"Encode": config.Remote.MessageEncode})
	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("get task boot log time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when get task boot log, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when get task boot log, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.GetChatTaskBootLogResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get chat task boot log, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to get chat task boot log, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when get task boot log, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Data,
	}
	return response
}

func TransRemoteTaskToTask(remoteTask *definition.RemoteChatTask) definition.ChatTask {
	task := &definition.ChatTask{
		Id:                 remoteTask.TaskId,
		Name:               remoteTask.Name,
		PrevStatus:         remoteTask.PrevStatus,
		Status:             remoteTask.Status,
		CreateTime:         remoteTask.CreatedAtTimestamp,
		EndTime:            remoteTask.EndAtTimestamp,
		UpdatedAtTimestamp: remoteTask.UpdatedAtTimestamp,
		FilePath:           remoteTask.Workspace.FilePath,
	}
	if remoteTask.Execution.ExecutionId != "" {
		task.SourceBranch = remoteTask.Execution.SourceBranch
		task.ExecuteStartTime = remoteTask.Execution.StartedAt
		task.ExecuteEndTime = remoteTask.Execution.CompletedAt
		task.SSHRelayAddress = remoteTask.Execution.RuntimeInfo.SSHRelayAddress
		task.BootStatus = remoteTask.Execution.BootStatus
		task.FinishedActionCount = remoteTask.Execution.FinishedActionCount
		task.TotalActionCount = remoteTask.Execution.ActionCount
		task.HeadCommitId = remoteTask.Execution.HeadCommitId
		task.RawConfig = remoteTask.Execution.RawConfig
	}
	return *task
}

func (t *TaskManager) commonTaskCall(ctx context.Context, method string, endpoint string, requestBody any, responseBody any) error {
	request, err := remote.BuildBigModelAuthRequest(method, endpoint, requestBody)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return err
	}
	resp, err := t.httpClient.Do(request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("call task service failed. endpoint : %s, status code: %d. response body: %s", endpoint, resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, responseBody)
	if err != nil {
		return err
	}
	return nil
}

func OnRemoteStatusChanged(ctx context.Context, taskId string, status string) error {
	if config.IsRemoteAgentMode() {
		return nil
	}
	switch status {
	case definition.ChatTaskStatusAccepted:
		// FIXME sessionID 理论上是要前端传的，暂时先这样
		sessionId := strings.TrimPrefix(taskId, "task-")
		taskMgr := NewTaskManager()
		chatMessages := []definition.ChatMessage{}
		for page := 1; page > 0; page++ {
			resp, err := taskMgr.GetSessionMessages(context.Background(), sessionId, "", 100, page)
			if err != nil {
				break
			}
			if len(resp.Items) == 0 {
				break
			}
			chatMessages = append(chatMessages, resp.Items...)
			if page*100 > resp.TotalSize {
				break
			}
		}
		if len(chatMessages) == 0 {
			return nil
		}
		sort.Slice(chatMessages, func(i, j int) bool {
			return chatMessages[i].GmtCreate < chatMessages[j].GmtCreate
		})
		taskResp := GetChatTask(taskId)
		if !taskResp.Success {
			return errors.New(taskResp.ErrorMessage)
		}
		taskInfo, _ := taskResp.Data.(definition.ChatTask)
		rawConfig := definition.RawConfig{}
		if err := json.Unmarshal([]byte(taskInfo.RawConfig), &rawConfig); err != nil {
			return err
		}
		askParams := definition.AskParams{
			ChatContext:         rawConfig.ChatContext,
			PluginPayloadConfig: rawConfig.PluginPayloadConfig,
		}
		inputs := map[string]any{
			common.KeyRequestId:     chatMessages[0].RequestId,
			common.KeySessionId:     sessionId,
			common.KeyAgentMessages: chatMessages,
			common.KeyChatAskParams: &askParams,
		}
		chains.InvokeExtractMemoryFromRemoteChains(ctx, taskId, sessionId, inputs, chatMessages)
	}
	return nil
}

func getLocalWorkspacePath(ctx context.Context) string {
	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	rootPath, _ := workspaceInfo.GetWorkspaceFolder()
	return rootPath
}

func buildErrorResponse(errorCode string, errorMessage string) *definition.Response {
	return &definition.Response{
		Success:      false,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
	}
}
