package remote

import (
	"context"
	"cosy/config"
	"cosy/log"
	"cosy/util"
	"os"
	"sync"
	"time"

	"github.com/spf13/viper"

	"fmt"

	"github.com/spf13/cast"
)

// EndpointType 端点类型
type EndpointType int

const (
	EndpointTypeCenter EndpointType = iota
	EndpointTypeInfer
)

// EndpointStatus 端点状态
type EndpointStatus struct {
	URL             string    `json:"url"`
	Healthy         bool      `json:"healthy"`
	LastCheckTime   time.Time `json:"lastCheckTime"`
	LastLatency     int       `json:"lastLatency"`     // 最后延迟(毫秒)
	LastFailureTime time.Time `json:"lastFailureTime"` // 最后失败时间
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	CheckInterval      time.Duration `json:"checkInterval"`      // 检查间隔
	CircuitBreakerTime time.Duration `json:"circuitBreakerTime"` // 熔断时间
}

// RegionFailoverService 区域故障服务
type RegionFailoverService struct {
	//系统初始化时，已存储的 preferred endpoints、嵌入HA Endpoint配置都更新到这里参与决策
	centerEndpoints   map[string]*EndpointStatus // 中心节点，动态路由
	inferEndpoints    map[string]*EndpointStatus // 推理节点，动态路由
	mutex             sync.RWMutex
	healthCheckConfig HealthCheckConfig
	ctx               context.Context
	cancel            context.CancelFunc
	isRunning         bool

	bestCenterEndpoint string // 最优中心端点
	bestInferEndpoint  string // 最优推理端点

	isBestRegionEndpointChanged bool // 变化后立即触发best region重新的存储
}

const (
	//重新选择延迟差异阈值，避免重选
	reselectLatencyThreshold = 30.0
)

// 全局实例
var GlobalRegionFailService *RegionFailoverService

// IsRegionFailoverDisable 是否禁用区域故障服务
func IsRegionFailoverDisable() bool {
	val := os.Getenv("LINGMA_FAILOVER_DISABLE")
	return cast.ToBool(val)
}

// InitRegionFailoverService 初始化区域故障服务
func InitRegionFailoverService() {
	if GlobalRegionFailService != nil {
		return
	}

	GlobalRegionFailService = &RegionFailoverService{
		centerEndpoints: make(map[string]*EndpointStatus),
		inferEndpoints:  make(map[string]*EndpointStatus),
		healthCheckConfig: HealthCheckConfig{
			CheckInterval:      5 * time.Minute,  // 5分钟检查一次
			CircuitBreakerTime: 10 * time.Minute, // 熔断时间，熔断时间内不恢复，避免频繁切换
		},
	}

	// 从配置中加载端点状态
	GlobalRegionFailService.loadEndpointsLocalConfig()

	//使用内置的region HA配置更新
	GlobalRegionFailService.loadBundledRegionHAConfig()

	GlobalRegionFailService.loadAndCheckHealth(true)

	// 启动健康检查
	go GlobalRegionFailService.startHealthCheck()

	log.Infof("Region failover service initialized.")
}

// loadEndpointsLocalConfig 从配置加载端点
func (r *RegionFailoverService) loadEndpointsLocalConfig() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	var preferredNode *config.EndpointConfig

	// 加载偏好中心节点配置
	preferredNode = config.GlobalRegionConfig.PreferredCenterNode
	if preferredNode != nil && preferredNode.Endpoint != "" {
		//校验已保存的preference endpoint是否匹配初始化配置
		if isPreferredEndpointMismatch(preferredNode) {
			preferredNode = nil
			config.GlobalRegionConfig.PreferredCenterNode = nil
		}
	}
	if preferredNode != nil && preferredNode.Endpoint != "" {
		r.centerEndpoints[preferredNode.Endpoint] = &EndpointStatus{
			URL:           preferredNode.Endpoint,
			Healthy:       true,
			LastCheckTime: time.Now(),
			LastLatency:   preferredNode.Latency,
		}
	}

	//加载偏好推理节点配置
	preferredNode = config.GlobalRegionConfig.PreferredInferenceNode
	//校验已保存的preference endpoint是否匹配初始化配置
	if isPreferredEndpointMismatch(preferredNode) {
		preferredNode = nil
		config.GlobalRegionConfig.PreferredInferenceNode = nil
	}
	if preferredNode != nil && preferredNode.Endpoint != "" {
		r.inferEndpoints[preferredNode.Endpoint] = &EndpointStatus{
			URL:           preferredNode.Endpoint,
			Healthy:       true,
			LastCheckTime: time.Now(),
			LastLatency:   preferredNode.Latency,
		}
	}

}

// 使用内置的端点更新Endpoint
func (r *RegionFailoverService) loadBundledRegionHAConfig() {
	regionHAConfig := config.BundledRegionHAConfig

	if regionHAConfig == nil {
		log.Warn("BundledRegionHAConfig is nil, skipping endpoint update")
		return
	}

	// 将RegionHAConfig转换为RegionUrlConfig格式
	regionUrlConfig := RemoteRegionConfig{
		CenterNodes: regionHAConfig.CenterNodes,
		InferNodes:  regionHAConfig.InferNodes,
		DataNodeMap: regionHAConfig.DataNodes,
	}

	// 检查是否有有效的端点配置
	if len(regionUrlConfig.CenterNodes) == 0 && len(regionUrlConfig.InferNodes) == 0 {
		log.Warn("No valid endpoints found in BundledRegionHAConfig")
		return
	}

	// 调用现有的updateEndpoints方法
	r.updateEndpoints(regionUrlConfig)
}

// updateEndpoints 更新端点列表
func (r *RegionFailoverService) updateEndpoints(regionUrls RemoteRegionConfig) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 处理中心节点
	if regionUrls.CenterNodes != nil && len(regionUrls.CenterNodes) > 0 {
		for _, url := range regionUrls.CenterNodes {
			if existing, exists := r.centerEndpoints[url]; exists {
				// 保留现有状态，但更新节点类型
				r.centerEndpoints[url] = existing
			} else {
				// 新端点，设置初始状态
				r.centerEndpoints[url] = &EndpointStatus{
					URL:           url,
					Healthy:       true,
					LastCheckTime: time.Now(),
				}
			}
		}

	}

	// 处理推理节点
	if regionUrls.InferNodes != nil && len(regionUrls.InferNodes) > 0 {
		for _, url := range regionUrls.InferNodes {
			if existing, exists := r.inferEndpoints[url]; exists {
				r.inferEndpoints[url] = existing
			} else {
				// 新端点，设置初始状态
				r.inferEndpoints[url] = &EndpointStatus{
					URL:           url,
					Healthy:       true,
					LastCheckTime: time.Now(),
				}
			}
		}
	}

}

// startHealthCheck 启动健康检查
func (r *RegionFailoverService) startHealthCheck() {
	r.isRunning = true
	ticker := time.NewTicker(r.healthCheckConfig.CheckInterval)
	defer ticker.Stop()

	log.Infof("Health check started with interval: %v", r.healthCheckConfig.CheckInterval)

	for {
		select {
		case <-ticker.C:
			r.loadAndCheckHealth(false)
		}
	}
}

func (r *RegionFailoverService) loadAndCheckHealth(initialCheck bool) {
	remoteEndpointConfig := FetchRemoteEndpointConfig()
	if remoteEndpointConfig != nil && !remoteEndpointConfig.IsEmpty() {
		r.updateEndpoints(*remoteEndpointConfig)
	}
	r.performHealthCheck(initialCheck)
}

// performHealthCheck 执行健康检查
func (r *RegionFailoverService) performHealthCheck(initialCheck bool) {
	r.mutex.RLock()

	// 收集所有端点
	var allEndpoints []*EndpointStatus
	for _, endpoint := range r.centerEndpoints {
		allEndpoints = append(allEndpoints, endpoint)
	}
	for _, endpoint := range r.inferEndpoints {
		allEndpoints = append(allEndpoints, endpoint)
	}

	r.mutex.RUnlock()

	// 并发检查所有端点
	var wg sync.WaitGroup

	for _, endpoint := range allEndpoints {
		wg.Add(1)
		go func(ep *EndpointStatus) {
			defer wg.Done()
			r.checkEndpointHealth(ep)
		}(endpoint)
	}

	wg.Wait()

	r.detectAndSaveBestEndpoints(initialCheck)

	// 记录健康检查结果
	r.logHealthStatus()
}

func (r *RegionFailoverService) detectAndSaveBestEndpoints(initialCheck bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 获取最佳中心端点
	r.detectBestEndpoint(EndpointTypeCenter, initialCheck)
	// 获取最佳推理端点
	r.detectBestEndpoint(EndpointTypeInfer, initialCheck)

	if !r.isBestRegionEndpointChanged {
		return
	}
	r.isBestRegionEndpointChanged = false

	isGlobalPreferredEndpointChanged := false

	// 更新推理端点配置
	if r.bestInferEndpoint != "" && r.isNeedUpdatePreferredEndpoint(r.bestInferEndpoint, config.GlobalRegionConfig.PreferredInferenceNode) {
		isGlobalPreferredEndpointChanged = true

		// 更新配置
		var inferLatency int

		if endpoint, exists := r.inferEndpoints[r.bestInferEndpoint]; exists {
			inferLatency = endpoint.LastLatency
		}
		config.GlobalRegionConfig.PreferredInferenceNode = &config.EndpointConfig{
			Endpoint: r.bestInferEndpoint,
			Latency:  inferLatency,
		}

	}

	if r.bestCenterEndpoint != "" && r.isNeedUpdatePreferredEndpoint(r.bestCenterEndpoint, config.GlobalRegionConfig.PreferredCenterNode) {
		isGlobalPreferredEndpointChanged = true

		var inferLatency int

		if endpoint, exists := r.centerEndpoints[r.bestCenterEndpoint]; exists {
			inferLatency = endpoint.LastLatency
		}
		config.GlobalRegionConfig.PreferredCenterNode = &config.EndpointConfig{
			Endpoint: r.bestCenterEndpoint,
			Latency:  inferLatency,
		}
	}

	if isGlobalPreferredEndpointChanged {
		viper.Set("region_config", config.GlobalRegionConfig)
		err := viper.WriteConfig()
		if err != nil {
			log.Errorf("save region config fail. err: %v", err)
		} else {
			log.Debugf("save region config success. config: " + util.ToJsonStr(config.GlobalRegionConfig))
		}
	}

}

// checkEndpointHealth 检查单个端点健康状态
func (r *RegionFailoverService) checkEndpointHealth(endpoint *EndpointStatus) {
	now := time.Now()

	// 如果端点在熔断状态，检查是否到了重试时间
	if !endpoint.Healthy && now.Sub(endpoint.LastFailureTime) < r.healthCheckConfig.CircuitBreakerTime {
		return
	}

	latency := testLatency(endpoint.URL)

	endpoint.LastCheckTime = now
	endpoint.LastLatency = latency

	if latency >= invalidLatency {

		// 健康检查失败
		endpoint.LastFailureTime = now

		// 判断是否需要标记为不健康
		if endpoint.Healthy {
			endpoint.Healthy = false
			log.Warnf("Endpoint %s marked as unhealthy.", endpoint.URL)
		}
	} else {
		// 判断是否需要标记为健康
		if !endpoint.Healthy {
			endpoint.Healthy = true
			log.Infof("Endpoint %s recovered.", endpoint.URL)
		}
	}
}

// logHealthStatus 记录健康状态
func (r *RegionFailoverService) logHealthStatus() {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 按节点类型分类统计
	centerHealthy := make([]string, 0)
	centerUnhealthy := make([]string, 0)
	inferHealthy := make([]string, 0)
	inferUnhealthy := make([]string, 0)
	dataHealthy := make([]string, 0)
	dataUnhealthy := make([]string, 0)

	// 统计中心节点
	for _, endpoint := range r.centerEndpoints {
		endpointInfo := fmt.Sprintf("%s(latency:%dms)", endpoint.URL, endpoint.LastLatency)
		if endpoint.Healthy {
			centerHealthy = append(centerHealthy, endpointInfo)
		} else {
			centerUnhealthy = append(centerUnhealthy, endpointInfo)
		}
	}

	// 统计推理节点
	for _, endpoint := range r.inferEndpoints {
		endpointInfo := fmt.Sprintf("%s(latency:%dms)", endpoint.URL, endpoint.LastLatency)
		if endpoint.Healthy {
			inferHealthy = append(inferHealthy, endpointInfo)
		} else {
			inferUnhealthy = append(inferUnhealthy, endpointInfo)
		}
	}

	// 记录健康状态总览
	totalHealthy := len(centerHealthy) + len(inferHealthy) + len(dataHealthy)
	totalUnhealthy := len(centerUnhealthy) + len(inferUnhealthy) + len(dataUnhealthy)

	log.Infof("Health check results - %d healthy, %d unhealthy endpoints", totalHealthy, totalUnhealthy)

	// 详细记录各类型节点状态
	if len(centerHealthy) > 0 {
		log.Infof("Center nodes healthy (%d): %v", len(centerHealthy), centerHealthy)
	}
	if len(centerUnhealthy) > 0 {
		log.Warnf("Center nodes unhealthy (%d): %v", len(centerUnhealthy), centerUnhealthy)
	}

	if len(inferHealthy) > 0 {
		log.Infof("Inference nodes healthy (%d): %v", len(inferHealthy), inferHealthy)
	}
	if len(inferUnhealthy) > 0 {
		log.Warnf("Inference nodes unhealthy (%d): %v", len(inferUnhealthy), inferUnhealthy)
	}

	if len(dataHealthy) > 0 {
		log.Infof("Data nodes healthy (%d): %v", len(dataHealthy), dataHealthy)
	}
	if len(dataUnhealthy) > 0 {
		log.Warnf("Data nodes unhealthy (%d): %v", len(dataUnhealthy), dataUnhealthy)
	}

	// 显示当前最优端点
	if r.bestCenterEndpoint != "" {
		if endpoint, exists := r.centerEndpoints[r.bestCenterEndpoint]; exists {
			log.Infof("Current best center endpoint: %s (latency:%dms)", r.bestCenterEndpoint, endpoint.LastLatency)
		}
	}
	if r.bestInferEndpoint != "" {
		if endpoint, exists := r.inferEndpoints[r.bestInferEndpoint]; exists {
			log.Infof("Current best inference endpoint: %s (latency:%dms)", r.bestInferEndpoint, endpoint.LastLatency)
		}
	}

}

// detectBestEndpoint 通用的最佳端点检测方法
func (r *RegionFailoverService) detectBestEndpoint(endpointType EndpointType, initialCheck bool) {
	var allHealthyEndpoints []*EndpointStatus
	var endpointsMap map[string]*EndpointStatus
	var preferredConfig *config.EndpointConfig
	var currentBestEndpoint string

	// 根据端点类型选择相应的映射和配置
	switch endpointType {
	case EndpointTypeCenter:
		endpointsMap = r.centerEndpoints
		preferredConfig = config.GlobalRegionConfig.PreferredCenterNode
		currentBestEndpoint = r.bestCenterEndpoint
	case EndpointTypeInfer:
		endpointsMap = r.inferEndpoints
		preferredConfig = config.GlobalRegionConfig.PreferredInferenceNode
		currentBestEndpoint = r.bestInferEndpoint
	default:
		return
	}

	// 收集所有健康的端点
	for _, endpoint := range endpointsMap {
		if endpoint.Healthy {
			allHealthyEndpoints = append(allHealthyEndpoints, endpoint)
		}
	}

	if len(allHealthyEndpoints) == 0 {
		// 没有健康端点，不重新选举
		if preferredConfig == nil || preferredConfig.Endpoint == "" {
			if currentBestEndpoint != "" {
				// 兜底吧
				preferredConfig = &config.EndpointConfig{
					Endpoint: currentBestEndpoint,
					Latency:  0,
				}
			} else {
				endpointStatus := getAnyEndpoint(endpointsMap)
				if endpointStatus != nil {
					preferredConfig = &config.EndpointConfig{
						Endpoint: endpointStatus.URL,
						Latency:  endpointStatus.LastLatency,
					}
				}
			}
		}
		return
	}

	if initialCheck {
		// 首次启动检查：考虑Endpoint健康度和rt差异
		var currentSavedEndpoint *EndpointStatus

		// 获取当前保存的Endpoint状态
		if preferredConfig != nil && preferredConfig.Endpoint != "" {
			if savedEndpoint, exists := endpointsMap[preferredConfig.Endpoint]; exists {
				currentSavedEndpoint = savedEndpoint
			}
		}

		// 找到rt延迟最低的健康Endpoint
		bestCandidateEndpoint := r.findLowestLatencyEndpoint(allHealthyEndpoints)
		if bestCandidateEndpoint == nil {
			return
		}

		var newBestEndpoint string

		// 当前保存的Endpoint不健康或不存在，或者延迟已经低于候选Endpoint的latency，继续使用保存的Endpoint
		if currentSavedEndpoint == nil || !currentSavedEndpoint.Healthy || currentSavedEndpoint.LastLatency >= invalidLatency {
			newBestEndpoint = bestCandidateEndpoint.URL
		} else if currentSavedEndpoint.LastLatency <= bestCandidateEndpoint.LastLatency {
			//如果当前保存Endpoint的延迟已经低于候选Endpoint的latency
			newBestEndpoint = currentSavedEndpoint.URL
		} else {
			percentageDiff := calculateLatencyDiff(bestCandidateEndpoint.LastLatency, currentSavedEndpoint.LastLatency)
			if percentageDiff <= reselectLatencyThreshold {
				// 延迟差异小于30%，保持当前保存的Endpoint
				newBestEndpoint = currentSavedEndpoint.URL
			} else {
				// 延迟差异超过30%，更新为最佳候选Endpoint
				newBestEndpoint = bestCandidateEndpoint.URL
			}
		}

		// 检查是否有变化并更新
		if newBestEndpoint != "" && currentBestEndpoint != newBestEndpoint {
			switch endpointType {
			case EndpointTypeCenter:
				r.bestCenterEndpoint = newBestEndpoint
			case EndpointTypeInfer:
				r.bestInferEndpoint = newBestEndpoint
			}
			r.isBestRegionEndpointChanged = true
		}
	} else {
		// 运行时检查：如果当前最佳节点健康，就不重新选择；如果不健康，按rt延迟选择最低的
		if currentBestEndpoint != "" {
			if currentEndpoint, exists := endpointsMap[currentBestEndpoint]; exists && currentEndpoint.Healthy {
				// 当前最佳节点仍然健康，保持不变
				return
			}
		}

		// 当前最佳节点不健康或不存在，按rt延迟选择最低的Endpoint
		bestCandidateEndpoint := r.findLowestLatencyEndpoint(allHealthyEndpoints)
		if bestCandidateEndpoint == nil {
			return
		}

		newBestEndpoint := bestCandidateEndpoint.URL
		if newBestEndpoint != currentBestEndpoint {
			switch endpointType {
			case EndpointTypeCenter:
				r.bestCenterEndpoint = newBestEndpoint
			case EndpointTypeInfer:
				r.bestInferEndpoint = newBestEndpoint
			}
			r.isBestRegionEndpointChanged = true
		}
	}
}

// selectBestEndpointByLatency 选择最优端点（基于延迟）
// 与param comparedEndpoint 比较，如果comparedEndpoint不为空，且延迟rt差距小于30%，则保持原始值
func (r *RegionFailoverService) selectBestEndpointByLatency(endpoints []*EndpointStatus, comparedEndpoint *EndpointStatus) string {
	if len(endpoints) == 0 {
		return ""
	}

	if len(endpoints) == 1 {
		if comparedEndpoint == nil {
			return endpoints[0].URL
		}
		newEndpoint := endpoints[0]

		//新Endpoint延迟rt高于comparedEndpoint，则返回comparedEndpoint
		if newEndpoint.LastLatency > comparedEndpoint.LastLatency {
			return comparedEndpoint.URL
		}

		// 新Endpoint延迟rt与comparedEndpoint的rt差距小于30%，则返回comparedEndpoint
		percentageDiff := calculateLatencyDiff(newEndpoint.LastLatency, comparedEndpoint.LastLatency)
		if newEndpoint.URL == comparedEndpoint.URL || percentageDiff <= reselectLatencyThreshold {
			return comparedEndpoint.URL
		}

		return newEndpoint.URL
	}

	// 基于延迟选择最优端点
	var bestEndpoint *EndpointStatus
	bestLatency := int(^uint(0) >> 1) // 初始化为最大int值

	// 遍历所有端点，找到延迟最低的健康端点
	for _, endpoint := range endpoints {
		if endpoint.Healthy && endpoint.LastLatency > 0 && endpoint.LastLatency < invalidLatency {
			if endpoint.LastLatency < bestLatency {
				bestLatency = endpoint.LastLatency
				bestEndpoint = endpoint
			}
		}
	}

	// 如果没找到健康的端点，返回空
	if bestEndpoint == nil {
		return ""
	}

	// 如果comparedEndpoint不为空且健康，检查延迟差距
	if comparedEndpoint != nil && comparedEndpoint.Healthy &&
		comparedEndpoint.LastLatency > 0 && comparedEndpoint.LastLatency < invalidLatency {

		percentageDiff := calculateLatencyDiff(bestLatency, comparedEndpoint.LastLatency)
		// 如果差距小于30%，保持原始值（comparedEndpoint）
		if bestLatency > comparedEndpoint.LastLatency || percentageDiff <= reselectLatencyThreshold {
			return comparedEndpoint.URL
		}
	}

	return bestEndpoint.URL
}

// findLowestLatencyEndpoint 从健康端点中找到延迟最低的端点
func (r *RegionFailoverService) findLowestLatencyEndpoint(endpoints []*EndpointStatus) *EndpointStatus {
	if len(endpoints) == 0 {
		return nil
	}

	var bestEndpoint *EndpointStatus
	bestLatency := int(^uint(0) >> 1) // 初始化为最大int值

	// 遍历所有端点，找到延迟最低的健康端点
	for _, endpoint := range endpoints {
		if endpoint.Healthy && endpoint.LastLatency > 0 && endpoint.LastLatency < invalidLatency {
			if endpoint.LastLatency < bestLatency {
				bestLatency = endpoint.LastLatency
				bestEndpoint = endpoint
			}
		}
	}

	return bestEndpoint
}

func (r *RegionFailoverService) isNeedUpdatePreferredEndpoint(newBestEndpoint string, ep *config.EndpointConfig) bool {
	if ep == nil || ep.Endpoint == "" {
		return true
	}
	return newBestEndpoint != ep.Endpoint
}
