package remote

import (
	"net/url"
	"strings"
)

const (
	QoderEnvDaily  = "daily"
	QoderEnvTest   = "test"
	QoderEnvOnline = "online"
)

// 判断域名的环境
func getEndpointEnv(endpoint string) (string, error) {
	u, err := url.Parse(endpoint)
	if err != nil {
		return "", err
	}
	if strings.HasPrefix(u.Host, "test-") {
		return QoderEnvTest, nil
	} else if strings.HasPrefix(endpoint, "daily-") {
		return QoderEnvDaily, nil
	}
	return QoderEnvOnline, nil
}
