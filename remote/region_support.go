package remote

import (
	"bytes"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	_ "embed"
	"io"
	"math"
	"net/http"
	"time"
)

//go:embed data/ping_payload.txt
var pingPayload string

const (
	//不可用节点，耗时常量
	invalidLatency = 3600000

	//测试延迟时次数
	latencyTestCount = 10 // 测试次数
)

func FetchRemoteEndpointConfig() *RemoteRegionConfig {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("Failed to check endpoint: %v", err)
		}
	}()

	req, err := BuildBigModelSignGetRequest(definition.UrlPathQueryRegionEndpoints)
	if err != nil {
		log.Warnf("Failed to build request: %v", err)
		return nil
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send request: %v", err)
		return nil
	}
	if resp == nil || resp.Body == nil {
		log.Warnf("Failed to get response: resp is nil")
		return nil
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to check endpiont response: " + resp.Status)
		return nil
	}
	bodyBytes, _ := io.ReadAll(resp.Body)
	regionUrlConfig := RemoteRegionConfig{}
	err = util.UnmarshalToObject(string(bodyBytes), &regionUrlConfig)
	if err != nil {
		log.Errorf("Failed to unmarshal check endpoint response. body: %s, err: %v", string(bodyBytes), err)
		return nil
	}
	return &regionUrlConfig
}

// testLatency 测试单个URL的网络延迟
func testLatency(endpoint string) int {
	latencies := make([]int, 0, latencyTestCount)

	// 发送实际的ping请求来测量延迟
	url := BuildAlgoForRequestUrl(endpoint, definition.UrlPathPing)

	for i := 0; i < latencyTestCount; i++ {
		start := time.Now()

		req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer([]byte(pingPayload)))
		if err != nil {
			log.Warnf("Failed to create ping request for %s: %v", endpoint, err)
			return invalidLatency // 返回1小时的毫秒数，表示此端点不可用
		}

		httpClient := client.GetDefaultClient()
		httpClient.Timeout = 5 * time.Second // 设置超时时间

		resp, err := httpClient.Do(req)
		if err != nil {
			log.Warnf("Failed to ping endpoint %s: %v", endpoint, err)
			continue // 跳过本次测试，继续下一次
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Warnf("Endpoint %s returned status code %d", endpoint, resp.StatusCode)
			continue // 跳过本次测试，继续下一次
		}

		latencyDuration := time.Since(start)
		latencyMs := int(latencyDuration / time.Millisecond)
		latencies = append(latencies, latencyMs)

		time.Sleep(50 * time.Millisecond)
	}

	successCount := len(latencies)
	if successCount <= 0 {
		return invalidLatency
	}

	// 如果成功次数太少（少于3次），直接计算平均值
	if successCount < 3 {
		total := 0
		for _, latency := range latencies {
			total += latency
		}
		averageLatency := total / successCount
		log.Infof("Endpoint %s average latency over %d tests: %dms (insufficient data for outlier removal)", endpoint, successCount, averageLatency)
		return averageLatency
	}

	// 找出最高和最低延迟
	minLatency := latencies[0]
	maxLatency := latencies[0]
	minIndex := 0
	maxIndex := 0

	for i, latency := range latencies {
		if latency < minLatency {
			minLatency = latency
			minIndex = i
		}
		if latency > maxLatency {
			maxLatency = latency
			maxIndex = i
		}
	}

	// 计算去除最高和最低延迟后的平均值
	totalLatency := 0
	validCount := 0

	for i, latency := range latencies {
		// 跳过最高和最低延迟值（如果它们是不同的索引）
		if i == minIndex || i == maxIndex {
			continue
		}
		totalLatency += latency
		validCount++
	}

	// 如果去除最高最低后没有数据了，使用所有数据
	if validCount <= 0 {
		for _, latency := range latencies {
			totalLatency += latency
		}
		validCount = successCount
	}

	averageLatency := totalLatency / validCount
	log.Infof("Endpoint %s average latency over %d tests (removed min: %dms, max: %dms): %dms", endpoint, successCount, minLatency, maxLatency, averageLatency)

	return averageLatency
}

// routeEndpoint 路由端点（集成健康监测）
// routeType: auto/center/infer
func routeEndpoint(urlPath string, routeType string) (string, string) {
	// 如果健康监测服务不可用，使用默认端点
	if GlobalRegionFailService == nil {
		return config.Remote.BigModelEndpoint, ""
	}

	// 根据routeType进行路由
	switch routeType {
	case definition.RouteTypeCenter:
		return routeCenterEndpoint(), definition.RouteTypeCenter
	case definition.RouteTypeInfer:
		return routeInferenceEndpoint(), definition.RouteTypeInfer
	case definition.RouteTypeAuto:
		// 自动路由：根据URL类型进行路由
		urlType := classifyURL(urlPath)
		return routeByURLType(urlType, urlPath)
	default:
		// 默认使用自动路由
		urlType := classifyURL(urlPath)
		return routeByURLType(urlType, urlPath)
	}
}

func classifyURL(urlPath string) definition.URLType {
	// 首先尝试从APIPathRegistry中查找匹配的路径
	matchedPath := definition.GlobalAPIPathRegistry.GetMatchedPath(urlPath)
	if matchedPath != nil {
		return matchedPath.URLType
	}

	log.Warnf("not matched url pattern. urlPath: %s", urlPath)
	return definition.URLTypeUnknown
}

// routeByURLType 根据URL类型进行路由
func routeByURLType(urlType definition.URLType, url string) (string, string) {
	switch urlType {
	case definition.URLTypeCenter:
		return routeCenterEndpoint(), definition.RouteTypeCenter
	case definition.URLTypeDataCodebase:
		return routeDataEndpoint(definition.DataRegionTypeCodebase), definition.RouteTypeCodebase
	case definition.URLTypeDataRemoteAgent:
		return routeDataEndpoint(definition.DataRegionTypeRemoteAgent), definition.RouteTypeRemoteAgent
	case definition.URLTypeInference:
		return routeInferenceEndpoint(), definition.RouteTypeInfer
	default:
		// 未知类型，使用推理端点作为默认
		return routeInferenceEndpoint(), definition.RouteTypeUnknown
	}
}

// routeCenterEndpoint 路由中心端点
func routeCenterEndpoint() string {
	preferredEndpoint := config.GlobalRegionConfig.PreferredCenterNode
	if preferredEndpoint != nil && preferredEndpoint.Endpoint != "" {
		return preferredEndpoint.Endpoint
	}

	return config.Remote.BigModelEndpoint
}

// routeInferenceEndpoint 路由推理端点
func routeInferenceEndpoint() string {
	preferredEndpoint := config.GlobalRegionConfig.PreferredInferenceNode
	if preferredEndpoint != nil && preferredEndpoint.Endpoint != "" {
		return preferredEndpoint.Endpoint
	}

	return config.Remote.BigModelEndpoint
}

// routeDataEndpoint 路由数据端点
func routeDataEndpoint(dataRegionType string) string {
	return getRouteDataEndpoint("", dataRegionType)
}

func getRouteDataEndpoint(url string, dataNodeType string) string {
	if config.GlobalRegionConfig.PreferredDataNodeMap == nil || len(config.GlobalRegionConfig.PreferredDataNodeMap) <= 0 {
		//数据节点返回为空，避免路由错误
		return ""
	}
	return config.GlobalRegionConfig.PreferredDataNodeMap[dataNodeType].Endpoint
}

// 计算延迟差距百分比
func calculateLatencyDiff(latency1, latency2 int) float64 {
	if latency2 == 0 {
		return 0
	}
	latencyDiff := float64(latency1 - latency2)
	percentageDiff := math.Abs(latencyDiff / float64(latency2) * 100)
	return percentageDiff
}

func getAnyEndpoint(endpoints map[string]*EndpointStatus) *EndpointStatus {
	if endpoints == nil || len(endpoints) <= 0 {
		return nil
	}
	for _, endpoint := range endpoints {
		return endpoint
	}
	return nil
}

// 判断初始节点的环境，与prefer决策的ep是不同环境造成干扰
func isPreferredEndpointMismatch(ep *config.EndpointConfig) bool {
	if ep == nil || ep.Endpoint == "" {
		return true
	}
	initQoderEnv, err := getEndpointEnv(config.Remote.BigModelEndpoint)
	if err != nil {
		log.Warnf("check endpoint env error. endpoint: %s, err: %v", config.Remote.BigModelEndpoint, err)
		return true
	}
	preferredEndpointEnv, err := getEndpointEnv(ep.Endpoint)
	if err != nil {
		log.Warnf("check endpoint env error. endpoint: %s, err: %v", ep.Endpoint, err)
		return true
	}
	return initQoderEnv != preferredEndpointEnv
}
