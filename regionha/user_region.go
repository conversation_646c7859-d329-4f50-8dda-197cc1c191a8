package regionha

import (
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/spf13/viper"

	"cosy/client"
	"cosy/util"
)

// 常量定义
const (
	// 网络请求相关
	maxRetryAttempts     = 3
	requestTimeout       = 5 * time.Second
	sleepBetweenRequests = 500 * time.Millisecond

	// 延迟测试相关
	latencyTestCount = 10
	invalidLatency   = 3600000 // 1小时的毫秒数，表示端点不可用
)

// endpointLatency 端点延迟测试结果
type endpointLatency struct {
	url     string
	latency int
}

// CheckUpdateUserDataRegion 从服务端查询用户关联的数据节点信息并更新本地配置
func CheckUpdateUserDataRegion() {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		log.Debugf("User not logged in, skipping data node config update")
		return
	}

	if !global.IsQoderProduct() {
		log.Debugf("Not a Qoder product, skipping data node config update")
		return
	}

	userDataRegionConfig, err := fetchUserDataRegionConfig()
	if err != nil {
		log.Errorf("Failed to get remote user data node config: %v", err)
		return
	}

	endpointConfig := getMergedEndpointConfig()
	if endpointConfig == nil || endpointConfig.DataNodeMap == nil || len(endpointConfig.DataNodeMap) == 0 {
		log.Warnf("No remote endpoint config available")
		return
	}

	// 如果没有查询到绑定的数据节点路由，则选择最佳节点并更新
	if userDataRegionConfig == nil || userDataRegionConfig.IsEmpty() {
		userDataRegionConfig = selectAndUpdateBestDataRegion(endpointConfig)
		if userDataRegionConfig != nil {
			log.Infof("Selected new data node config: %s", util.ToJsonStr(userDataRegionConfig))
		}
	}

	// 更新本地配置
	updateLocalRegionConfig(userDataRegionConfig, endpointConfig)
	log.Infof("Updated local data node config: %s", util.ToJsonStr(config.GlobalRegionConfig))
}

// fetchUserDataRegionConfig 从远程获取用户数据节点配置（带重试机制）
func fetchUserDataRegionConfig() (*definition.UserDataRegion, error) {
	var lastErr error

	for attempt := 1; attempt <= maxRetryAttempts; attempt++ {
		userDataConfig, err := getUserDataNodeEndpoint()
		if err != nil {
			lastErr = err
			log.Debugf("Failed to fetch user data node config (attempt %d/%d): %v", attempt, maxRetryAttempts, err)
			if attempt < maxRetryAttempts {
				time.Sleep(sleepBetweenRequests)
				continue
			}
			return nil, fmt.Errorf("failed to fetch user data node config after %d attempts: %w", maxRetryAttempts, lastErr)
		}

		return userDataConfig, nil
	}

	return nil, lastErr
}

// pushUserDataRegionConfig 将用户数据节点配置推送到远程（带重试机制）
func pushUserDataRegionConfig(userDataRegion *definition.UserDataRegion) error {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return errors.New("user not logged in, cannot update data node config")
	}
	userDataRegion.UserId = userInfo.Uid

	var lastErr error

	for attempt := 1; attempt <= maxRetryAttempts; attempt++ {
		err := updateRemoteUserRegionConfig(userDataRegion)
		if err != nil {
			lastErr = err
			log.Debugf("Failed to push data node config (attempt %d/%d): %v", attempt, maxRetryAttempts, err)
			if attempt < maxRetryAttempts {
				time.Sleep(sleepBetweenRequests)
				continue
			}
			return fmt.Errorf("failed to push data node config after %d attempts: %w", maxRetryAttempts, lastErr)
		}

		return nil
	}

	return lastErr
}

// updateRemoteUserRegionConfig 调用远程API更新用户数据节点配置
func updateRemoteUserRegionConfig(dataNodeConfig *definition.UserDataRegion) error {
	url := fmt.Sprintf("%s?userId=%s", definition.UrlPathUserDataNodeEndpoint, dataNodeConfig.UserId)

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(dataNodeConfig),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, url, httpPayload)
	if err != nil {
		return fmt.Errorf("failed to build update request: %w", err)
	}

	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send update request: %w", err)
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("update request failed with status: %s, body: %s", resp.Status, string(bodyBytes))
	}

	var result definition.UpdateUserRegionResponse
	if err := util.UnmarshalToObject(string(bodyBytes), &result); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if !result.IsSuccess() {
		return fmt.Errorf("update data node config failed: %s", result.ErrorMessage)
	}
	if result.Result {
		log.Infof("Successfully updated remote data node config")
	}

	return nil
}

// getUserDataNodeEndpoint 从远程获取用户数据节点端点配置
func getUserDataNodeEndpoint() (*definition.UserDataRegion, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("Panic in getUserDataNodeEndpoint: %v", err)
		}
	}()

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return nil, errors.New("user not logged in")
	}

	url := fmt.Sprintf("%s?userId=%s", definition.UrlPathUserDataNodeEndpoint, userInfo.Uid)

	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to build get request: %w", err)
	}

	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send get request: %w", err)
	}

	if resp == nil || resp.Body == nil {
		return nil, errors.New("invalid response: resp or body is nil")
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get request failed with status: %s, body: %s", resp.Status, string(bodyBytes))
	}

	var dataEndpoint definition.UserDataRegion
	if err := util.UnmarshalToObject(string(bodyBytes), &dataEndpoint); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %s, error: %w", string(bodyBytes), err)
	}

	return &dataEndpoint, nil
}

// updateLocalRegionConfig 更新本地数据节点配置
func updateLocalRegionConfig(userDataRegionConfig *definition.UserDataRegion, endpointConfig *remote.RemoteRegionConfig) {
	if userDataRegionConfig == nil || userDataRegionConfig.IsEmpty() {
		log.Debugf("No user data config to update locally")
		return
	}

	preferredDataNodeMap := make(map[string]*config.EndpointConfig)

	if userDataRegionConfig.Repo != "" {
		selectEndpoint := selectMatchedEndpoint(userDataRegionConfig.Repo, endpointConfig.DataNodeMap[definition.DataRegionTypeCodebase])
		if selectEndpoint != "" {
			log.Infof("select matched endpoint for codebase. user region: %s, endpoint: %s", userDataRegionConfig.Repo, selectEndpoint)

			preferredDataNodeMap[definition.DataRegionTypeCodebase] = &config.EndpointConfig{
				Endpoint: selectEndpoint,
				Region:   userDataRegionConfig.Repo,
				Latency:  0,
			}
		}

	}

	if userDataRegionConfig.Ra != "" {
		selectEndpoint := selectMatchedEndpoint(userDataRegionConfig.Ra, endpointConfig.DataNodeMap[definition.DataRegionTypeRemoteAgent])
		if selectEndpoint != "" {
			log.Infof("select matched endpoint for remote agent. user region: %s, endpoint: %s", userDataRegionConfig.Ra, selectEndpoint)

			preferredDataNodeMap[definition.DataRegionTypeRemoteAgent] = &config.EndpointConfig{
				Endpoint: selectEndpoint,
				Region:   userDataRegionConfig.Ra,
				Latency:  0,
			}
		}

	}

	config.GlobalRegionConfig.PreferredDataNodeMap = preferredDataNodeMap

	viper.Set("region_config", config.GlobalRegionConfig)
	err := viper.WriteConfig()
	if err == nil {
		log.Debugf("Update global region config success. " + util.ToJsonStr(config.GlobalRegionConfig))
	} else {
		log.Errorf("Update global region config fail. error: %v", err)
	}
}
