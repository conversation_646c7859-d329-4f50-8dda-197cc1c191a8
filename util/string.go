package util

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"
)

func LongestCommonPrefixByToken(tokenList1 []string, tokenList2 []string) []string {
	len1 := len(tokenList1)
	len2 := len(tokenList2)
	if len1 == 0 || len2 == 0 {
		return nil
	}

	l := len1
	if len2 < len1 {
		l = len2
	}

	commonPrefix := []string{}
	for i := 0; i < l; i++ {
		if tokenList1[i] != tokenList2[i] {
			break
		}
		commonPrefix = tokenList1[:i+1]
	}

	return commonPrefix
}

func LongestCommonPrefix(str1 string, str2 string) string {
	len1 := len(str1)
	len2 := len(str2)
	if len1 == 0 || len2 == 0 {
		return ""
	}

	l := len1
	if len2 < len1 {
		l = len2
	}

	commonPrefix := ""
	for i := 0; i < l; i++ {
		if str1[:i+1] != str2[:i+1] {
			break
		}
		commonPrefix = str1[:i+1]
	}

	return commonPrefix
}

func LongestCommonSubsequence(text1 string, text2 string) int {
	h := len(text1) + 1
	w := len(text2) + 1
	m := make([][]int, h)
	for i := 0; i < h; i++ {
		m[i] = make([]int, w)
	}

	for i := 1; i < h; i++ {
		for j := 1; j < w; j++ {
			if text1[i-1] == text2[j-1] {
				m[i][j] = m[i-1][j-1] + 1
			} else {
				if m[i-1][j] > m[i][j-1] {
					m[i][j] = m[i-1][j]
				} else {
					m[i][j] = m[i][j-1]
				}
			}
		}
	}
	return m[h-1][w-1]
}

func Lcs(s1 string, s2 string) string {
	if len(s1) == 0 || len(s2) == 0 {
		return ""
	}
	a := make([][]int, len(s1))
	for i := 0; i < len(s1); i++ {
		a[i] = make([]int, len(s2))
		if []byte(s1)[i] == []byte(s2)[0] {
			a[i][0] = 1
		}
	}
	for j := 1; j < len(s2); j++ {
		if []byte(s1)[0] == []byte(s2)[j] {
			a[0][j] = 1
		}
	}
	max := 0
	ii := 0
	jj := 0
	for i := 1; i < len(s1); i++ {
		for j := 1; j < len(s2); j++ {
			if []byte(s1)[i] == []byte(s2)[j] {
				a[i][j] = a[i-1][j-1] + 1
			}
			if a[i][j] > max {
				max = a[i][j]
				ii = i
				jj = j
			}
		}
	}
	return string([]byte(s1)[ii+1-a[ii][jj] : ii+1])
}

func LcsLine(source string, target string) string {
	s1 := strings.Split(source, "\n")
	s2 := strings.Split(target, "\n")
	if len(s1) == 0 || len(s2) == 0 {
		return ""
	}
	a := make([][]int, len(s1))
	for i := 0; i < len(s1); i++ {
		a[i] = make([]int, len(s2))
		if strings.Trim(s1[i], "\r\n\t ") == strings.Trim(s2[0], "\r\n\t ") {
			a[i][0] = 1
		}
	}
	for j := 1; j < len(s2); j++ {
		if strings.Trim(s1[0], "\r\n\t ") == strings.Trim(s2[j], "\r\n\t ") {
			a[0][j] = 1
		}
	}
	max := 0
	ii := 0
	jj := 0
	for i := 1; i < len(s1); i++ {
		for j := 1; j < len(s2); j++ {
			if strings.Trim(s1[i], "\r\n\t ") == strings.Trim(s2[j], "\r\n\t ") {
				a[i][j] = a[i-1][j-1] + 1
			}
			if a[i][j] > max {
				max = a[i][j]
				ii = i
				jj = j
			}
		}
	}
	resultLines := s1[ii+1-a[ii][jj] : ii+1]
	return strings.Join(resultLines, "\n")
}

// TruncateString 查找最近chars字符，并从头截断
func TruncateString(content string, chars string) string {
	index := strings.IndexAny(content, chars)
	if index >= 0 {
		return content[0:index]
	}
	return content
}

// TruncateStringOrderChars 根据chats的顺序查找字符，从头截断
func TruncateStringOrderChars(content string, chars string, appendChars string) string {
	rs := []rune(chars)
	ars := []rune(appendChars)
	for i, c := range rs {
		index := strings.IndexRune(content, c)
		if index >= 0 {
			newStr := content[0:index]
			appendChar := ""
			if i < len(ars) {
				appendChar = string(ars[i])
			}
			return newStr + strings.TrimSpace(appendChar)
		}
	}
	return content
}

// ContainsString 判断字符串数组中是否包含指定字符串
func ContainsString(strings []string, target string) bool {
	for _, str := range strings {
		if target == str {
			return true
		}
	}
	return false
}

// ContainsEnglishOrChinese 判断字符串中是否包含英文或中文字符
func ContainsEnglishOrChinese(content string) bool {
	for _, c := range content {
		if unicode.Is(unicode.Han, c) || unicode.IsLetter(c) {
			return true
		}
	}
	return false
}

// AppendSymbolForEachLineHead 将给定的字符串分割成多行字符串，然后为每一行字符串添加指定的符号。
// 参数：
//
//	content：待分割的字符串
//	symbol：指定的符号
//
// 返回值：
//
//	拼接后的字符串
func AppendSymbolForEachLineHead(content string, symbol string) string {
	lines := strings.Split(content, "\n")
	for i := 0; i < len(lines); i++ {
		if strings.TrimSpace(lines[i]) == "" {
			continue
		}
		if len(lines[i]) > 0 {
			lines[i] = symbol + lines[i]
		}
	}
	return strings.Join(lines, "\n")
}

// GetLineHeadString 获取行首包含指定字符的字符串
func GetLineHeadString(line string, cutChars string) string {
	if len(line) == 0 {
		return ""
	}
	index := -1
	for i := 0; i < len(line); i++ {
		if !strings.ContainsAny(string(line[i]), cutChars) {
			break
		}
		index = i
	}
	return line[:index+1]
}

// GetModuleSuffixPath 获取python模块的后缀路径
func GetModuleSuffixPath(moduleFullName string) []string {
	result := []string{}
	args := strings.Split(moduleFullName, ".")
	if len(args) > 1 {
		for i := 0; i < len(args); i++ {
			result = append(result, strings.Join(args[i:], "."))
		}
	} else {
		result = append(result, moduleFullName)
	}
	return result
}

// GetReverseFilePath 将文件路径倒序
func GetReverseFilePath(filePath string) string {
	re := regexp.MustCompile("[\\\\/]+")
	paths := re.Split(filePath, -1)
	result := []string{}
	for i := len(paths) - 1; i >= 0; i-- {
		item := paths[i]
		if strings.HasSuffix(item, ":") {
			continue
		}
		result = append(result, item)
	}
	return strings.Join(result, "/")
}

func CommonPrefixSuffix(source, target string) (string, string, bool) {
	source = strings.Trim(source, " \n\r\t")
	target = strings.Trim(target, " \n\r\t")
	s1 := strings.Split(source, "\n")
	s2 := strings.Split(target, "\n")
	minLen := len(s1)
	if len(s2) < minLen {
		minLen = len(s2)
	}
	i := 0
	found := false
	for ; i < minLen; i++ {
		prefix := strings.Join(s2[:i+1], "\n")
		if strings.HasSuffix(source, prefix) {
			found = true
		} else if found {
			break
		}
	}
	if found {
		return strings.Join(s2[:i], "\n"), strings.Join(s2[i:], "\n"), found
	}
	return "", "", found
}

// GetCharAtCaret 获取光标所在字符
func GetCharAtCaret(code string, row uint32, column uint32) (rune, error) {
	if row > 0 && column > 0 {
		lines := strings.Split(code, "\n")
		currentLine := lines[row]
		lineChars := []rune(currentLine)
		return lineChars[column-1], nil
	}
	return 0, errors.New("row or column is invalid")
}

// RemoveCharAtCaret 删除光标所在字符
func RemoveCharAtCaret(code string, char rune, row uint32, column uint32) (string, bool) {
	if row > 0 && column > 0 {
		lines := strings.Split(code, "\n")
		newLines := make([]string, len(lines))
		newLines = append(newLines, lines[:row]...)
		currentLine := lines[row]
		lineChars := []rune(currentLine)
		if int(column)-1 > len(lineChars) {
			column = uint32(len(lineChars))
		}
		found := false
		if lineChars[column-1] == char {
			newLines = append(newLines, currentLine[:column-1]+" "+currentLine[column:])
			found = true
		} else {
			newLines = append(newLines, currentLine)
		}
		newLines = append(newLines, lines[row+1:]...)
		return strings.Join(newLines, "\n"), found
	}
	return code, false
}

// IsFirstRuneLowerCase 判断字符串的首字母是否是小写字母
func IsFirstRuneLowerCase(s string) bool {
	if s == "" {
		return false // 空字符串没有首字母
	}
	r := rune(s[0]) // 获取第一个字符的rune
	return unicode.IsLower(r)
}

// RemoveStringsDuplicates 函数移除给定字符串切片中的重复元素。
//
// 参数:
//
//	strings []string - 包含可能重复元素的字符串切片。
//
// 返回值:
//
//	[]string - 移除重复元素后的字符串切片
func RemoveStringsDuplicates(strings []string) []string {
	unique := make(map[string]bool)
	result := []string{}
	for _, str := range strings {
		if _, ok := unique[str]; !ok {
			unique[str] = true
			result = append(result, str)
		}
	}
	return result
}

// TrimLeadingSpaces 函数接收一个字符串作为参数，移除每一行开头的空格和制表符
func TrimLeadingSpaces(s string) string {
	lines := strings.Split(s, "\n")
	var trimmedLines []string

	for _, line := range lines {
		trimmedLine := strings.TrimLeft(line, " \t")
		trimmedLines = append(trimmedLines, trimmedLine)
	}

	return strings.Join(trimmedLines, "\n")
}

// Indent 函数接收一个字符串、一个缩进符和一个缩进次数作为参数，为字符串的每一行添加指定数量的缩进
func Indent(s string, indentChar string, numIndents int) string {
	lines := strings.Split(s, "\n")
	var indentedLines []string

	for _, line := range lines {
		if strings.Trim(line, " \t") != "" { // 跳过空行
			indentedLine := strings.Repeat(indentChar, numIndents) + line
			indentedLines = append(indentedLines, indentedLine)
		} else {
			indentedLines = append(indentedLines, line) // 保留空行
		}
	}

	return strings.Join(indentedLines, "\n")
}

// ToCamelCase 将字符串数组转换为驼峰形式的字符串，每个单词首字母大写
func ToCamelCase(parts []string) string {
	var result strings.Builder
	for _, part := range parts {
		chars := []rune(part)
		if len(chars) == 0 {
			continue
		}
		chars[0] = unicode.ToUpper(chars[0])
		result.WriteString(string(chars))
	}
	return result.String()
}

// RemoveMarkdownCodeWrapper remove the first and last line of ``` from string
func RemoveMarkdownCodeWrapper(str string) string {
	str = strings.TrimSpace(str)

	if strings.HasPrefix(str, "```") && strings.HasSuffix(str, "```") {
		lines := strings.Split(str, "\n")
		if len(lines) >= 2 {
			lines = lines[1 : len(lines)-1]
		}
		str = strings.Join(lines, "\n")
	}

	return str
}

// ExtractCodeBlocks 提取 Markdown 文本中所有代码块的内容。
// 如果 language 不为空，则只提取指定语言的代码块。
func ExtractCodeBlocks(markdown string, language string) (string, error) {
	var pattern string
	if language != "" {
		// 匹配指定语言的代码块，如 ```java
		pattern = fmt.Sprintf("(?s)```%s\\s*(.*?)\\s*```", regexp.QuoteMeta(language))
	} else {
		// 匹配所有代码块
		pattern = "(?s)``(?:\\w+)?\\s*(.*?)\\s*```"
	}

	re, err := regexp.Compile(pattern)
	if err != nil {
		return "", err
	}

	matches := re.FindAllStringSubmatch(markdown, -1)
	var codeBlocks []string
	for _, match := range matches {
		if len(match) > 1 {
			codeBlocks = append(codeBlocks, match[1])
		}
	}
	combinedCode := strings.Join(codeBlocks, "\n\n")
	return combinedCode, nil
}

// GetFirstAndLastLine 获取字符串的第一行和最后一行，如果字符串为空，则返回空字符串，如果字符串只有一行，则返回该行
func GetFirstAndLastLine(s string) (string, string) {
	lines := strings.Split(s, "\n")
	if len(lines) == 0 {
		return "", ""
	}
	if len(lines) == 1 {
		return lines[0], ""
	}

	return lines[0], lines[len(lines)-1]
}

// GetAroundLineByOffset 根据偏移量获取指定行以及上下各margin行的字符串
// @param code 代码字符串
// @param offset 字符偏移量。offset是rune级别的偏移量
// @param margin 上下各margin行的数量
// @return 返回指定行以及上下各margin行的字符串
func GetAroundLineByOffset(code string, offset int, margin int) (string, error) {
	if offset < 0 {
		return "", errors.New("offset is invalid")
	}

	lines := strings.Split(code, "\n")
	totalOffset := 0
	lineIndex := -1
	for i, line := range lines {
		lineLength := utf8.RuneCountInString(line)
		if totalOffset+lineLength >= offset {
			lineIndex = i
			break
		}
		totalOffset += lineLength + 1
	}
	if lineIndex == -1 {
		return "", errors.New("offset is invalid")
	}
	result := make([]string, 0, 1+margin*2)
	fromLine := lineIndex - margin
	if fromLine < 0 {
		fromLine = 0
	}
	endLine := lineIndex + margin
	if endLine >= len(lines) {
		endLine = len(lines) - 1
	}
	for i := fromLine; i <= endLine; i++ {
		result = append(result, lines[i])
	}

	return strings.Join(result, "\n"), nil
}

// GetLineByOffset 根据偏移量获取指定行的字符串
func GetLineByOffset(code string, offset int, lineOffset int) (string, error) {
	if offset < 0 {
		return "", errors.New("offset is invalid")
	}

	lines := strings.Split(code, "\n")
	totalOffset := 0
	lineIndex := -1
	for i, line := range lines {
		lineLength := utf8.RuneCountInString(line)
		if totalOffset+lineLength >= offset {
			lineIndex = i
			break
		}
		totalOffset += lineLength + 1
	}
	if lineIndex == -1 {
		return "", errors.New("offset is invalid")
	}
	if lineIndex+lineOffset < 0 || lineIndex+lineOffset >= len(lines) {
		return "", nil
	}
	return lines[lineIndex+lineOffset], nil
}

// RemoveLineByOffset 根据偏移量删除指定行
func RemoveLineByOffset(code string, offset int) (string, error) {
	if offset < 0 {
		return "", errors.New("offset is invalid")
	}

	lines := strings.Split(code, "\n")
	totalOffset := 0
	lineIndex := -1
	for i, line := range lines {
		lineLength := utf8.RuneCountInString(line)
		if totalOffset+lineLength >= offset {
			lineIndex = i
			break
		}
		totalOffset += lineLength + 1
	}
	if lineIndex == -1 {
		return "", errors.New("offset is invalid")
	}
	lines = append(lines[:lineIndex], lines[lineIndex+1:]...)
	return strings.Join(lines, "\n"), nil
}

// TruncateStringByRuneCount 截取字符串到指定的Unicode字符数量
func TruncateStringByRuneCount(s string, maxRuneCount int) (string, error) {
	// 计算字符串中的字符数量
	runeCount := utf8.RuneCountInString(s)
	// 如果字符数小于或等于最大字符数，则直接返回原字符串
	if runeCount <= maxRuneCount {
		return s, nil
	}

	// 创建一个切片来存储截取后的字符
	var truncated []rune
	curRuneCount := 0
	for _, r := range s {
		curRuneCount++
		if curRuneCount > maxRuneCount {
			break
		}
		// 将当前字符添加到切片
		truncated = append(truncated, r)
	}
	// 将 rune 切片转换回字符串
	return string(truncated), nil
}
