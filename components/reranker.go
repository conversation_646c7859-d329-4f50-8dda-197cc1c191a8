package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"time"
)

const (
	DefaultTopN             = 80
	defaultReturnDocuments  = true
	DefaultMaxThreshold     = 0.2
	GoogleRerankModelV4     = "semantic-ranker-default-004"
	GoogleRerankModelFastV4 = "semantic-ranker-fast-004"
	GoogleRerankModelV3     = "semantic-ranker-default-003"
	GoogleRerankModelV2     = "semantic-ranker-default-002"
	RerankModelV1           = "gte-rerank"
	RerankModelV2           = "gte-rerank-v2"

	// 批次处理常量
	DefaultMaxBatchSize = 300
)

// RerankRequest 是主请求结构体
type RerankRequest struct {
	Model      string                  `json:"model"`
	Input      RerankRequestInput      `json:"input"`
	Parameters RerankRequestParameters `json:"parameters"`
}

// RerankRequestInput 是输入部分的结构体
type RerankRequestInput struct {
	Query     string   `json:"query"`     // rerank凭据，query
	Documents []string `json:"documents"` // rerank的文档
}

// RerankRequestParameters 是参数部分的结构体
type RerankRequestParameters struct {
	ReturnDocuments bool `json:"return_documents"` // 是否返回，默认需要返回
	TopN            int  `json:"top_n"`            // 返回top_n个数据
}

// RerankResponse 是主结果结构体
type RerankResponse struct {
	StatusCode int    `json:"status_code"` // 状态码，200为正常
	Code       string `json:"code"`        // 模型出错时，返回的模型错误码
	Message    string `json:"message"`     // 模型出错时的错误消息

	Output    RerankResponseOutput `json:"output"`     // 排序的结果
	Usage     RerankResponseUsage  `json:"usage"`      // 消耗token数
	RequestID string               `json:"request_id"` // 请求id
}

type RerankResponseOutput struct {
	Results []RerankResponseResult `json:"results"`
}

type RerankResponseResult struct {
	Document       RerankResponseDocument `json:"document"`
	Index          int                    `json:"index"`
	RelevanceScore float64                `json:"relevance_score"`
}

type RerankResponseDocument struct {
	Text string `json:"text"`
}

type RerankResponseUsage struct {
	TotalTokens int `json:"total_tokens"`
}

// BatchRerankResult 批次重排序结果
type BatchRerankResult struct {
	Results []RerankResponseResult
	Error   error
}

type LingmaReranker struct {
	HttpClient      *http.Client
	ReturnDocuments bool
	MaxBatchSize    int // 每批次的最大文档数
}

func NewLingmaReranker() *LingmaReranker {
	return &LingmaReranker{
		HttpClient:      client.GetDocRerankClient(),
		ReturnDocuments: defaultReturnDocuments,
		MaxBatchSize:    DefaultMaxBatchSize,
	}
}

// SetBatchSize 设置批次大小
func (reranker *LingmaReranker) SetBatchSize(maxBatchSize int) {
	reranker.MaxBatchSize = maxBatchSize
}

// splitIntoBatches 将文档分批
func (reranker *LingmaReranker) splitIntoBatches(documents []string) [][]string {
	var batches [][]string

	for i := 0; i < len(documents); i += reranker.MaxBatchSize {
		end := i + reranker.MaxBatchSize
		if end > len(documents) {
			end = len(documents)
		}
		batches = append(batches, documents[i:end])
	}

	return batches
}

// processMultipleBatches 处理多个批次
func (reranker *LingmaReranker) processMultipleBatches(ctx context.Context, query string, documents []string, limit int) (*RerankResponse, error) {
	batches := reranker.splitIntoBatches(documents)
	log.Infof("processing %d batches with total %d documents", len(batches), len(documents))

	var allResults []RerankResponseResult
	var batchOffset int

	// 顺序处理所有批次
	for batchIndex, batch := range batches {
		log.Debugf("processing batch %d with %d documents", batchIndex, len(batch))

		result, err := reranker.processSingleBatch(ctx, query, batch, limit, batchOffset)
		if err != nil {
			log.Errorf("batch %d failed: %v", batchIndex, err)
			// 单个批次失败不中断整个流程，继续处理下一批次
			batchOffset += len(batch)
			continue
		}

		allResults = append(allResults, result.Output.Results...)
		batchOffset += len(batch)
	}

	if len(allResults) == 0 {
		return nil, fmt.Errorf("all batches failed")
	}

	// 按分数降序排序所有结果
	sort.Slice(allResults, func(i, j int) bool {
		return allResults[i].RelevanceScore > allResults[j].RelevanceScore
	})

	// 限制返回结果数量
	if limit > 0 && len(allResults) > limit {
		allResults = allResults[:limit]
	}

	log.Infof("merged %d results from multiple batches, returning top %d",
		len(allResults), len(allResults))

	return &RerankResponse{
		StatusCode: http.StatusOK,
		Output:     RerankResponseOutput{Results: allResults},
		Usage:      RerankResponseUsage{TotalTokens: 0},
	}, nil
}

// processSingleBatch 处理单个批次
func (reranker *LingmaReranker) processSingleBatch(ctx context.Context, query string, documents []string, limit int, indexOffset int) (*RerankResponse, error) {
	if len(documents) == 0 {
		log.Infof("documents is empty, query: `%s`", query)
		return &RerankResponse{
			StatusCode: http.StatusOK,
			Output:     RerankResponseOutput{Results: []RerankResponseResult{}},
		}, nil
	}

	requestBody := RerankRequest{
		Model: GoogleRerankModelV4,
		Input: RerankRequestInput{
			Query:     query,
			Documents: documents,
		},
		Parameters: RerankRequestParameters{
			ReturnDocuments: reranker.ReturnDocuments,
			TopN:            limit,
		},
	}
	var result RerankResponse
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(requestBody),
		EncodeVersion: config.Remote.MessageEncode,
	}
	rerankRequest, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.UrlPathCodebaseRerank, httpPayload)
	if err != nil {
		return nil, err
	}

	log.Infof("send rerank request. query: `%s`, documents count: %d", query, len(documents))

	startTime := time.Now()

	httpClient := reranker.HttpClient
	resp, err := httpClient.Do(rerankRequest)
	if err != nil {
		return nil, err
	}
	endTime := time.Now()
	log.Infof("rerank response time is %fs", endTime.Sub(startTime).Seconds())
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		return nil, err
	}

	if result.StatusCode != http.StatusOK {
		err := fmt.Errorf("rerank return failed, status code: %s, message: %s", result.Code, result.Message)
		return nil, err
	} else {
		log.Infof("rerank return success, total token count: %d", result.Usage.TotalTokens)
	}

	// 调整索引以反映在原始文档列表中的位置
	for i := range result.Output.Results {
		result.Output.Results[i].Index += indexOffset
	}

	return &result, nil
}

func (reranker *LingmaReranker) RerankDocuments(ctx context.Context, query string, documents []string, limit int) (*RerankResponse, error) {
	if len(documents) == 0 {
		log.Infof("rerank documents is empty, query: `%s`", query)
		return &RerankResponse{
			StatusCode: http.StatusOK,
			Output:     RerankResponseOutput{Results: []RerankResponseResult{}},
		}, nil
	}

	// 检查是否需要分批处理
	if len(documents) <= reranker.MaxBatchSize {
		// 单批处理
		return reranker.processSingleBatch(ctx, query, documents, limit, 0)
	} else {
		// 分批处理
		return reranker.processMultipleBatches(ctx, query, documents, limit)
	}
}
