package experiment

import (
	"cosy/definition"
	"cosy/definition/environment"
	"cosy/log"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"sort"
	"strconv"
	"strings"
)

const (
	// ConfigScopeCommon 所有端共享特性
	ConfigScopeCommon = "common"

	// ConfigScopeVscode vscode端专享特性
	ConfigScopeVscode = "vscode"

	// ConfigScopeJetbrains jetbrains端专享特性
	ConfigScopeJetbrains = "jetbrains"

	ConfigScopeQoder = "qoder"

	// ConfigScopeClient LingmaGo端专享特性
	ConfigScopeClient = "client"
)

var ConfigService = &ExperimentalCenter{}

type ConfigItem struct {
	//范围，common/client/vscode/jebrains
	Scope string
	//配置key
	Key string
	//配置value
	Value string
}

type ExperimentalCenter struct {
	//实验项
	configs map[string]map[string]ConfigItem
	//内容md5
	md5 string
}

func (ec *ExperimentalCenter) GetDoubleConfigValue(key string, configScope string, defValue float64) float64 {
	configs, ok := ec.configs[configScope]
	if !ok {
		return defValue
	}
	configItem, ok := configs[key]
	if !ok {
		return defValue
	}
	value, err := strconv.ParseFloat(configItem.Value, 64)
	if err != nil {
		log.Warnf("illegal config key: %s, value: %s", configItem.Key, configItem.Value)
		return defValue
	}
	return value
}

func (ec *ExperimentalCenter) GetIntConfigValue(key string, configScope string, defValue int) int {
	configs, ok := ec.configs[configScope]
	if !ok {
		return defValue
	}
	configItem, ok := configs[key]
	if !ok {
		return defValue
	}
	value, err := strconv.ParseInt(configItem.Value, 10, 64)
	if err != nil {
		log.Warnf("illegal config key: %s, val: %s", configItem.Key, configItem.Value)
		return defValue
	}
	return int(value)
}

func (ec *ExperimentalCenter) GetBoolConfigValue(key string, configScope string, defValue bool) bool {
	configs, ok := ec.configs[configScope]
	if !ok {
		return defValue
	}
	configItem, ok := configs[key]
	if !ok {
		return defValue
	}
	value, err := strconv.ParseBool(configItem.Value)
	if err != nil {
		log.Warnf("illegal config key: %s, val: %s", configItem.Key, configItem.Value)
		return defValue
	}
	return value
}

func (ec *ExperimentalCenter) GetStringConfigValue(key string, configScope string, defValue string) string {
	configs, ok := ec.configs[configScope]
	if !ok {
		return defValue
	}
	configItem, ok := configs[key]
	if !ok {
		return defValue
	}
	return configItem.Value
}

// key-value 结构
// "vscode.completion.auto.delay": "500",
// "xxxxx.feature1": "xxxx"
func (ec *ExperimentalCenter) UpdateAll(experimentConfigs map[string]string) bool {
	newMd5 := md5Config(experimentConfigs)
	if ec.md5 != "" && ec.md5 == newMd5 {
		log.Debugf("abFeatures is same, no need update.")
		return false
	}
	ec.md5 = newMd5
	//清空现有实验配置
	ec.configs = make(map[string]map[string]ConfigItem, 0)
	for key, val := range experimentConfigs {
		item, err := parseExperimentItem(key, val)
		if err != nil {
			continue
		}
		configsInScope := ec.configs[item.Scope]
		if configsInScope == nil {
			configsInScope = make(map[string]ConfigItem, 0)
		}
		configsInScope[item.Key] = item

		ec.configs[item.Scope] = configsInScope
	}
	return true
}

func md5Config(experimentConfigs map[string]string) string {
	h := md5.New()
	var contentSlice []string
	for key, val := range experimentConfigs {
		contentSlice = append(contentSlice, key+"="+val)
	}
	//排序
	sort.Strings(contentSlice)
	orderedContent := strings.Join(contentSlice, "")
	h.Write([]byte(orderedContent))
	return hex.EncodeToString(h.Sum(nil))
}

func (ec *ExperimentalCenter) GetExperimentConfigsByIdeConfig(ideConfig definition.IdeConfig) map[string]string {
	if ideConfig.IdeSeries == "" {
		return nil
	}
	lowIdeSeries := strings.ToLower(ideConfig.IdeSeries)
	if strings.Contains(lowIdeSeries, "jetbrains") {
		return ec.GetExperimentConfigs(ConfigScopeJetbrains)
	} else if strings.Contains(lowIdeSeries, ConfigScopeVscode) {
		return ec.GetExperimentConfigs(ConfigScopeVscode)
	} else if strings.Contains(lowIdeSeries, ConfigScopeQoder) {
		return ec.GetExperimentConfigs(ConfigScopeQoder)
	}
	return nil
}

func GetExperimentTypeByIdeConfig(ideConfig definition.IdeConfig) (string, error) {
	if ideConfig.IdeSeries == "" {
		return "", errors.New("IdeSeries is null. ")
	}
	lowIdeSeries := strings.ToLower(ideConfig.IdeSeries)
	if strings.Contains(lowIdeSeries, "jetbrains") {
		return ConfigScopeJetbrains, nil
	} else if strings.Contains(lowIdeSeries, ConfigScopeVscode) {
		return ConfigScopeVscode, nil
	} else if strings.Contains(lowIdeSeries, ConfigScopeQoder) {
		return ConfigScopeQoder, nil
	}
	return "", errors.New("unknown IdeSeries. " + ideConfig.IdeSeries)
}

// GetExperimentConfigs 根据端查询配置
func (ec *ExperimentalCenter) GetExperimentConfigs(configScope string) map[string]string {
	vscodeConfigs := ec.configs[ConfigScopeVscode]
	jetbrainConfigs := ec.configs[ConfigScopeJetbrains]
	qoderConfigs := ec.configs[ConfigScopeQoder]
	commonConfigs := ec.configs[ConfigScopeCommon]
	clientConfigs := ec.configs[ConfigScopeClient]

	var filteredItems map[string]ConfigItem
	switch configScope {
	case ConfigScopeVscode:
		{
			filteredItems = ec.mergeConfigs(commonConfigs, vscodeConfigs)
		}
	case ConfigScopeJetbrains:
		{
			filteredItems = ec.mergeConfigs(commonConfigs, jetbrainConfigs)
		}
	case ConfigScopeQoder:
		{
			filteredItems = ec.mergeConfigs(commonConfigs, qoderConfigs)
		}
	case ConfigScopeClient:
		{
			filteredItems = clientConfigs
		}
	case ConfigScopeCommon:
		{
			filteredItems = commonConfigs
		}
	}
	resultItems := make(map[string]string, len(filteredItems))
	for _, item := range filteredItems {
		resultItems[item.Key] = item.Value
	}
	return resultItems
}

// 客户端接收到特性后进行解析及分发，合并各特性信息，将common合并到各端，
// 如果名称重复，端侧feature覆盖common的feature
func (ec *ExperimentalCenter) mergeConfigs(lowPriorityItems, highPriorityItems map[string]ConfigItem) map[string]ConfigItem {
	if len(lowPriorityItems) <= 0 {
		return highPriorityItems
	}
	//根据优先级合并slice
	mergedConfigMap := make(map[string]ConfigItem, 16)
	for _, item := range lowPriorityItems {
		mergedConfigMap[item.Key] = item
	}
	for _, item := range highPriorityItems {
		mergedConfigMap[item.Key] = item
	}
	return mergedConfigMap
}

func parseExperimentItem(key, value string) (ConfigItem, error) {
	typeIndex := strings.Index(key, ".")
	if typeIndex < 0 {
		return ConfigItem{}, errors.New("illegal key. key=" + key)
	}
	configScope := key[0:typeIndex]
	configKey := key[typeIndex+1:]
	if configKey == "" {
		return ConfigItem{}, errors.New("key is nil")
	}
	return ConfigItem{
		Scope: configScope,
		Key:   configKey,
		Value: value,
	}, nil
}

// GetDoubleConfigWithEnv 从环境变量中获取配置，如果没有则返回实验特性配置
func (ec *ExperimentalCenter) GetDoubleConfigWithEnv(key string, scope string, defValue float64) float64 {
	if value, ok := environment.GetDoubleFromEnv(key, scope); ok {
		return value
	}
	return ec.GetDoubleConfigValue(key, scope, defValue)
}

// GetIntConfigWithEnv 从环境变量中获取配置，如果没有则返回实验特性配置
func (ec *ExperimentalCenter) GetIntConfigWithEnv(key string, scope string, defValue int) int {
	if value, ok := environment.GetIntFromEnv(key, scope); ok {
		return value
	}
	return ec.GetIntConfigValue(key, scope, defValue)
}

// GetBoolConfigWithEnv 从环境变量中获取配置，如果没有则返回实验特性配置
func (ec *ExperimentalCenter) GetBoolConfigWithEnv(key string, scope string, defValue bool) bool {
	if value, ok := environment.GetBoolFromEnv(key, scope); ok {
		return value
	}
	return ec.GetBoolConfigValue(key, scope, defValue)
}

// GetStringConfigWithEnv 从环境变量中获取配置，如果没有则返回实验特性配置
func (ec *ExperimentalCenter) GetStringConfigWithEnv(key string, scope string, defValue string) string {
	if value, ok := environment.GetStringFromEnv(key, scope); ok {
		return value
	}
	return ec.GetStringConfigValue(key, scope, defValue)
}

// GetStringMapConfigWithEnv 从环境变量中获取配置，如果没有则返回实验特性配置
func (ec *ExperimentalCenter) GetStringMapConfigWithEnv(key string, scope string, defValue map[string]string) map[string]string {
	value := ec.GetStringConfigWithEnv(key, scope, "")
	if value == "" {
		return defValue
	}
	result := make(map[string]string)
	for _, item := range strings.Split(value, ",") {
		parts := strings.Split(item, ":")
		if len(parts) == 2 {
			result[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
		}
	}
	return result
}
