package server

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"time"

	"cosy/auth"
	"cosy/chat"
	"cosy/chat/ability"
	"cosy/chat/agents/coder"
	"cosy/chat/agents/support"
	"cosy/chat/agents/unittest"
	"cosy/chat/agents/unittest/agent"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/deepwiki"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/extension"
	"cosy/feedback"
	"cosy/global"
	"cosy/ide/common"
	"cosy/indexing"
	"cosy/log"
	"cosy/longruntask"
	"cosy/memory/experience"
	"cosy/model/remote_model"
	"cosy/monitor"
	"cosy/search"
	"cosy/server/handlers"
	"cosy/sls"
	"cosy/stable"
	"cosy/update"
	"cosy/user"
	"cosy/util"
	"cosy/util/uri"
	"cosy/websocket"

	"github.com/google/uuid"
)

// MockResult 用于接口调试的返回值缓存
var MockResult interface{}

// Deliver delivers incoming request to corresponding cosyServer
func (cs *CosyServer) Deliver(ctx context.Context, req *websocket.WireRequest) (err error) {
	ctx = prefillContext(ctx, cs)
	curCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	// After the request is completed, close current ctx and all children goroutine
	defer cancel()
	// 上报请求监控数据
	apiRequestMonitor := monitor.NewApiRequestMonitor(req.Method, curCtx, req.Params)
	defer func() {
		// 当产生panic错误上报日志
		if r := recover(); r != nil {
			if req.Params == nil {
				return
			}
			params := map[string]any{}
			json.Unmarshal(*req.Params, &params)

			stack := debug.Stack()
			apiRequestMonitor.ReportPanicError(stack)
		} else {
			if err != nil {
				apiRequestMonitor.SetFailedWithError(err)
			}
			apiRequestMonitor.Report()
		}
	}()

	if !IsIgnoreMethodLog(req.Method) {
		simpleLogMethodEnter(ctx, cs, req.Method)
	}

	switch req.Method {
	case "exit": // notification
		log.Info("Lingma exit gracefully.")

		cs.Exit(ctx)
		os.Exit(0)
	case "ping":
		if replyErr := cs.Reply(curCtx, req, definition.PingResult{Success: true}, nil); replyErr != nil {
			log.Warn("Failed to reply ping: ", replyErr)
		}
	case "initialize":
		var params definition.InitializeParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Warnf("Failed to parse input params for initialize. params: %v", string(*req.Params))
			return err
		}
		log.Infof("initialize client, params=%+v", params)
		res, err := cs.Initialize(ctx, &params)
		if err != nil {
			log.Warnf("Failed to initialize %s: %s", params.IdePlatform, err.Error())
		}

		defer func() {
			// 获取服务端的数据协议签署，并更新到本地
			// 异步，避免阻塞启动进程
			// 必须放在同端侧初始化通信成功后，否则无法弹窗
			go auth.InitDataPolicySignStatus()
		}()
		if replyErr := cs.Reply(curCtx, req, res, err); replyErr != nil {
			log.Warn("Reply err: ", replyErr)
		}
	case "workspace/close":
		var params definition.WorkspaceCloseParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Warnf("Failed to parse input params for workspace/close. params: %v", string(*req.Params))
			return err
		}
		if params.IsEmptyWorkspace() {
			log.Debugf("ignore workspace close, is empty.")

			closeResult := &definition.WorkspaceCloseResult{
				Success: true,
				Message: "Ignore Empty workspace Close",
			}
			if replyErr := cs.Reply(curCtx, req, closeResult, err); replyErr != nil {
				log.Warn("Failed to reply workspace close: ", replyErr)
			}
			return nil
		}
		log.Debugf("workspace close request, params=%+v", params)
		res, err := cs.WorkspaceClose(ctx, &params)
		if err != nil {
			log.Warnf("Failed to close workspace %v: %s", params.WorkspaceFolders, err.Error())
		}
		if replyErr := cs.Reply(curCtx, req, res, err); replyErr != nil {
			log.Warn("Failed to reply workspace close: ", replyErr)
		}
	case "textDocument/completion":
		var params definition.CompletionParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor(string(params.TextDocument.URI))
		if params.RequestId == "" {
			params.RequestId = uuid.New().String()
		}
		// Insert workspace info
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			params.WorkspaceInfo = workspace
		}

		res := processor.Complete(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, res, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
		//reqStr := util.ToJsonStr(params)
		//rspStr := util.ToJsonStr(res)
		//log.Debugf("completion details. requestId: %s, params: %s, result: %s", params.RequestId, reqStr, rspStr)
	case "textDocument/inlineEdit":
		var params definition.InlineEditParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor(string(params.TextDocument.URI))
		if params.RequestId == "" {
			params.RequestId = uuid.New().String()
		}

		res := processor.InlineEdit(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, res, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "textDocument/preCompletion":
		var params definition.PreCompletionParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor(string(params.TextDocument.URI))
		if params.RequestId == "" {
			params.RequestId = uuid.New().String()
		}

		log.Debug("["+params.RequestId+"] ", fmt.Sprintf("pre completion file(%s): %s@%d:%d", params.TriggerMode, params.TextDocument.URI, uint64(params.Position.Line), uint64(params.Position.Character)))

		// Add IDE type
		if ide, err := cs.getIdeSeries(ctx); err == nil {
			curCtx = context.WithValue(curCtx, "ide", ide)
		}

		if langIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			curCtx = context.WithValue(curCtx, definition.ContextKeyFileIndexer, langIndexer)
		}

		if err := processor.PreComplete(curCtx, &params); err != nil {
			log.Error("["+params.RequestId+"] ", "pre complete failed: ", err)
		}
		// No need to reply
	case "statistics/general": // 通用埋点
		var params definition.GeneralStatisticsParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		if params.EventData == nil {
			params.EventData = make(map[string]string)
		}
		params.EventData["ide_type"] = params.IdeType
		params.EventData["ide_version"] = params.IdeVersion
		// Add IDE type
		var ideSeries string
		var pluginVersion string
		var err error
		if ideSeries, err = cs.getIdeSeries(ctx); err == nil {
			params.EventData["ide_series"] = ideSeries
		}
		if pluginVersion, err = cs.getPluginVersion(ctx); err == nil {
			params.EventData["plugin_version"] = pluginVersion
		}
		if params.EventType == "code-inline-completion-trigger" {
			sls.ReportTriggerMap.Store(params.RequestId, definition.ReportTriggerInfo{
				RecordTime: time.Now(),
				IdeType:    params.IdeType,
				IdeVersion: params.IdeVersion,
				IdeSeries:  ideSeries,
			})
		} else if params.EventType == "chat-free-input" {
			if data, ok := params.EventData["features"]; ok {
				var features []definition.ChatAskFeature
				err = json.Unmarshal([]byte(data), &features)
				if err == nil {
					for _, feature := range features {
						if feature.Id == "TEAM_DOCS" ||
							feature.Id == "WORKSPACE" {
							sls.ReportTriggerMap.Store(params.RequestId, definition.ReportTriggerInfo{
								RecordTime: time.Now(),
								IdeType:    params.IdeType,
								IdeVersion: params.IdeVersion,
								IdeSeries:  ideSeries,
							})
						}
					}
				}
			}
		}
		sls.Report(params.EventType, params.RequestId, params.EventData)
	case "statistics/compute": // notification
		var params definition.ComputeStatisticsParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor(string(params.TextDocumentURI))
		if err := processor.ComputeStatistics(&params); err != nil {
			log.Info("Failed to compute statistics:", err)
		}
	case "textDocument/didClose":
		var params definition.DidCloseTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Debug("did close uri:", params.TextDocument.URI)
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			filePath := uri.URIToPath(string(params.TextDocument.URI))
			fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.CloseOperation, false))
			go fileIndexer.RemoveFileQueue(filePath)
		}
	case "textDocument/didOpen":
		var params definition.DidOpenTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Info("did open uri:", params.TextDocument.URI)
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			filePath := uri.URIToPath(string(params.TextDocument.URI))
			fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.OpenOperation, false))
			go fileIndexer.AddFileQueue(filePath, params.TextDocument.Text)
		}
	case "workspace/didCreateFiles":
		var params definition.CreateFilesParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			for _, file := range params.Files {
				filePath := uri.URIToPath(file.URI)
				if stat, err := os.Stat(filePath); err == nil && stat != nil {
					if stat.IsDir() {
						// 递归遍历这个目录下的全部文件
						queue := make([]string, 0)
						queue = append(queue, filePath)
						for len(queue) > 0 {
							currentDir := queue[0]
							queue = queue[1:]
							files, err := os.ReadDir(currentDir)
							if err != nil {
								log.Errorf("Failed to read directory: %s", currentDir)
								continue
							}
							for _, file := range files {
								if file == nil {
									continue
								}
								// 获取子文件/目录的绝对路径
								fullPath := filepath.Join(currentDir, file.Name())
								if file.IsDir() {
									// 如果是目录，添加到队列中继续遍历
									queue = append(queue, fullPath)
								} else {
									// 如果是文件，直接索引
									fileIndexer.IndexFile(definition.NewFileCache(fullPath, indexing.SaveOperation, false))
								}
							}
						}
					} else {
						// 如果是单个文件，直接索引
						fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.SaveOperation, false))
					}
				}
			}
			//cs.GetDefaultProcessor().DidCreateFiles(&params, fileIndexer)
		}
	case "textDocument/didSave":
		var params definition.DidSaveTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Debug("did save uri:", params.TextDocument.URI)
		// Insert workspace info
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			params.WorkspaceInfo = workspace
		}
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			filePath := uri.URIToPath(string(params.TextDocument.URI))
			fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.SaveOperation, false))
			//cs.GetProcessor(string(params.TextDocument.URI)).DidSaveTextDocument(&params, fileIndexer)
		}
	case "workspace/didDeleteFiles":
		var params definition.DeleteFilesParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Info("did delete uri:", params.Files)
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {

			for _, file := range params.Files {
				filePath := uri.URIToPath(file.URI)
				// 默认删除文件，都是dir，后续会根据mtree来获取具体删除的是文件还是目录
				fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.DeleteOperation, true))
			}
		}
	case "workspace/didRenameFiles":
		var params definition.RenameFilesParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Info("did rename uri:", params.Files)
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			for _, file := range params.Files {
				oldFilePath := uri.URIToPath(file.OldURI)
				newFilePath := uri.URIToPath(file.NewURI)
				// 不确定重命名的是目录还是文件，覆盖删除
				fileIndexer.IndexFile(definition.NewFileCache(oldFilePath, indexing.DeleteOperation, true))
				fileIndexer.IndexFile(definition.NewFileCache(oldFilePath, indexing.DeleteOperation, false))
				fileIndexer.IndexFile(definition.NewFileCache(newFilePath, indexing.SaveOperation, false))
			}
		}
	case "textDocument/didChange":
		var params definition.DidChangeTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Info("active editor uri change; new uri:", params.TextDocument.URI)
		// Insert workspace info
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			params.WorkspaceInfo = workspace
		}
		if fileIndexer, err := cs.GetFileIndexer(ctx); err == nil {
			filePath := uri.URIToPath(string(params.TextDocument.URI))
			fileIndexer.IndexFile(definition.NewFileCache(filePath, indexing.ChangeOperation, false))
			//cs.GetProcessor(string(params.TextDocument.URI)).DidChangeActiveEditor(&params, fileIndexer)
		}
	case "textDocument/willChange":
		var params definition.WillChangeTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		log.Info("active editor uri change; new uri:", params.FilePath)
		//log.Debugf("active editor uri make changes: %s", util.ToJsonStr(params))

		// Insert workspace info
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			params.WorkspaceInfo = workspace
		}
		cs.GetProcessor(params.FilePath).WillChangeActiveEditor(ctx, &params)
	case "workspace/didChangeWorkspaceFolders":
		// Do nothing when changing workspace folder for now
		// In the future, when the workspace folder is changed, the local model should be re-trained
		log.Debug(">> workspace/didChangeWorkspaceFolders")
		var params definition.DidChangeWorkspaceFoldersParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		res, err := cs.UpdateCurrentWorkspace(ctx, params.Event)
		if err != nil {
			log.Warnf("Failed to UpdateCurrentWorkspace: %s", err.Error())
		}

		if replyErr := cs.Reply(curCtx, req, res, err); replyErr != nil {
			log.Warn("Reply err: ", replyErr)
		}
	case "workspace/didChangeWatchedFiles": // notification
		// Do nothing when changing watched files folder for now
		log.Debug(">> workspace/didChangeWatchedFiles")
	case "doc/recommend":
		var params definition.RecommendParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.Recommend(&params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "doc/search":
		var params definition.SearchParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.Search(&params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "kb/list":
		// 远程知识库列表
		var params definition.KBListParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		docList, pageTotal, err := components.NewLingmaDocRetrever().GetRelevantDocumentList(ctx,
			components.RetrieveDocListRequest{
				Page:      params.Page,
				PageSize:  params.PageSize,
				SceneType: "answer",
				State:     params.State,
				Query:     params.Query,
			})
		result := definition.KBListResult{
			List:      docList,
			PageTotal: pageTotal,
			RequestId: params.RequestId,
		}
		log.Info(fmt.Sprintf("GetRelevantDocumentList, req:%+v, result:%+v, err:%+v", params, result, err))
		if replyErr := cs.Reply(curCtx, req, result, err); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "snippet/search":
		var params definition.SearchParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params for snippet search: ", err)
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.Search(&params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "snippet/report":
		var params search.ReportedStatistics
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse search reported stat data")
		}
		search.ReportSearchStatistics(params)
	case "settings/change":
		var params definition.UserSettingChangedParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse input params:", err)
			return err
		}
		cs.ChangeUserSetting(&params)
	case "commitMsg/generate":
		var params definition.CommitMsgGenerateParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse generate commitMsg param")
		}
		asyncCtx := websocket.CopyContext(curCtx)
		if workspace, err := cs.getCurrentWorkspace(asyncCtx); err == nil {
			asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyWorkspace, workspace)
		}
		if ide, err := cs.GetIdeInfoWithCtx(asyncCtx); err == nil {
			asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyIdeConfig, ide)
		}
		result := chat.GenerateCommitMsg(asyncCtx, params)
		if err := cs.Reply(asyncCtx, req, result, nil); err != nil {
			log.Info("Failed to reply: ", err)
		}
	case "chat/ask":
		var params definition.AskParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse ask param")
			return err
		}
		cs.storeSessionAndClientForChatAsk(ctx, &params)
		//异步流程，copy context
		asyncCtx := websocket.CopyContext(curCtx)
		if workspace, err := cs.getCurrentWorkspace(asyncCtx); err == nil {
			asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyWorkspace, workspace)
		}
		if langIndexer, err := cs.GetFileIndexer(asyncCtx); err == nil {
			asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyFileIndexer, langIndexer)
		}
		if ide, err := cs.GetIdeInfoWithCtx(asyncCtx); err == nil {
			asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyIdeConfig, ide)
		}
		str := util.ToJsonStr(params)
		log.Debugf("chat ask params: %s", str)

		// qoder 如果是多模态场景，判断模型是否支持多模态，不支持的话，直接报错
		if global.IsQoderProduct() {
			isVl := false
			modelConfig := chatUtil.PrepareModelConfig(params)
			if modelConfig != nil && modelConfig.IsVl && "openai" == modelConfig.Format {
				isVl = true
			}
			if !isVl {
				hasImage := chatUtil.HasImage(params.SessionId, params)
				if hasImage {
					chatFinish := definition.ChatFinish{
						RequestId:  params.RequestId,
						SessionId:  params.SessionId,
						Reason:     "trying to submit images without a vision-enabled model selected",
						StatusCode: cosyError.QoderModalIsNotVlError,
					}
					err := websocket.FinishChatRequest(ctx, chatFinish, 10*time.Second)
					if err != nil {
						log.Error(err)
					}
					// 停止回答
					return nil
				}
			}
		}

		processor := cs.GetProcessor("") // default processor
		asyncCtx = context.WithValue(asyncCtx, "processor", processor)
		asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyChatStartTime, time.Now())
		result, err := cs.chatAgent.Ask(asyncCtx, &params)
		if err != nil {
			//返回值通过chat/finish中错误码发送
			log.Errorf("Failed to invoke chatAgent chain reply: requestId: %s, err: %v", result.RequestId, err)
			return err
		}
	case "chat/replyRequest":
		var params definition.ReplyRequestParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse reply request dto")
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.ReplyRequest(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
			return replyErr
		}
	case "chat/stop":
		var params definition.ChatStopParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse reply request dto")
			return err
		}
		processor := cs.GetProcessor("") // default processor
		// 停止问答排队轮询
		isPolling := cs.chatAgent.StopPolling(&params)
		params.IsModelQueuing = isPolling
		result := processor.ChatStop(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to stop chat: ", replyErr)
			return replyErr
		}
	case "chat/stopSession":
		var params definition.SessionStopParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse reply request dto")
			return err
		}
		log.Debugf("deliver request: chat/stopSession: sessionId: %v", params.SessionId)
		//异步流程，copy context
		asyncCtx := websocket.CopyContext(curCtx)

		processor := cs.GetProcessor("") // default processor
		result := processor.SessionStop(asyncCtx, &params)
		if replyErr := cs.Reply(asyncCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to stop chat: ", replyErr)
		}
	case "chat/like":
		var params definition.ChatLikeParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse chat like params")
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.ChatLike(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to like: ", replyErr)
		}
	case "chat/systemEvent":
		var params definition.ChatSystemEventParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse chat system event params")
			return err
		}
		processor := cs.GetProcessor("") // default processor
		result := processor.ChatSystemEvent(curCtx, &params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to system event: ", replyErr)
		}
	case "chat/getSessionById":
		var params definition.GetSessionParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse get session params")
			return err
		}
		chatSession, err := service.SessionServiceManager.GetChatSession(params)
		if err != nil {
			if replyErr := cs.Reply(curCtx, req, "{}", nil); replyErr != nil {
				log.Info("Failed to replay getSessionById: ", replyErr)
			}
			return err
		} else {
			if replyErr := cs.Reply(curCtx, req, chatSession, nil); replyErr != nil {
				log.Info("Failed to replay getSessionById: ", replyErr)
			}
		}
	case "chat/listAllSessions":
		var workspaceInfo, _ = cs.getCurrentWorkspace(ctx)
		includeAgentRecord := util.IsIncludeAgentRecord(ctx)

		var chatSessions []definition.ChatSession
		if !includeAgentRecord && global.IsLingmaProduct() {
			chatSessions = service.SessionServiceManager.ListAllChatSessions(workspaceInfo, true)
		} else {
			chatSessions = service.SessionServiceManager.ListAllChatSessions(workspaceInfo, false)
		}

		if replyErr := cs.Reply(curCtx, req, chatSessions, nil); replyErr != nil {
			log.Info("Failed to replay listAllSessions: ", replyErr)
		}
	case "chat/deleteSessionById":
		var params definition.DeleteSessionParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse del session params")
			return err
		}
		service.SessionServiceManager.DeleteChatSession(params)
	case "chat/clearAllSessions":
		service.SessionServiceManager.ClearUserChatSessions()
	case "chat/deleteChatById":
		var params definition.DeleteSessionChatParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse del session params")
			return err
		}
		err := service.SessionServiceManager.DeleteChat(params)
		if replyErr := cs.Reply(curCtx, req, err == nil, err); replyErr != nil {
			log.Info("Failed to reply deleteChatById: ", replyErr)
		}
		if err != nil {
			log.Info("Failed to reply deleteChatById: ", err)
			return err
		}
	case "chat/codeChange/apply":
		var params definition.CodeChangeApplyParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse code change apply params")
			return err
		}
		err := chat.ApplyAskCodeChanges(ctx, &params)
		if replyErr := cs.Reply(curCtx, req, err == nil, err); replyErr != nil {
			log.Info("Failed to reply code change apply: ", replyErr)
		}
		if err != nil {
			log.Info("Failed to reply code change apply: ", err)
			return err
		}
	case "auth/login":
		var params definition.LoginParams
		if req.Params != nil {
			json.Unmarshal(*req.Params, &params)
		}
		if params.RegionEnv != "" {
			auth.CheckSwitchRegion(curCtx, definition.SwitchRegionParams{
				RegionEnv: params.RegionEnv,
			})
		}
		if params.LoginDedicatedType != "" {
			if params.LoginDedicatedType == definition.LoginDedicatedTypeStandard {
				//标准登录时，清空Endpoint
				auth.UpdateEndpoint(ctx, definition.ConfigEndpointParam{
					Endpoint: "",
				})
			} else if params.LoginDedicatedType == definition.LoginDedicatedTypeDedicated {
				if config.GlobalModelConfig.Endpoint == "" {
					failResult := definition.LoginStartResult{
						Success: false,
					}
					if replyErr := cs.Reply(curCtx, req, failResult, nil); replyErr != nil {
						log.Info("Failed to reply: ", replyErr)
					}
					return nil
				}
			}
		}

		// 增加埋点
		if ide, err := cs.GetIdeInfoWithCtx(ctx); err == nil {
			curCtx = context.WithValue(curCtx, definition.ContextKeyIdeConfig, ide)

			statisticsData := make(map[string]string)
			statisticsData["ide_type"] = ide.IdePlatform
			statisticsData["ide_version"] = ide.IdeVersion
			statisticsData["plugin_version"] = ide.PluginVersion
			statisticsData["ide_series"] = ide.IdeSeries
			deviceInfo := util.BuildDeviceInformation()
			if deviceInfo != nil {
				for k, v := range deviceInfo {
					statisticsData[k] = v
				}
			}

			loginType := params.LoginType
			if loginType == "" {
				loginType = definition.LoginTypeWeb
			}
			statisticsData["login_type"] = loginType
			stable.GoSafe(curCtx, func() {
				requestId := uuid.NewString()
				sls.Report(definition.EventTypeLogin, requestId, statisticsData)
				log.Debugf("post login event. requestId: %s, statisticsData: %+v", requestId, statisticsData)
			}, stable.SceneSystem)
		}
		if params.LoginType == definition.LoginTypeAccessKeySecretKey {
			// 使用AK/SK登录
			log.Info("Logging via ak/sk")
			akLoginRequest := definition.AccessKeyLoginRequest{
				AccessKey: params.AccessKey,
				SecretKey: params.SecretKey,
				OrgId:     params.OrgId,
			}
			replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginWithAkSk(ctx, akLoginRequest), nil)
			if replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else if params.LoginType == definition.LoginTypeNameOnly {
			// 使用用户名登录
			log.Info("Logging via name")
			replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginWithUserName(ctx, params.UserName), nil)
			if replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else if params.LoginType == definition.LoginTypePersonalToken {
			// 使用云效personalToken登录
			log.Info("Logging via personal token")
			replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginWithPersonalToken(ctx, params.PersonalToken, params.OrgId), nil)
			if replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else if params.LoginType == definition.LoginTypeCustom {
			// 使用custom auth info登录
			log.Info("Logging via custom auth info")
			replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginWithCustomAuthInfo(ctx, params.AuthInfo), nil)
			if replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else if config.AuthExtConf.Enabled {
			log.Info("Logging via custom auth ext command")
			customAuthInfo, err := auth.ExecuteExtCmd()
			if err != nil {
				log.Warnf("login with auth command error %v", err)
			}
			if customAuthInfo != nil {
				params.AuthInfo = *customAuthInfo
			}
			replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginWithCustomAuthInfo(ctx, params.AuthInfo), nil)
			if replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else {
			// 使用页面登录
			log.Info("Logging via web")
			if replyErr := cs.Reply(curCtx, req, auth.AuthServer.LoginStart(ctx), nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		}
	case "auth/status":
		var params definition.AuthStatusParams
		var res definition.AuthStatusResult
		if req.Params != nil {
			json.Unmarshal(*req.Params, &params)
		}
		res = auth.AuthServer.GetAuthStatusWithCache(ctx)
		log.Debugf("id: %s, token: %s, quota: %d, status: %d, wlStatus: %d",
			res.Id, res.Token, res.Quota, res.Status, res.WhitelistStatus)
		if replyErr := cs.Reply(curCtx, req, res, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "auth/grantInfos":
		// 老版本，已废弃
		// 查询已加入的企业列表
		var params definition.GrantInfoQueryParams
		json.Unmarshal(*req.Params, &params)

		if params.RegionEnv != "" {
			auth.CheckSwitchRegion(curCtx, definition.SwitchRegionParams{
				RegionEnv: params.RegionEnv,
			})
		}

		grantAuthInfos, err := auth.AuthServer.GetGrantAuthInfos(params)
		if err != nil {
			log.Infof("Failed to call grantInfos: %s", err.Error())
		}
		if replyErr := cs.Reply(curCtx, req, grantAuthInfos, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "auth/grantInfosWrap":
		// 查询已加入的企业列表
		// 将auth/grantInfos的返回值包裹，老接口无法扩展，因此新增wrap接口
		var params definition.GrantInfoQueryParams
		json.Unmarshal(*req.Params, &params)

		if params.RegionEnv != "" {
			auth.CheckSwitchRegion(curCtx, definition.SwitchRegionParams{
				RegionEnv: params.RegionEnv,
			})
		}
		if params.LoginDedicatedType != "" {
			if params.LoginDedicatedType == definition.LoginDedicatedTypeStandard {
				//标准登录时，清空Endpoint
				auth.UpdateEndpoint(ctx, definition.ConfigEndpointParam{
					Endpoint: "",
				})
			} else if params.LoginDedicatedType == definition.LoginDedicatedTypeDedicated {
				if config.GlobalModelConfig.Endpoint == "" {
					failResult := definition.GrantAccountInfoWrap{
						BaseResult: definition.BaseResult{
							RequestId:    uuid.NewString(),
							ErrorCode:    definition.EndpointEmptyErrorCode,
							ErrorMessage: "Endpoint is empty",
							ExtraInfo:    map[string]interface{}{},
						},
					}
					replyErr := cs.Reply(curCtx, req, failResult, nil)
					if replyErr != nil {
						log.Info("Failed to reply: ", replyErr)
					}
					return nil
				}
			}
		}

		grantAuthInfoWrap, err := auth.AuthServer.GetGrantAuthInfosWrap(params)
		if err != nil {
			log.Infof("Failed to call grantInfosWrap: %s", err.Error())
		}
		if replyErr := cs.Reply(curCtx, req, grantAuthInfoWrap, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "auth/switchAccount":
		var params definition.SwitchAccountParams
		json.Unmarshal(*req.Params, &params)
		accountInfoResult, err := auth.AuthServer.SwitchAccount(ctx, params)
		if err != nil {
			log.Infof("Failed to call switch account: %s", err.Error())
		}
		if replyErr := cs.Reply(curCtx, req, accountInfoResult, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "auth/logout":
		if replyErr := cs.Reply(curCtx, req, auth.AuthServer.Logout(ctx), nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "auth/profile/getUrl":
		var params definition.WebViewParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse getUrl params")
		}
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			if workspaceRootPath, ok := workspace.GetWorkspaceFolder(); ok {
				params.WorkspacePath = workspaceRootPath
			}
		}
		_, url := auth.AuthServer.GenerateProfileUrl(&params)
		if replyErr := cs.Reply(curCtx, req, url, nil); replyErr != nil {
			log.Info("Failed to generate profile url: ", replyErr)
		}
	case "auth/profile/getGlobalConfig":
		// VSC 个人设置页获取全局变量
		var params definition.WebViewParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse getGlobalConfig params")
		}
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			if workspaceRootPath, ok := workspace.GetWorkspaceFolder(); ok {
				params.WorkspacePath = workspaceRootPath
			}
		}
		result := auth.AuthServer.GetGlobalConfig(&params)
		log.Debugf("auth/profile/getGlobalConfig: %v", result)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to get global config: ", replyErr)
		}
	case "auth/profile/update":
		var params definition.WebViewParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse update profile params")
		}
		if err := auth.AuthServer.UpdateWebViewPage(&params); err != nil {
			log.Info("Failed to broadcast update request: ", err)
		}
	case "ide/update":
		var params definition.UpdateParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse update param")
		}
		if replyErr := cs.Reply(curCtx, req, update.CheckIdeUpdate(ctx, params), nil); replyErr != nil {
			log.Info("Failed to check update: ", replyErr)
		}
	case "feedback/upload":
		var params definition.FeedbackParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse feedback param")
		}
		if replyErr := cs.Reply(curCtx, req, feedback.Feedback(params), nil); replyErr != nil {
			log.Info("Failed to check update: ", replyErr)
		}
	case "feedback/submit":
		//新版结构化反馈
		var params definition.FeedbackDislikeParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse submit param")
		}
		log.Debug(">> feedback/submit params is ", params)
		resp := SubmitFeedback(params)
		if replyErr := cs.Reply(curCtx, req, resp, nil); replyErr != nil {
			log.Info("Failed to check update: ", replyErr)
		}
	case "config/getGlobal":
		response := config.GetGlobalConfig()
		if replyErr := cs.Reply(curCtx, req, response, nil); replyErr != nil {
			log.Info("Failed to get config: ", replyErr)
		}
	case "config/updateGlobal":
		var configParams definition.GlobalConfigParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := config.UpdateGlobalConfig(configParams)
		client.UpdateHttpClientProxy()
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", configParams, nil)
		} else {
			log.Info("Failed to update global config.")
		}
	case "config/updateGlobalMcpAutoRun":
		var configParams definition.GlobalConfigParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config McpAutoRun update param")
			return err
		}
		originParams := config.GetGlobalConfig()
		// 只更新mcp的
		originParams.McpAutoRun = configParams.McpAutoRun
		updateResult := config.UpdateGlobalConfig(originParams)
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originParams, nil)
		} else {
			log.Info("Failed to update McpAutoRun config.")
		}
	case "config/appendCommandAllowList":
		var configParams definition.ConfigCommandListParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := config.AppendCommandAllowListConfig(configParams)
		originalConfig := config.GetGlobalConfig()
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originalConfig, nil)
		} else {
			log.Info("Failed to update command allow config.")
		}
	case "config/removeCommandAllowList":
		var configParams definition.ConfigCommandListParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := config.RemoveCommandAllowListConfig(configParams)
		originalConfig := config.GetGlobalConfig()
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originalConfig, nil)
		} else {
			log.Info("Failed to update remove command config.")
		}
	case "config/updateGlobalTerminalRunMode":
		var configParams definition.GlobalConfigParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config McpAutoRun update param")
			return err
		}
		originParams := config.GetGlobalConfig()
		// 只更新terminal command
		originParams.TerminalRunMode = configParams.TerminalRunMode
		updateResult := config.UpdateGlobalConfig(originParams)
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originParams, nil)
		} else {
			log.Info("Failed to update terminalRun config.")
		}
	case "config/appendCommandDenyList":
		var configParams definition.ConfigCommandListParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := config.AppendCommandDenyListConfig(configParams)
		originalConfig := config.GetGlobalConfig()
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originalConfig, nil)
		} else {
			log.Info("Failed to update command allow config.")
		}
	case "config/removeCommandDenyList":
		var configParams definition.ConfigCommandListParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := config.RemoveCommandDenyListConfig(configParams)
		originalConfig := config.GetGlobalConfig()
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originalConfig, nil)
		} else {
			log.Info("Failed to update remove command config.")
		}
	case "config/updateGlobalWebToolsAutoExecute":
		var configParams definition.ConfigWebToolsExecutionModeParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		// 当前接口只处理Web 工具自动执行，还是每次询问
		if configParams.WebToolsExecutionMode != definition.WebToolsExecutionModeAutoExecute && configParams.WebToolsExecutionMode != definition.WebToolsExecutionModeAskEveryTime {
			log.Info("Invalidate params")
			return
		}

		// 校验当前用户是否开启了 Web 工具，没有开启的话，不能修改
		originParams := config.GetGlobalConfig()
		// 如果originParams.WebTools不存在，或者originParams.WebToolsExecutionMode是disabled，报错
		if originParams.WebToolsExecutionMode == "" || originParams.WebToolsExecutionMode == definition.WebToolsExecutionModeDisabled {
			log.Info("Web Tools is disabled")
			return
		}
		// 只更新Web 工具自动执行，还是每次询问
		originParams.WebToolsExecutionMode = configParams.WebToolsExecutionMode
		updateResult := config.UpdateGlobalConfig(originParams)
		if updateResult.Success {
			websocket.SendBroadcastWithTimeout(ctx, "config/changeGlobal", originParams, nil)
		} else {
			log.Info("Failed to update Web Tools config.")
		}
	case "config/updateEndpoint":
		var configParams definition.ConfigEndpointParam
		if err := json.Unmarshal(*req.Params, &configParams); err != nil {
			log.Info("Failed to parse config update param")
		}
		updateResult := auth.UpdateEndpoint(ctx, configParams)
		if replyErr := cs.Reply(curCtx, req, updateResult, nil); replyErr != nil {
			log.Info("Failed to get config: ", replyErr)
		}
	case "config/getEndpoint":
		configEndpointParam := definition.ConfigEndpointParam{
			Endpoint: config.GlobalModelConfig.Endpoint,
		}
		if replyErr := cs.Reply(curCtx, req, configEndpointParam, nil); replyErr != nil {
			log.Info("Failed to get endpoint: ", replyErr)
		}
	case "config/queryModels":
		modelConfig := components.QueryModelConfig()
		if replyErr := cs.Reply(curCtx, req, modelConfig, nil); replyErr != nil {
			log.Info("Failed to sync model config: ", replyErr)
		}
	case "error/getLastNotificationError":
		var errorCode string
		if err := json.Unmarshal(*req.Params, &errorCode); err != nil {
			log.Info("Failed to parse errorCode")
		}
		lastNotificationError := remote_model.GetLastNotificationError(errorCode)
		if replyErr := cs.Reply(curCtx, req, lastNotificationError, nil); replyErr != nil {
			log.Info("Failed to get lastNotificationError: ", replyErr)
		}
	case "login/generate_nonce":
		_, nonce := auth.AuthServer.GenerateLoginUrl(ctx)
		generateNonceResult := definition.GenerateNonceResult{
			Nonce:   nonce,
			Success: true,
		}
		if replyErr := cs.Reply(curCtx, req, generateNonceResult, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "login/generateUrl":
		loginUrl, nonce := auth.AuthServer.GenerateLoginUrl(ctx)
		generateNonceResult := definition.GenerateNonceResult{
			LoginUrl: loginUrl,
			Nonce:    nonce,
			Success:  true,
		}
		if replyErr := cs.Reply(curCtx, req, generateNonceResult, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "login/auth_callback":
		var authCallbackParam definition.LoginAuthCallbackParam

		if err := json.Unmarshal(*req.Params, &authCallbackParam); err != nil {
			log.Info("Failed to parse auth callback param")

			authCallbackResult := definition.LoginAuthCallbackResult{
				Success:  false,
				ErrorMsg: "Failed to parse auth callback param",
			}
			if replyErr := cs.Reply(curCtx, req, authCallbackResult, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		}
		err := auth.AuthServer.HandleAuthCallback(authCallbackParam)
		if err != nil {
			log.Info("Failed to handle auth callback: ", err)

			authCallbackResult := definition.LoginAuthCallbackResult{
				Success:  false,
				ErrorMsg: err.Error(),
			}
			if replyErr := cs.Reply(curCtx, req, authCallbackResult, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		} else {
			authCallbackResult := definition.LoginAuthCallbackResult{
				Success: true,
			}
			if replyErr := cs.Reply(curCtx, req, authCallbackResult, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		}
	case "dataPolicy/sign",
		"dataPolicy/cancel":
		// jb和vsc端均为异步实现
		// 这两个方法的返回值是无意义的
		// 因为jb端通过query获取签署状态，vsc通过异步接收notificationError获得签署状态
		// 为了接口的统一性，所以设计一个返回值，但这里是无用的
		userInfo := user.GetCachedUserInfo()
		if userInfo == nil || config.OnPremiseMode ||
			userInfo.UserType == definition.UserTypeEnterpriseStandard ||
			userInfo.UserType == definition.UserTypeEnterpriseDedicated {
			errorResult := definition.SignStatusResult{
				BaseResult: definition.BaseResult{
					RequestId:    uuid.NewString(),
					ErrorCode:    definition.DataPolicySignError.Code,
					ErrorMessage: definition.DataPolicySignError.Message,
				},
			}
			if replyErr := cs.Reply(curCtx, req, errorResult, nil); replyErr != nil {
				log.Info("Failed to sign data policy: ", replyErr)
			}
			return
		}

		updateRequest := components.UpdateDataPolicyRequest{
			RequestId: uuid.NewString(),
		}
		if req.Method == "dataPolicy/sign" {
			updateRequest.Status = definition.SignStatusAgree
		} else if req.Method == "dataPolicy/cancel" {
			updateRequest.Status = definition.SignStatusDisagree
		}

		if config.SignDataPolicyStatus == updateRequest.Status {
			log.Infof("no need to update policy, you have %s the data policy", config.SignDataPolicyStatus)
			// 签署目标状态和当前状态一致，不需要更新
			signResult := definition.SignStatusResult{
				ResultBody: definition.SignStatusResultBody{
					SignStatus: config.SignDataPolicyStatus,
				},
				BaseResult: definition.BaseResult{
					RequestId: uuid.NewString(),
				},
			}
			if replyErr := cs.Reply(curCtx, req, signResult, nil); replyErr != nil {
				log.Info("Failed to get dataPolicy : ", replyErr)
			}
			return
		}

		_, err := components.UpdateDataPolicySignStatus(context.Background(), updateRequest)
		if err != nil {
			log.Info("Failed to update data policy sign status: ", err)
			signResult := definition.SignStatusResult{
				BaseResult: definition.BaseResult{
					RequestId:    uuid.NewString(),
					ErrorCode:    definition.DataPolicySignError.Code,
					ErrorMessage: definition.DataPolicySignError.Message,
				},
			}
			if replyErr := cs.Reply(curCtx, req, signResult, nil); replyErr != nil {
				log.Info("Failed to sign data policy: ", replyErr)
			}
			return err
		}
		signResult := definition.SignStatusResult{
			ResultBody: definition.SignStatusResultBody{
				SignStatus: config.SignDataPolicyStatus,
			},
			BaseResult: definition.BaseResult{
				RequestId: uuid.NewString(),
			},
		}
		if replyErr := cs.Reply(curCtx, req, signResult, nil); replyErr != nil {
			log.Info("Failed to sign data policy: ", replyErr)
		}
	case "dataPolicy/query":
		userInfo := user.GetCachedUserInfo()
		if userInfo == nil {
			errorResult := definition.SignStatusResult{
				BaseResult: definition.BaseResult{
					RequestId:    uuid.NewString(),
					ErrorCode:    definition.DataPolicySignError.Code,
					ErrorMessage: definition.DataPolicySignError.Message,
				},
			}
			if replyErr := cs.Reply(curCtx, req, errorResult, nil); replyErr != nil {
				log.Info("Failed to get endpoint: ", replyErr)
			}
			return nil
		}

		signResult := definition.SignStatusResult{
			ResultBody: definition.SignStatusResultBody{
				SignStatus: config.SignDataPolicyStatus,
			},
			BaseResult: definition.BaseResult{
				RequestId: uuid.NewString(),
			},
		}
		if replyErr := cs.Reply(curCtx, req, signResult, nil); replyErr != nil {
			log.Info("Failed to get dataPolicy : ", replyErr)
		}
	case "extension/query":
		extensionApiConfig, _ := extension.GetExtensionApiConfig()
		if replyErr := cs.Reply(curCtx, req, extensionApiConfig, nil); replyErr != nil {
			log.Info("Failed to push extension api config: ", replyErr)
		}
	case "extension/contextProvider/loadComboBoxItems":
		var loadComboboxItemsParams definition.LoadComboboxItemsParams
		if err := json.Unmarshal(*req.Params, &loadComboboxItemsParams); err != nil {
			log.Info("Failed to parse loadComboBoxItems param")

			if replyErr := cs.Reply(curCtx, req, []definition.ComboBoxItem{}, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)

			}
		}
		request := definition.GetComboBoxItemsRequest{
			RequestId: loadComboboxItemsParams.RequestId,
			Query:     loadComboboxItemsParams.Query,
			Page:      loadComboboxItemsParams.Page,
			PageSize:  loadComboboxItemsParams.PageSize,
		}
		if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
			ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspace)
		}
		ideSdk := extension.BuildSdkTool(ctx)
		response, err := extension.LoadComboBoxItems(ctx, loadComboboxItemsParams.Identifier, request, ideSdk)
		if err != nil {
			log.Info("Failed to load combo box items: ", err)
			if replyErr := cs.Reply(curCtx, req, []definition.ComboBoxItem{}, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
			return err
		} else {
			if replyErr := cs.Reply(curCtx, req, response.ComboBoxItems, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
		}
	case "image/upload":
		var imageUploadParams definition.ImageUploadParams

		failResult := definition.ImageUploadResult{
			BaseResult: definition.BaseResult{
				RequestId:    uuid.NewString(),
				ErrorCode:    definition.UnknownErrorCode,
				ErrorMessage: "Unknown Error",
			}, ResultBody: struct {
				Success bool `json:"success"`
			}{Success: false}}

		if err := json.Unmarshal(*req.Params, &imageUploadParams); err != nil {
			log.Info("Failed to parse imageUploadParams param")
			if replyErr := cs.Reply(curCtx, req, failResult, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
			return err
		}
		if fileExist := util.PathExists(imageUploadParams.ImageUri); !fileExist {
			log.Infof("image not exist. path: %s", imageUploadParams.ImageUri)

			if replyErr := cs.Reply(curCtx, req, failResult, nil); replyErr != nil {
				log.Info("Failed to reply: ", replyErr)
			}
			return
		}

		go func() {
			uploadFailResult := definition.ImageUploadNotificationResult{
				BaseResult: definition.BaseResult{
					ErrorMessage: "image upload failed",
					ErrorCode:    definition.ImageUploadErrorCode,
				},
				ResultBody: definition.ImageUploadNotificationBody{
					RequestId: imageUploadParams.RequestId,
				},
			}
			uploadResp, uploadErr := components.UploadImage(imageUploadParams.ImageUri)

			log.Debugf("uploaded image: %+v", uploadResp)

			if uploadErr != nil {
				websocket.SendBroadcastWithTimeout(ctx, "image/uploadResultNotification", uploadFailResult, nil)

				log.Infof("Failed to upload image: %+v", uploadErr)
				return
			}
			if !uploadResp.Success || uploadResp.Result.Url == "" {
				websocket.SendBroadcastWithTimeout(ctx, "image/uploadResultNotification", uploadFailResult, nil)

				log.Infof("Failed to upload image: %+v ", uploadResp)
				return
			}

			successResult := definition.ImageUploadNotificationResult{
				ResultBody: definition.ImageUploadNotificationBody{
					RequestId: imageUploadParams.RequestId,
					ImageUrl:  uploadResp.Result.Url,
				},
			}
			websocket.SendBroadcastWithTimeout(ctx, "image/uploadResultNotification", successResult, nil)
		}()
		if replyErr := cs.Reply(curCtx, req, definition.ImageUploadResult{
			ResultBody: struct {
				Success bool `json:"success"`
			}{Success: true}}, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
		return nil
	case "agents/testAgent/stepProcessConfirm":
		params := &unittest.StepProcessConfirmRequest{}
		if err := json.Unmarshal(*req.Params, params); err != nil {
			log.Error("Failed to parse stepProcessConfirm param")
			return err
		}
		log.Debugf("[agent][stepProcessConfirm] params.base=%v", params.StepProcessConfirmBase)
		resumeAgentSuccess := true
		resumeAgentErrMessage := ""
		if err := agent.ResumeAgent(params.RequestId, params); err != nil {
			log.Errorf("Failed to resume agent: %v", err)
			resumeAgentErrMessage = err.Error()
			resumeAgentSuccess = false
		}
		if replyErr := cs.Reply(curCtx, req, definition.AgentStepConfirmResult{
			RequestId:    params.RequestId,
			ErrorMessage: resumeAgentErrMessage,
			Successful:   resumeAgentSuccess,
		}, nil); replyErr != nil {
			log.Infof("Failed to reply: %v", replyErr)
		}
	case "workingSpaceFile/operate":
		var params definition.WorkingSpaceFileOperateParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse operate workingSpaceFile params")
		}

		go experience.WorkspaceOperationHandle(curCtx, service.OP_TYPE(params.OpType), params.Id)

		errorCode, errorMsg := service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, params)
		result := definition.BaseResult{
			ErrorCode:    errorCode,
			ErrorMessage: errorMsg,
		}

		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "workingSpaceFile/listBySnapshot":
		var params definition.WorkingSpaceFileListBySnapshotParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse list workingSpaceFiles params")
		}
		var err error
		var workingSpaceFiles []definition.WorkingSpaceFileVO
		if params.SnapshotId != "" {
			workingSpaceFiles, err = service.WorkingSpaceServiceManager.ListWorkingSpaceFileVOsBySnapshot(ctx, params.SnapshotId, false, true, params.FillContent)
		} else {
			workingSpaceFiles, err = service.WorkingSpaceServiceManager.ListWorkingSpaceFileVOsByChatRecord(ctx, params.SessionId, params.ChatRecordId, false)
		}
		errorCode := ""
		errorMsg := ""
		if err != nil {
			errorCode = definition.UnknownErrorCode
			errorMsg = err.Error()
		}
		result := definition.WorkingSpaceFileListResult{
			BaseResult: definition.BaseResult{
				ErrorCode:    errorCode,
				ErrorMessage: errorMsg,
			},
			WorkingSpaceFiles: workingSpaceFiles,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "workingSpaceFile/getFullContent":
		var params definition.WorkingSpaceFileGetContentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse get workingSpaceFile content params")
		}
		content, err := service.WorkingSpaceServiceManager.GetFullContent(ctx, params)
		errorCode := ""
		errorMsg := ""
		if err != nil {
			errorCode = definition.UnknownErrorCode
			errorMsg = err.Error()
		}
		result := definition.WorkingSpaceFileFullContentResult{
			BaseResult: definition.BaseResult{
				ErrorCode:    errorCode,
				ErrorMessage: errorMsg,
			},
			Content: content,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "workingSpaceFile/getLastStableContent":
		var params definition.WorkingSpaceFileGetContentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse get workingSpaceFile content params")
		}
		content, version, err := service.WorkingSpaceServiceManager.GetLastStableContent(ctx, params)
		errorCode := ""
		errorMsg := ""
		if err != nil {
			errorCode = definition.UnknownErrorCode
			errorMsg = err.Error()
		}
		result := definition.WorkingSpaceFileStableContentResult{
			BaseResult: definition.BaseResult{
				ErrorCode:    errorCode,
				ErrorMessage: errorMsg,
			},
			Content: content,
			Version: version,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "workingSpaceFile/getBaseMd5":
		var params definition.WorkingSpaceFileGetContentParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse get workingSpaceFile content params")
		}
		md5, err := service.WorkingSpaceServiceManager.GetBaseMd5(ctx, params)
		errorCode := ""
		errorMsg := ""
		if err != nil {
			errorCode = definition.UnknownErrorCode
			errorMsg = err.Error()
		}
		result := definition.WorkingSpaceFileMd5Result{
			BaseResult: definition.BaseResult{
				ErrorCode:    errorCode,
				ErrorMessage: errorMsg,
			},
			MD5: md5,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "snapshot/operate":
		var params definition.SnapshotOperateParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse snapshot operate params")
		}

		if len(params.WorkingSpaceFiles) > 0 {
			go experience.WorkspaceOperationHandle(curCtx, service.OP_TYPE(params.OpType), params.WorkingSpaceFiles[0].Id)
		}

		errorCode, errorMsg := service.WorkingSpaceServiceManager.OperateSnapshot(ctx, params)
		result := definition.BaseResult{
			ErrorCode:    errorCode,
			ErrorMessage: errorMsg,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "snapshot/listBySession":
		var params definition.SnapshotListBySessionParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse snapshot listBySession params")
		}
		snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(params.SessionId)
		errorCode := ""
		errorMsg := ""
		if err != nil {
			errorCode = definition.UnknownErrorCode
			errorMsg = err.Error()
		}
		result := definition.SnapshotListResult{
			BaseResult: definition.BaseResult{
				ErrorCode:    errorCode,
				ErrorMessage: errorMsg,
			},
			Snapshots: snapshots,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "session/close":
		var params definition.SessionCloseParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to close session params")
		}
		log.Debugf("deliver request: session/close: sessionId: %v", params.SessionId)
		errorCode, errorMsg := service.WorkingSpaceServiceManager.CloseSession(ctx, params.SessionId)
		result := definition.BaseResult{
			ErrorCode:    errorCode,
			ErrorMessage: errorMsg,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "session/getCurrent":
		sessionIds := service.WorkingSpaceServiceManager.GetCurrentSessions()
		result := definition.SessionListResult{
			BaseResult:        definition.BaseResult{},
			CurrentSessionIds: sessionIds,
		}
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "agents/codeDiffGenerate":
		var params definition.DiffApplyGenerateParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to generate code diff params")
		}
		result := ability.GenerateDiffApply(ctx, params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "system/reportDiagnosisLog":
		var params definition.LogFeedbackParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse diagnosis log params")
		}
		result := feedback.ReportDiagnosisLog(ctx, params)
		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "tool/call/approve":
		params := &definition.ToolConfirmRequest{}
		if err := json.Unmarshal(*req.Params, params); err != nil {
			log.Error("Failed to parse ToolConfirmRequest")
			return err
		}
		resumeAgentSuccess := true
		resumeAgentErrMessage := ""
		//if err := support.ResumeAgentWithOriginCtx(curCtx, params.RequestId, params); err != nil {
		//	log.Errorf("Failed to resume agent: %v", err)
		//	resumeAgentErrMessage = err.Error()
		//	resumeAgentSuccess = false
		//}
		if err := support.ConfirmToolCall(curCtx, params); err != nil {
			log.Errorf("Failed to resume agent: %v", err)
			resumeAgentErrMessage = err.Error()
			resumeAgentSuccess = false
		}
		if replyErr := cs.Reply(curCtx, req, definition.AgentStepConfirmResult{
			RequestId:    params.RequestId,
			ErrorMessage: resumeAgentErrMessage,
			Successful:   resumeAgentSuccess,
		}, nil); replyErr != nil {
			log.Infof("Failed to reply: %v", replyErr)
		}
	case "tool/invokeResult":
		params := &definition.ToolInvokeResponse{}
		if err := json.Unmarshal(*req.Params, params); err != nil {
			log.Error("Failed to parse ToolResponse param")
			return err
		}
		log.Debugf("[agent][stepProcessConfirm] params.base=%v", params)
		if err := common.ReceiveToolResponse(ctx, params); err != nil {
			log.Errorf("Failed to ReceiveToolResponse: %v", err)
		}
	case "tool/call/results":
		var params definition.GetSessionParam
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse get session params")
			if replyErr := cs.Reply(curCtx, req, AgentToolCallResult{
				ErrorMessage: "Failed to parse get session params",
				Successful:   false,
			}, nil); replyErr != nil {
				log.Infof("Failed to reply: %v", replyErr)
			}
			return err
		}
		toolResults, err := coder.GetToolResultsBySession(params.SessionId)
		if err != nil {
			if replyErr := cs.Reply(curCtx, req, AgentToolCallResult{
				ErrorMessage: "get tools failed",
				Successful:   true,
				ToolResults:  toolResults,
			}, nil); replyErr != nil {
				log.Infof("Failed to reply: %v", replyErr)
			}
			return err
		}
		//todo 返回toolResults
		log.Info(toolResults)
		if replyErr := cs.Reply(curCtx, req, AgentToolCallResult{
			ErrorMessage: "",
			Successful:   true,
			ToolResults:  toolResults,
		}, nil); replyErr != nil {
			log.Infof("Failed to reply: %v", replyErr)
		}
	case "wiki/deleteIndex":
		var params definition.WikiIndexParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse wiki deleteIndex params:", err)
			cs.Reply(curCtx, req, definition.BaseResult{
				ErrorCode:    definition.UnknownErrorCode,
				ErrorMessage: "Failed to parse params",
			}, nil)
			return nil
		}

		// 删除wiki索引
		err := deepwiki.GlobalWikiService.DeleteWikiIndex(curCtx, params.WorkspacePath)
		result := definition.BaseResult{}
		if err != nil {
			result.ErrorCode = definition.UnknownErrorCode
			result.ErrorMessage = err.Error()
		}

		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	case "wiki/rebuildIndex":
		var params definition.WikiIndexParams
		if err := json.Unmarshal(*req.Params, &params); err != nil {
			log.Info("Failed to parse wiki rebuildIndex params:", err)
			cs.Reply(curCtx, req, definition.BaseResult{
				ErrorCode:    definition.UnknownErrorCode,
				ErrorMessage: "Failed to parse params",
			}, nil)
			return nil
		}

		// 重建wiki索引
		err := deepwiki.GlobalWikiService.RebuildWikiIndex(curCtx, params.WorkspacePath)
		result := definition.BaseResult{}
		if err != nil {
			result.ErrorCode = definition.UnknownErrorCode
			result.ErrorMessage = err.Error()
		}

		if replyErr := cs.Reply(curCtx, req, result, nil); replyErr != nil {
			log.Info("Failed to reply: ", replyErr)
		}
	default:
		if h := handlers.GetHandler(req.Method); h != nil {
			if err := h(ctx, req); err != nil {
				log.Errorf("process method %s encouter err: %v", req.Method, err)
			}
		} else {
			log.Info("Unknown method:", req.Method)
			return
		}
	}

	if !IsIgnoreMethodLog(req.Method) {
		log.Debug("call method finish >> " + req.Method)
	}
	return nil
}

// Reply sends response of given request or an error to websocket server
// The response will be forwarded to corresponding client
func (cs *CosyServer) Reply(ctx context.Context, request *websocket.WireRequest, response interface{}, err error) error {
	if request.IsMock {
		MockResult = response
		return err
	}
	return websocket.WsInst.Reply(ctx, request, response, err)
}

// prefillContext fills the context with necessary information
// such as workspace, IDE type, and file indexer
func (cs *CosyServer) prefillContext(ctx context.Context) context.Context {
	curCtx := ctx
	// Insert workspace info
	if workspace, err := cs.getCurrentWorkspace(ctx); err == nil {
		curCtx = context.WithValue(curCtx, definition.ContextKeyWorkspace, workspace)
	}

	// Add IDE type
	if ideSeries, err := cs.getIdeSeries(ctx); err == nil {
		curCtx = context.WithValue(curCtx, "ide", ideSeries)
	}
	if langIndexer, err := cs.GetFileIndexer(ctx); err == nil {
		curCtx = context.WithValue(curCtx, definition.ContextKeyFileIndexer, langIndexer)
	}
	if ideConfig, err := cs.GetIdeInfoWithCtx(ctx); err == nil {
		curCtx = context.WithValue(curCtx, definition.ContextKeyIdeConfig, ideConfig)
	}
	return curCtx
}

// storeSessionAndClientForChatAsk RemoteAgent模式下储存会话与Client
func (cs *CosyServer) storeSessionAndClientForChatAsk(ctx context.Context, params *definition.AskParams) {
	if !cs.remoteAgentMode {
		return
	}
	if params.SessionId == "" {
		return
	}
	longruntask.ChatSessionClientCache.Remove(params.SessionId)
	longruntask.ChatSessionClientCache.Add(params.SessionId, ctx.Value(websocket.ClientCtxKey))
}

func IsIgnoreMethodLog(method string) bool {
	if strings.EqualFold(method, "ping") || strings.EqualFold(method, "textDocument/preCompletion") || strings.EqualFold(method, "workingSpaceFile/getFullContent") ||
		strings.EqualFold(method, "textDocument/didSave") || strings.EqualFold(method, "workingSpaceFile/listBySnapshot") || strings.EqualFold(method, "textDocument/didClose") {
		return true
	}
	return false
}

func simpleLogMethodEnter(ctx context.Context, cs *CosyServer, method string) {
	if cs == nil {
		return
	}

	wsClient, _ := cs.getCurrentClient(ctx)
	ideInfo, _ := cs.GetIdeInfoWithCtx(ctx)

	var ideLog string
	if ideInfo != nil {
		ideLog = fmt.Sprintf("idePlatform: %s, ideVersion: %s, pluginVersion: %s", ideInfo.IdePlatform, ideInfo.IdeVersion, ideInfo.PluginVersion)
	}

	logMessage := fmt.Sprintf("call method begin >> %s, wsClient: %p, %s", method, wsClient, ideLog)
	log.Debug(logMessage)
}
