package server

import (
	"context"
	chainImpl "cosy/chat/chains"
	"cosy/config"
	"cosy/deepwiki"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/indexing"
	"cosy/lang"
	"cosy/log"
	"cosy/storage"
	"cosy/util"
	"cosy/util/collection"
	"cosy/websocket"
	"errors"
	"io/ioutil"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// CosyServer delivers requests from websocket server to processors
// It manages active workspaces, when a new client registered, an initialize message should be send to CosyServer to initialize a workspace
// Before unregister a client, an exit message should be send to CosyServer to clean the workspace for the client
type CosyServer struct {
	Processors      map[string]lang.Processor                                                // All CosyServers share same processors, which reduces memory usage
	Wd              string                                                                   // Wd is the working directory
	workspaces      *collection.SynchronizedMap[*websocket.Client, definition.WorkspaceInfo] // Workspaces are managed in CosyServer, and passed to processors
	ideInfo         *collection.SynchronizedMap[*websocket.Client, definition.IdeConfig]     // Stores information and settings about client IDE
	db              storage.KvStore
	fileIndexer     *collection.SynchronizedMap[*websocket.Client, *indexing.ProjectFileIndex]
	indexerManager  *indexing.GlobalFileIndex
	chatAgent       *chainImpl.ChatService
	remoteAgentMode bool
}

func NewCosyServer(processors map[string]lang.Processor, db storage.KvStore) *CosyServer {
	// WorkspacePath info is initialized in "initialize" request from client
	return &CosyServer{
		Processors:     processors,
		Wd:             util.GetCosyHomePath(),
		workspaces:     collection.NewSynchronizedMap[*websocket.Client, definition.WorkspaceInfo](),
		ideInfo:        collection.NewSynchronizedMap[*websocket.Client, definition.IdeConfig](),
		db:             db,
		fileIndexer:    collection.NewSynchronizedMap[*websocket.Client, *indexing.ProjectFileIndex](),
		indexerManager: indexing.NewGlobalFileIndex(db),
		chatAgent:      chainImpl.InitializeChatService(),
	}
}

func NewRemoteAgentCosyServer(processors map[string]lang.Processor, db storage.KvStore) *CosyServer {
	// WorkspacePath info is initialized in "initialize" request from client
	return &CosyServer{
		Processors:      processors,
		Wd:              util.GetCosyHomePath(),
		workspaces:      collection.NewSynchronizedMap[*websocket.Client, definition.WorkspaceInfo](),
		ideInfo:         collection.NewSynchronizedMap[*websocket.Client, definition.IdeConfig](),
		db:              db,
		fileIndexer:     collection.NewSynchronizedMap[*websocket.Client, *indexing.ProjectFileIndex](),
		indexerManager:  indexing.NewGlobalFileIndex(db),
		chatAgent:       chainImpl.InitializeChatService(),
		remoteAgentMode: true,
	}
}

// Initialize receives a clients initialize message, do initialization works for the client's workspace
//
// Note that Initialize can be called many times to initialize different workspaces for different clients.
func (cs *CosyServer) Initialize(ctx context.Context, params *definition.InitializeParams) (*definition.InitializeResult, error) {
	start := time.Now()

	//通用的lingma client，主动补位
	if params.PluginPublisher == "" {
		params.PluginPublisher = global.PublisherAliyun
	}
	if params.PluginName == "" {
		params.PluginName = global.PluginName
	}

	// Default result
	res := defaultInitializeResult()
	// Get client first
	client, err := cs.getCurrentClient(ctx)
	log.Infof("initializing client: %p workspace: %v, ideSeries: %s, idePlatform: %s, ideVersion: %s, pluginVersion: %s",
		client, params.WorkspaceFolders, params.IdeSeries, params.IdePlatform, params.IdeVersion, params.PluginVersion)
	if err != nil {
		return &res, err
	}

	if _, ok := cs.workspaces.Get(client); ok {
		return &res, errors.New("cannot initialize the workspace twice")
	}

	// Register client's workspace
	workspaceInfo := definition.WorkspaceInfo{WorkspaceFolders: params.WorkspaceFolders}
	cs.workspaces.Set(client, workspaceInfo)
	// Register IDE info
	cs.ideInfo.Set(client, params.IdeConfig)
	cs.fileIndexer.Set(client, cs.indexerManager.GetOrAddIndexerWithHandler(workspaceInfo, func(fileIndexer *indexing.ProjectFileIndex) {

		workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
		if workspacePath == "" {
			log.Infof("workspace path is not specific, ignore wiki generate.")
			return
		}
		if !util.IsDir(workspacePath) {
			//非目录场景，比如vsc拖拽单个文件到编辑器中
			return
		}

		ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, fileIndexer)
		ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceInfo)

		go deepwiki.GlobalWikiService.GenerateUpdate(ctx, definition.CreateDeepwikiRequest{
			RequestId:         uuid.NewString(),
			WorkspacePath:     workspacePath,
			PreferredLanguage: definition.DeepwikiPreferredLanguage,
		})
	}))

	// Update global IDE info and statistics option
	//log.Infof("Registering %s %s", params.IdePlatform, params.IdeVersion)
	global.IdeInfo.Store(params.IdePlatform, params.IdeConfig) // 使用 sync.Map 的 Store 方法
	global.CosyConfig.AllowReportUsage = &params.AllowStatistics
	config.UpdateBeamConfig(params.InferenceMode)
	if params.MaxCandidateNum > 0 && params.MaxCandidateNum <= 5 {
		global.CosyConfig.Local.MaxCandidateNum = &params.MaxCandidateNum
	}

	//同步experiment内容
	fillExperimentFeatures(&res, params.IdeConfig)

	// Initialize processors
	processorCnt := len(cs.Processors)
	var wg sync.WaitGroup
	wg.Add(processorCnt)
	for _, p := range cs.Processors {
		go p.Initialize(lang.InitializeProcessorParams{
			Client:        client,
			WorkspaceInfo: workspaceInfo,
			Db:            cs.db,
			IdeSeries:     params.IdeSeries,
		}, &wg)
	}
	wg.Wait()

	log.Infof("Server initialized, time cost: %v", time.Since(start))
	return &res, nil
}

func fillExperimentFeatures(initializeResult *definition.InitializeResult, ideConfig definition.IdeConfig) {
	ideSeries := strings.ToLower(ideConfig.IdeSeries)
	experimentalConfig := definition.Experimental{}
	if ideSeries == "vscode" {
		experimentalConfig.Features = experiment.ConfigService.GetExperimentConfigs(experiment.ConfigScopeVscode)
	} else if ideSeries == "jetbrains" {
		experimentalConfig.Features = experiment.ConfigService.GetExperimentConfigs(experiment.ConfigScopeJetbrains)
	} else if strings.Contains(ideSeries, experiment.ConfigScopeQoder) {
		experimentalConfig.Features = experiment.ConfigService.GetExperimentConfigs(experiment.ConfigScopeJetbrains)
	}
	initializeResult.Experimental = experimentalConfig
}

// WorkspaceClose handles workspace close requests, stopping wiki generation for specified workspaces
func (cs *CosyServer) WorkspaceClose(ctx context.Context, params *definition.WorkspaceCloseParams) (*definition.WorkspaceCloseResult, error) {
	start := time.Now()

	result := &definition.WorkspaceCloseResult{
		Success:      true,
		Message:      "Workspace closed successfully",
		StoppedTasks: 0,
	}

	// Get current client
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		log.Warnf("Failed to get current client for workspace close: %v", err)
		result.Success = false
		result.Message = "Failed to identify client"
		return result, err
	}

	// Stop wiki generation tasks for each workspace folder
	for _, workspaceFolder := range params.WorkspaceFolders {
		workspacePath := workspaceFolder.URI
		if workspacePath == "" {
			log.Warnf("Empty workspace path, skipping")
			continue
		}

		log.Debugf("Stopping wiki generation for workspace: %s", workspacePath)

		// Stop wiki generation for this workspace using deepwiki service
		if deepwiki.GlobalWikiService != nil {
			// Stop any running wiki generation tasks for this workspace
			// This will stop both full generation and incremental update tasks
			stopped := cs.stopWikiGenerationForWorkspace(workspacePath)
			result.StoppedTasks += stopped
			log.Debugf("Stopped %d wiki generation tasks for workspace: %s", stopped, workspacePath)
		}
	}

	// Clean up client-specific resources
	cs.cleanupClientResources(client, params.WorkspaceFolders)

	log.Debugf("Workspace close completed, time cost: %v, stopped tasks: %d", time.Since(start), result.StoppedTasks)
	return result, nil
}

// stopWikiGenerationForWorkspace stops wiki generation tasks for a specific workspace
func (cs *CosyServer) stopWikiGenerationForWorkspace(workspacePath string) int {
	stopped := 0

	// Stop tasks and monitoring for the workspace using the wiki service
	if deepwiki.GlobalWikiService != nil {
		// Use the new comprehensive stop method that:
		// 1. Cancels all pending/processing tasks for the workspace
		// 2. Removes workspace from monitoring
		// 3. Cleans up workspace locks
		stopped = deepwiki.GlobalWikiService.StopWorkspaceTasks(workspacePath)

		log.Debugf("Wiki generation stopped for workspace: %s, tasks stopped: %d", workspacePath, stopped)
	} else {
		log.Warnf("GlobalWikiService is nil, cannot stop wiki generation for workspace: %s", workspacePath)
	}

	return stopped
}

// cleanupClientResources cleans up resources associated with a client when workspace is closed
func (cs *CosyServer) cleanupClientResources(client *websocket.Client, workspaceFolders []definition.WorkspaceFolder) {
	// Remove the client's workspace info
	if workspace, exists := cs.workspaces.Get(client); exists {
		// Filter out the closed workspace folders
		remainingFolders := []definition.WorkspaceFolder{}
		closedFolderMap := make(map[string]bool)

		// Create a map of folders being closed
		for _, folder := range workspaceFolders {
			closedFolderMap[folder.URI] = true
		}

		// Keep folders that are not being closed
		for _, folder := range workspace.WorkspaceFolders {
			if !closedFolderMap[folder.URI] {
				remainingFolders = append(remainingFolders, folder)
			}
		}

		// Update or remove workspace info
		if len(remainingFolders) > 0 {
			// Update workspace with remaining folders
			workspace.WorkspaceFolders = remainingFolders
			cs.workspaces.Set(client, workspace)
			log.Debugf("Updated workspace with %d remaining folders", len(remainingFolders))
		} else {
			// Remove workspace entirely if no folders remain
			cs.workspaces.Delete(client)
			cs.ideInfo.Delete(client)
			cs.fileIndexer.Delete(client)
			log.Debugf("Removed all workspace info for client")
		}
	}
}

// GetWorkspaceGitConfig parses workspace's .git folder and returns parsed git configs for current client
//
// Note: the registered workspace should be removed when the client is unregistered
func (cs *CosyServer) GetWorkspaceGitConfig(workspaceFolders []definition.WorkspaceFolder) definition.WorkspaceGitConfig {
	if len(workspaceFolders) < 1 {
		log.Info("No workspace folder")
		return definition.WorkspaceGitConfig{}
	}

	// Use the first workspace as the root
	configBytes, err := ioutil.ReadFile(filepath.Join(workspaceFolders[0].URI, ".git", "config"))
	if err != nil {
		log.Warn("Failed to identify a git repository")
		return definition.WorkspaceGitConfig{}
	}
	parsedGitConfig := util.ParseGitConfig(string(configBytes))
	projectUrl := parsedGitConfig["remote \"origin\""]["url"]
	userName := parsedGitConfig["user"]["name"]
	userEmail := parsedGitConfig["user"]["email"]
	headBytes, err := ioutil.ReadFile(filepath.Join(workspaceFolders[0].URI, ".git", "HEAD"))
	if err != nil {
		log.Debug("Failed to identify a git repository in workspace")
	}
	branch := strings.TrimPrefix(strings.TrimSpace(string(headBytes)), "ref: refs/heads/")
	return definition.WorkspaceGitConfig{
		User:       userName,
		UserEmail:  userEmail,
		ProjectUrl: projectUrl,
		Branch:     branch,
	}
}

// UnregisterClient cleans all resources of given client
//
// This should be called before client is unregistered from websocket server
// Unregistering of a client means the client is disconnected from websocket server, which is different from `exit` call from jsonrpc
// `exit` just means that the client is exitting, but the websocket connection remains there
func (cs *CosyServer) UnregisterClient(client *websocket.Client) {
	// Remove client from registered workspaces
	for _, p := range cs.Processors {
		p.UnregisterClient(client)
	}
	cs.workspaces.Delete(client)
}

// GetProcessor returns corresponding processor by given filename
func (cs *CosyServer) GetProcessor(filename string) lang.Processor {
	language := util.GetLanguageByFilePath(filename)
	if util.Contains([]string{definition.Java, definition.Python}, language) {
		return cs.Processors[language]
	}
	// 通用处理器
	return cs.Processors[definition.Others]
}

func (cs *CosyServer) GetDefaultProcessor() lang.Processor {
	return cs.Processors[definition.Others]
}

// ChangeUserSetting updates users settings
func (cs *CosyServer) ChangeUserSetting(params *definition.UserSettingChangedParams) {
	global.CosyConfig = params.CosyConfig
	config.UpdateBeamConfig(*params.Local.InferenceMode)
}

// Exit does the clean work for current cosy server before quitting
func (cs *CosyServer) Exit(ctx context.Context) {
	// Do nothing for now

	log.Info("Exit triggered")
}

func defaultInitializeResult() definition.InitializeResult {
	return definition.InitializeResult{
		Capabilities: definition.ServerCapabilities{
			CompletionProvider: definition.CompletionOptions{
				ResolveProvider:   true,
				TriggerCharacters: []string{".", " ", "\r"},
			},
			TextDocumentSync: &definition.TextDocumentSyncOptions{
				Change:    definition.Incremental,
				OpenClose: true,
				Save: definition.SaveOptions{
					IncludeText: false,
				},
			},
			Workspace: definition.WorkspaceGn{
				WorkspaceFolders: definition.WorkspaceFoldersGn{
					Supported:           true,
					ChangeNotifications: "workspace/didChangeWorkspaceFolders",
				},
			},
			ExecuteCommandProvider: definition.ExecuteCommandOptions{
				Commands: []string{},
			},
		},
	}
}

func (cs *CosyServer) getCurrentClient(ctx context.Context) (*websocket.Client, error) {
	client, ok := ctx.Value(websocket.ClientCtxKey).(*websocket.Client)
	if !ok {
		return nil, errors.New("no client found in context")
	}
	return client, nil
}

func (cs *CosyServer) getCurrentWorkspace(ctx context.Context) (definition.WorkspaceInfo, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return definition.WorkspaceInfo{}, err
	}
	workspace, ok := cs.workspaces.Get(client)
	if !ok {
		return definition.WorkspaceInfo{}, errors.New("cannot find current workspace")
	}
	return workspace, nil
}

func (cs *CosyServer) UpdateCurrentWorkspace(ctx context.Context, event definition.WorkspaceFoldersChangeEvent) (definition.WorkspaceInfo, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return definition.WorkspaceInfo{}, err
	}
	workspace, ok := cs.workspaces.Get(client)
	if !ok {
		return definition.WorkspaceInfo{}, errors.New("cannot find current workspace")
	}
	workspaceFolders := workspace.WorkspaceFolders
	folderMap := make(map[string]int)
	for _, folder := range workspaceFolders {
		folderMap[folder.URI] = 1
	}
	for _, folder := range event.Removed {
		folderMap[folder.URI] = 0
	}
	finalWorkspaceFolders := []definition.WorkspaceFolder{}
	for _, folder := range workspaceFolders {
		if folderMap[folder.URI] == 1 {
			finalWorkspaceFolders = append(finalWorkspaceFolders, folder)
		}
	}
	for _, folder := range event.Added {
		if folderMap[folder.URI] == 0 {
			finalWorkspaceFolders = append(finalWorkspaceFolders, folder)
		}
	}
	workspace.WorkspaceFolders = finalWorkspaceFolders
	cs.workspaces.Set(client, workspace)
	return workspace, nil
}

func (cs *CosyServer) GetFileIndexer(ctx context.Context) (*indexing.ProjectFileIndex, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return nil, err
	}
	fileIndexer, ok := cs.fileIndexer.Get(client)
	if !ok {
		return nil, errors.New("cannot find file indexer")
	}
	return fileIndexer, nil
}

func (cs *CosyServer) getIdeSeries(ctx context.Context) (string, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return "", err
	}
	ideInfo, ok := cs.ideInfo.Get(client)
	if !ok {
		return "", errors.New("cannot find current IDE type")
	}
	return ideInfo.IdeSeries, nil
}

func (cs *CosyServer) getPluginVersion(ctx context.Context) (string, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return "", err
	}
	ideInfo, ok := cs.ideInfo.Get(client)
	if !ok {
		return "", errors.New("cannot find current plugin version")
	}
	return ideInfo.PluginVersion, nil
}

func (cs *CosyServer) GetIdeInfoWithCtx(ctx context.Context) (*definition.IdeConfig, error) {
	client, err := cs.getCurrentClient(ctx)
	if err != nil {
		return nil, err
	}
	ideConfig, err := cs.GetIdeInfo(client)
	if err != nil {
		return nil, err
	}
	return &ideConfig, nil
}

func (cs *CosyServer) GetIdeInfo(client *websocket.Client) (definition.IdeConfig, error) {
	ideInfo, ok := cs.ideInfo.Get(client)
	if !ok {
		return definition.IdeConfig{}, errors.New("cannot find current IDE type")
	}
	return ideInfo, nil
}

func (cs *CosyServer) GetWorkspaceInfos() ([]definition.WorkspaceInfo, error) {
	if cs.workspaces == nil {
		return nil, errors.New("workspaces is nil")
	}
	return cs.workspaces.Values(), nil
}
