package rule

import "time"

// ProjectRulesTrigger 规则触发类型
type ProjectRulesTrigger string

const (
	ManualRule        ProjectRulesTrigger = "manual"         // 手工引入
	GlobRule          ProjectRulesTrigger = "glob"           // 通过glob匹配引入
	ModelDecisionRule ProjectRulesTrigger = "model_decision" // 模型判断
	AlwaysOnRule      ProjectRulesTrigger = "always_on"      // 始终开启
)

// ProjectRule 规则领域对象
type ProjectRule struct {
	Name         string              `json:"name"`         // 规则名称,例如clean-code
	FilePath     string              `json:"filePath"`     // 规则文件全路径
	Trigger      ProjectRulesTrigger `json:"trigger"`      // 规则触发类型
	Glob         string              `json:"glob"`         // glob匹配规则，当触发类型为"glob"时，必传
	Description  string              `json:"description"`  // 规则描述，当触发类型为"model_decision"时，必传
	Content      string              `json:"-"`            // 规则内容，不参与序列化
	ModifiedTime time.Time           `json:"modifiedTime"` // 修改时间
	CreateTime   time.Time           `json:"createTime"`   // 创建时间
	HasFormatErr bool                `json:"hasFormatErr"` // 是否有格式错误
}

// AddProjectRuleParams 用于封装新增规则的请求参数
type AddProjectRuleParams struct {
	Name        string              `json:"name"`        // 规则名称,例如clean-code
	Trigger     ProjectRulesTrigger `json:"trigger"`     // 规则触发类型
	Glob        string              `json:"glob"`        // glob匹配规则，当触发类型为"glob"时，必传
	Description string              `json:"description"` // 规则描述，当触发类型为"model_decision"时，必传
	Content     string              `json:"content"`     // 规则内容
}

// EditProjectRuleParams 用于封装修改规则的请求参数
type EditProjectRuleParams struct {
	Name        string              `json:"name"`        // 规则名称,例如clean-code
	NewName     string              `json:"newName"`     // 规则的新名称，例如clean-code-v1
	Trigger     ProjectRulesTrigger `json:"trigger"`     // 规则触发类型
	Glob        string              `json:"glob"`        // glob匹配规则，当触发类型为"glob"时，必传
	Description string              `json:"description"` // 规则描述，当触发类型为"model_decision"时，必传
	Content     string              `json:"content"`     // 规则内容
}

// DeleteProjectRuleParams 用于封装删除规则的请求参数
type DeleteProjectRuleParams struct {
	Name string `json:"name"` // 规则文件全路径
}

// QueryProjectRuleParams 用于封装查询单个规则详情的请求参数
type QueryProjectRuleParams struct {
	Name     string `json:"name"`     // 规则文件全路径
	Page     int    `json:"page"`     // 分页参数，默认为1
	PageSize int    `json:"pageSize"` // 分页参数，默认为20
}

type ProjectRuleContext struct {
	UserManualSelectedRules []*ProjectRule // 本次请求用户手动选择的rules
	GlobMatchedRules        []*ProjectRule // 本次请求匹配到的glob rules
	AllAlwaysRules          []*ProjectRule
	AllModelDecisionRules   []*ProjectRule
	AllGlobRules            []*ProjectRule
}

// EffectiveRule 请求时规则生效的方式
type EffectiveRule struct {
	Rule          *ProjectRule
	EffectiveType string // manual_import、glob_match、model_decision
}
