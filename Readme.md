# Cosy

An intelligent coding assistant which makes your coding easy

# Development guide
    本地模型只支持java/python

## 安装跨平台编译器

使用HomeBrew安装Zig和musl-cross 

```bash
brew install zig    
brew install FiloSottile/musl-cross/musl-cross
```

 ### 手动编译方式
```
 go build -tags local -gcflags="all=-N -l" -ldflags " -X main.trialEdition=云效 -X main.serverUrl=http://120.26.6.89:80/algo -X main.expireTime=1709222400" -o ~/.lingma/bin/1.0.17/x86_64_darwin/Lingma
 ~/.lingma/bin/1.0.17/x86_64_darwin/Lingma start
```

或者从[download](https://ziglang.org/download/)页面下载离线包

## 本地配置文件

- 启动参数配置：`~/.lingma/bin/config.json`

### 模型参数配置
- Mac：`~/Library/Caches/.lingma/config.json`
- Windows的模型参数配置为 `C:\Users\<USER>\AppData\Local\.lingma\config.json`
- linux：~/.cache/.lingma/config.json

### 本地数据库(老版，已废弃)
- Mac：`~/Library/Caches/.lingma/db/local.db`
- Windows的本地db： `C:\Users\<USER>\AppData\Local\.lingma\db\local.db`
- linux：`~/.cache/.lingma/db/local.db`

### 本地数据库(v2.1.2开始)
- Mac：`~/.lingma/caches/.lingma/db/local.db`
- Windows的本地db： `C盘/用户/[用户名]/.lingma/cache\db\local.db`
- linux：`~/.lingma/caches/.lingma/db/local.db`


# 工具脚本

## 构建debug包并启动本地服务
- 根目录下新建DEV_VERSION文件，修改内容为版本号，如1.3.13
- 执行./build_local_debug.sh，会构建出debug包到 ~/.lingma/bin/${version}目录，并启动
```bash
./build_local_debug.sh
```

# IDE启动灵码go调试
![img_1.png](https://img.alicdn.com/imgextra/i1/O1CN01ltEGkj1u095HcaAqi_!!6000000005974-0-tps-545-236.jpg)
### 启动配置参数
- go tool arguments: -tags=dev
- program arguments: start
### 环境变量
-  COSY_DEV_VERSION=1.3.14   
用于mock版本号，会使用 **~/.lingma/bin/${version}/extension/main.js** 作为执行引擎代码
-  COSY_LOCAL_DEV   
  - 会使用dev模式启动，部分场景下会产生额外日志
  - 开启node 调试，会启动node服务，并使用node调试器调试

## 构建主流平台服务包

```bash
./build_local_all.sh out
```

## 启用和关闭调试日志

- 查看状态 `./switch_debug_log.sh`
- 开启 `./switch_debug_log.sh on`
- 关闭 `./switch_debug_log.sh off`

## 切换连接的服务端环境

- 查看当前环境 `./switch_env.sh`
- 切换到本地环境 `./switch_env.sh local`
- 切换到日常环境 `./switch_env.sh daily`
- 切换到预发环境 `./switch_env.sh prepub`
- 切换到正式环境 `./switch_env.sh prod`

## Restart lingma process

## PIPE模式下如何切换到预发环境
注： pipe模式，灵码进程被项目级独占
### 先查看pipe模式下灵码进程的工作目录，--workDir参数
![img.png](img.png)
### 进入灵码workDir工作目录，修改config.json 配置连接参数
```shell
  ## 增加配置——预发环境
  "remote_config": {
    "big_model_endpoint": "https://devops.aliyun.com/search",
    "big_model_cookie": "version=ga"  
  }
  
  ## 增加配置——日常环境
  "remote_config": {
    "big_model_endpoint": "https://devops.aliyun.com/search",
    "big_model_cookie": "version=ga_dev"  
  }
```


```bash
./restart_process.sh
```

## View lingma log
 - mac： ~/.lingma/logs/lingma.log
 - windows：C盘/用户/[用户名]/.lingma/logs/lingma.log

## 机器id
- mac：cat ~/Library/Caches/.lingma/id
- windows：//TODO

## debug日志

## 日志路径：
- windows：C盘/用户/[用户名]/.lingma/cache/diagnosis.bin
- mac/linux：~/.lingma/cache/diagnosis.bin


```shell
go env -w GOPROXY=http://goproxy.alibaba-inc.com      
```


### 日志解码：

运行`tool/cache_log_reader/main.go`，运行参数-i <path/to/diagnosis.bin> -o <path/to/diagnosis.log>

解码工具构建：
`注意：该工具只能小范围传播，不能提供给外部人员及用户`

构建MAC arm64版本（默认）：
```shell
make log_decode
```
构建MAC x86_64版本：
```shell
make GOOS=darwin GOARCH=amd64 log_decode
```
工具使用：
```shell
./lingma_log_decode_darwin_arm64 -i <path/to/diagnosis.bin> -o <path/to/diagnosis.log>
```

## 阿里集团go module代理服务
https://goproxy.alibaba-inc.com/

# 常见问题

## code.alibaba-inc.com 私有仓库无法访问

解决办法：

首先设置go环境变量

```shell
export GOPRIVATE='gitlab.alibaba-inc.com'
export GONOPROXY='code.alibaba-inc.com'
export GONOSUMDB='code.alibaba-inc.com'
export GOPROXY='http://goproxy.alibaba-inc.com'
```

然后正在任意目录 git clone 一个 code.alibaba-inc.com 的代码库，比如

```shell
git clone https://code.alibaba-inc.com/cosy/lingma-agent-graph.git
```

重新获取依赖即可

```shell
go mod download
```

# qoder相关
## 数据存储
|                    | Windows（用户/系统）                                                                 | macOS（zip包/dmg）                                                                 | Linux                                      |
|--------------------|------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------|--------------------------------------------|
| **IDE 安装路径**   | **用户**：`C:\Users\<USER>\AppData\Local\Programs\{产品名}`<br/>**系统**：`C:\Programe Files\{产品名}` | `/Applications/{产品名}`                                                          | `/usr/share/{产品名}`                      |
| **IDE 数据目录**   | `C:\Users\<USER>\AppData\Roaming\{产品名}\`                                          | `~/Library/Application Support/{产品名}/`                                         | `~/.config/{产品名}/`                      |
| **IDE 扩展目录**   | `C:\Users\<USER>\.QoderIDE`                                                         | `~/.QoderIDE`                                                                     | `~/.QoderIDE`                              |
| **Go 进程解压/启动目录** | **用户**：`{IDE 安装路径}\Contents\Resources\app\shared_client\bin`<br>**系统**：`C:\Users\<USER>\.qoder` | **dmg版**：`{IDE 安装路径}/Contents/Resources/app/shared_client/bin`<br>**zip版**：`~/.qoder` | `{IDE 安装路径}/Contents/Resources/app/shared_client/bin` |
| **Go 进程数据目录** | `{IDE数据目录}\SharedClientCache`                                                  | `{IDE数据目录}\SharedClientCache`                                                 | `{IDE数据目录}\SharedClientCache`          |



## qoder IDE中的go连接不上IDE本地启动的DEBUG模式 
原因：最新版qoder IDE因为规范数据目录调整了建立连接时的内置的client的解压启动和数据目录目录，与本地IDE启动的目录不一致。   
#### 解决办法：
- 查看qoder IDE中client的数据存储目录，使用 ```ps aux | grep Qoder | grep start``` 查看 --workDir参数
- 修改IDE中client的启动目录，在IDE启动参数中新增/修改 QODER_HOME 环境变量，指向上面qoderIDE中--workDir参数指定的目录
![参考图片](https://img.alicdn.com/imgextra/i3/O1CN01RVogyF29OAB00YPF6_!!6000000008057-2-tps-2098-198.png)

## qoder IDE中的go数据存储目录/日志开启参数/模型配置/工程索引文件在哪里   
同上方--workDir参数指定的路径，一般情况：
- Mac上：~/Library/Application Support/Qoder/SharedClientCache
- windows上：C:\Users\<USER>\AppData\Roaming\Qoder\SharedClientCache
- linux上：~/.config/Qoder/SharedClientCache

## golang启动client调试时如何指定env.json
原因：qoder的client进程启动目录和数据分离，env.json是跟随灵码进程启动目录；从golang ide启动时，默认从"~/.qoder/bin/env.json"加载了
### 解决办法：显式指定灵码client启动及env.json路径
增加goland启动参数中的环境变量 QODER_PROCESS_HOME，指向需要加载env.json的相对路径（不包含\bin路径）