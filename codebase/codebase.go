package codebase

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	graph_definition "code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"

	"bufio"
	"bytes"
	"cosy/codebase/dependency"
	"cosy/codebase/graph"
	"cosy/codebase/overview"
	"cosy/codebase/recommend"
	"cosy/codebase/semantic"
	"cosy/codebase/statistics"
	"cosy/codebase/symbol"
	"cosy/components"
	"cosy/indexing"
	"cosy/lang/indexer"
	"strings"
)

// 代码库检索工具类型常量
const (
	// 语义搜索类型
	TypeSemanticSearch = "semantic_search"
	// 代码结构概览类型
	TypeCodeOverview = "code_overview"
	// 框架识别类型
	TypeFrameworkDetection = "framework_detection"
	// 符号搜索类型
	TypeSymbolSearch = "symbol_search"
	// 依赖分析类型
	TypeDependencyAnalysis = "dependency_analysis"
	// 代码统计类型
	TypeCodeStatistics = "code_statistics"
	// 文件推荐类型
	TypeFileRecommendation = "file_recommendation"
	// 文件依赖类型
	TypeFileDependencies = "file_dependencies"
)

// 各工具结果类型定义
type SemanticSearchResult struct {
	Documents []indexer.CodeChunk `json:"documents"`
}

type CodeOverviewResult struct {
	Structure string `json:"structure"`
}

type FrameworkDetectionResult struct {
	Framework string `json:"framework"`
}

type SymbolSearchResult struct {
	Symbols []SymbolWithSnippet `json:"symbols"`
}

type DependencyAnalysisResult struct {
	Graph *dependency.DependencyGraph `json:"graph"`
}

type CodeStatisticsResult struct {
	Stats interface{} `json:"stats"`
}

type FileRecommendationResult struct {
	Files []recommend.RecommendationFile `json:"files"`
}

type FileDependenciesResult struct {
	Dependencies graph.Graph `json:"dependencies"`
	GraphInfo    string      `json:"graph_info"`
}

// CodebaseToolResult 代码库工具执行结果
type CodebaseToolResult[T any] struct {
	// 工具类型
	Type string `json:"type"`
	// 执行结果
	Result T `json:"result"`
	// 错误信息
	Error string `json:"error,omitempty"`
}

// GetType returns the tool type
func (r *CodebaseToolResult[T]) GetType() string {
	return r.Type
}

// GetResult returns the result
func (r *CodebaseToolResult[T]) GetResult() interface{} {
	return r.Result
}

// CodebaseToolOptions 代码库工具选项
type CodebaseToolOptions struct {
	// 工作区URI
	WorkspaceURI string `json:"workspace_uri"`
	// 子目录
	SubDir string `json:"sub_dir,omitempty"`
	// 查询内容
	RawQuery string `json:"raw_query,omitempty"`
	// 精炼后的查询内容
	RefinedQuery string `json:"refined_query,omitempty"`
	// 关键词
	Keywords []string `json:"keywords,omitempty"`
	// 代码类型
	CodeCategory string `json:"code_category,omitempty"`
	// 返回结果数量限制
	Limit int `json:"limit,omitempty"`
	// 令牌数量限制
	TokenLimit int `json:"token_limit,omitempty"`
	// 语义检索选项
	SemanticOptions semantic.RetrieveOptions `json:"semantic_options,omitempty"`
	// 文件路径
	FilePath string `json:"file_path,omitempty"`
	// 起始行
	StartLine int `json:"start_line,omitempty"`
	// 结束行
	EndLine int `json:"end_line,omitempty"`
	// 符号搜索语言
	SearchLanguage string `json:"search_language,omitempty"`
	// 符号名
	SearchSymbolQuery string `json:"search_symbol_query,omitempty"`
	// 是否排序
	RankResult bool `json:"rank_result,omitempty"`
}

// Operator 代码库工具操作接口
type Operator interface {
	// 执行语义搜索
	SemanticSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SemanticSearchResult]

	// 获取代码结构概览
	GetCodeOverview(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeOverviewResult]

	// 识别项目框架
	DetectFramework(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FrameworkDetectionResult]

	// 符号搜索（函数、类、变量等）
	SymbolSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SymbolSearchResult]

	// 依赖分析
	AnalyzeDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[DependencyAnalysisResult]

	// 代码统计
	GetCodeStatistics(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeStatisticsResult]

	// 推荐文件
	RecommendFiles(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileRecommendationResult]

	// 获取文件依赖
	GetFileDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileDependenciesResult]

	GetFileStructures(ctx context.Context, filePaths []string, workspaceURI string) (map[string]string, error)
}

// operatorImpl 代码库工具操作实现
type operatorImpl struct {
	semanticOp    semantic.Operator
	overviewOp    overview.Operator
	dependencyOp  dependency.Operator
	statisticsOp  statistics.Operator
	recommendOp   recommend.Recommender
	symbolOp      symbol.SymbolSearcher
	fileIndexer   *indexing.ProjectFileIndex
	graphSearcher graph.GraphSearcher
}

func GetToolKits(p *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder) (tk Operator, err error) {
	treeIndexer, ok := p.GetWorkspaceTreeFileIndexer()
	if !ok {
		return nil, errors.New("workspace tree indexer not found")
	}
	meteFileIndexer, ok := p.GetMetaFileIndexer()
	if !ok {
		return nil, errors.New("meta file indexer not found")
	}
	graphFileIndexer, ok := p.GetGraphFileIndexer()
	if !ok {
		return nil, errors.New("graph file indexer not found")
	}

	reranker := components.NewLingmaReranker()

	tk = &operatorImpl{
		semanticOp:    semantic.NewRagOperatorWithReranker(p, embedder, reranker),
		overviewOp:    overview.NewOverviewOperator(treeIndexer.WorkspaceTree),
		recommendOp:   recommend.NewBaseRecommender(treeIndexer, meteFileIndexer),
		dependencyOp:  dependency.NewClient(p),
		symbolOp:      symbol.NewBaseSymbolSearcher(meteFileIndexer, graphFileIndexer),
		graphSearcher: graph.NewBaseGraphSearcher(graphFileIndexer),
		fileIndexer:   p,
	}
	return
}

// 创建包含语法结构的结果
type DocumentWithStructure struct {
	indexer.CodeChunk
	FileStructure string `json:"file_structure,omitempty"`
}

// SemanticSearch 实现语义搜索
func (op *operatorImpl) SemanticSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SemanticSearchResult] {
	result := CodebaseToolResult[SemanticSearchResult]{
		Type: TypeSemanticSearch,
	}

	if options.Limit <= 0 {
		options.Limit = 30 // 默认返回30条结果
	}

	query := semantic.RetrieveQuery{
		RawQuery:     options.RawQuery,
		RefinedQuery: options.RefinedQuery,
		Keywords:     options.Keywords,
		CodeCategory: options.CodeCategory,
	}

	docs, err := op.semanticOp.Retrieve(ctx, query, options.WorkspaceURI, options.Limit, options.SemanticOptions)
	if err != nil {
		log.Error(err)
		result.Error = err.Error()
		return result
	}

	result.Result = SemanticSearchResult{Documents: docs}
	return result
}

func (op *operatorImpl) GetFileStructures(ctx context.Context, filePaths []string, workspaceURI string) (map[string]string, error) {
	// 获取MetaFileIndexer
	metaFileIndexer, ok := op.fileIndexer.GetMetaFileIndexer()
	if !ok {
		return nil, errors.New("[codebase] meta file indexer not found")
	}
	// 获取每个文件的语法结构
	fileStructures := make(map[string]string)
	for _, filePath := range filePaths {
		lang := util.GetLanguageByFilePath(filePath)
		langIndexer, ok := metaFileIndexer.GetLangIndexer(lang)
		if !ok || langIndexer == nil {
			continue
		}

		// 创建虚拟文件
		virtualFile := definition.NewVirtualFile(filePath)

		// 索引文件获取元信息
		metas, err := langIndexer.IndexFile(ctx, workspaceURI, virtualFile)
		if err != nil {
			continue
		}

		// 将元信息转换为结构化的字符串
		var structureBuilder strings.Builder
		for key, meta := range metas {
			if unifiedMeta, ok := meta.(indexer.UnifiedMeta); ok {
				structureBuilder.WriteString(fmt.Sprintf("类型: %s\n", unifiedMeta.MetaType))
				structureBuilder.WriteString(fmt.Sprintf("名称: %s\n", unifiedMeta.Name))
				structureBuilder.WriteString(fmt.Sprintf("位置: %d-%d行\n", unifiedMeta.LineRange.StartLine, unifiedMeta.LineRange.EndLine))
				if len(unifiedMeta.Methods) > 0 {
					structureBuilder.WriteString("方法:\n")
					for methodName, methods := range unifiedMeta.Methods {
						for _, method := range methods {
							structureBuilder.WriteString(fmt.Sprintf("  - %s: %s\n", methodName, method.MethodSignature))
						}
					}
				}
				if len(unifiedMeta.Fields) > 0 {
					structureBuilder.WriteString("字段:\n")
					for fieldName, field := range unifiedMeta.Fields {
						structureBuilder.WriteString(fmt.Sprintf("  - %s: %s\n", fieldName, field.FieldSignature))
					}
				}
				structureBuilder.WriteString("\n")
			} else {
				structureBuilder.WriteString(fmt.Sprintf("元信息: %s = %v\n", key, meta))
			}
		}

		fileStructures[filePath] = structureBuilder.String()
	}

	return fileStructures, nil
}

// GetCodeOverview 实现代码结构概览
func (op *operatorImpl) GetCodeOverview(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeOverviewResult] {
	result := CodebaseToolResult[CodeOverviewResult]{
		Type: TypeCodeOverview,
	}

	if options.TokenLimit <= 0 {
		options.TokenLimit = 4000 // 默认令牌限制
	}

	structure, err := op.overviewOp.GetStructure(ctx, options.WorkspaceURI, options.SubDir, options.TokenLimit)
	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = CodeOverviewResult{Structure: structure}
	return result
}

// DetectFramework 实现框架识别
func (op *operatorImpl) DetectFramework(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FrameworkDetectionResult] {
	result := CodebaseToolResult[FrameworkDetectionResult]{
		Type: TypeFrameworkDetection,
	}

	if options.TokenLimit <= 0 {
		options.TokenLimit = 2000 // 默认令牌限制
	}

	framework, err := op.overviewOp.GuessFramework(ctx, options.WorkspaceURI, options.TokenLimit)
	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = FrameworkDetectionResult{Framework: framework}
	return result
}

// 为每个符号添加代码片段内容
type SymbolWithSnippet struct {
	symbol.SymbolSearchResult
	Snippet string `json:"snippet,omitempty"`
}

// GetSymbolSnippet 获取符号对应的代码片段
func GetSymbolSnippet(filePath string, workspacePath string, startLine uint32, endLine uint32) string {
	// 确保文件路径是绝对路径
	if !filepath.IsAbs(filePath) && workspacePath != "" {
		filePath = filepath.Join(workspacePath, filePath)
	}

	// 读取代码片段
	if startLine < endLine {
		content, err := readFileByLine(filePath, startLine, endLine)
		if err == nil {
			return content
		}
	}
	return ""
}

// SymbolSearch 实现符号搜索
func (op *operatorImpl) SymbolSearch(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[SymbolSearchResult] {
	result := CodebaseToolResult[SymbolSearchResult]{
		Type:   TypeSymbolSearch,
		Result: SymbolSearchResult{Symbols: make([]SymbolWithSnippet, 0)},
	}

	if options.Limit <= 0 {
		options.Limit = 25 // 默认返回25条结果
	}

	symbolsQuery := strings.Split(options.SearchSymbolQuery, " ")
	if len(symbolsQuery) == 0 {
		return result
	}

	// 计算每个分词的最大结果数，确保所有分词都能公平贡献结果
	maxPerQuery := options.Limit
	if len(symbolsQuery) > 1 {
		// 为多个分词分配结果数量，留出一些buffer防止某些分词没有结果
		maxPerQuery = int(float64(options.Limit) * 1.5 / float64(len(symbolsQuery)))
		if maxPerQuery < 3 {
			maxPerQuery = 3 // 确保每个分词至少能获得3个结果
		}
	}

	var allSymbols [][]symbol.SymbolSearchResult
	// 对每个符号进行搜索，并收集分组结果
	for _, symbolItem := range symbolsQuery {
		symbolResult, err := op.symbolOp.SearchSymbol(ctx, &symbol.SymbolSearchParams{
			Language:      options.CodeCategory,
			WorkspacePath: options.WorkspaceURI,
			RankResult:    options.RankResult,
			SymbolKey:     symbolItem,
			MaxCount:      maxPerQuery * 2, // 搜索更多结果以便后续筛选
		})
		if err != nil {
			log.Error(err)
			continue
		}

		// 过滤匹配的符号
		var matchedSymbols []symbol.SymbolSearchResult
		for _, tempSymbol := range symbolResult {
			if strings.HasSuffix(strings.ToLower(symbolItem), strings.ToLower(tempSymbol.SymbolName)) ||
				strings.HasSuffix(strings.ToLower(tempSymbol.SymbolName), strings.ToLower(symbolItem)) {
				matchedSymbols = append(matchedSymbols, tempSymbol)
				// 限制每个分词的结果数量
				if len(matchedSymbols) >= maxPerQuery {
					break
				}
			}
		}
		allSymbols = append(allSymbols, matchedSymbols)
	}

	// 使用轮询算法平衡地合并所有分词的结果
	var symbols []symbol.SymbolSearchResult
	symbols = mergeSymbolResultsRoundRobin(allSymbols, options.Limit)

	// 先进行去重处理，避免后续重复读取文件
	uniqueSymbols := removeDuplicateSymbols(symbols)

	// 直接对SymbolSearchResult类型进行过滤，避免不必要的类型转换
	filteredSymbolResults := filterContainedSymbolResults(uniqueSymbols)

	// 最后才读取文件内容，只为过滤后的符号读取
	filteredSymbols := make([]SymbolWithSnippet, 0, len(filteredSymbolResults))
	for _, sym := range filteredSymbolResults {
		snippet := sym.Content
		if sym.Content == "" {
			// 使用GetSymbolSnippet获取代码片段
			snippet = GetSymbolSnippet(sym.FilePath, sym.WorkspacePath, sym.LineRange.StartLine, sym.LineRange.EndLine)
		}
		filteredSymbols = append(filteredSymbols, SymbolWithSnippet{
			SymbolSearchResult: sym,
			Snippet:            snippet,
		})
	}

	result.Result.Symbols = filteredSymbols

	return result
}

// filterContainedSymbolResults 过滤被包含的符号片段，如果片段A完全包含片段B，则移除片段B
func filterContainedSymbolResults(symbols []symbol.SymbolSearchResult) []symbol.SymbolSearchResult {
	filteredSymbols := make([]symbol.SymbolSearchResult, 0, len(symbols))
	for i, symbolA := range symbols {
		isContained := false
		for j, symbolB := range symbols {
			if i != j && symbolA.FilePath == symbolB.FilePath {
				// 检查symbolA是否被symbolB包含
				// 如果symbolB的范围完全包含symbolA的范围，则symbolA被包含
				if symbolB.LineRange.StartLine <= symbolA.LineRange.StartLine &&
					symbolB.LineRange.EndLine >= symbolA.LineRange.EndLine &&
					!(symbolB.LineRange.StartLine == symbolA.LineRange.StartLine &&
						symbolB.LineRange.EndLine == symbolA.LineRange.EndLine) {
					isContained = true
					break
				}
			}
		}
		if !isContained {
			filteredSymbols = append(filteredSymbols, symbolA)
		}
	}
	return filteredSymbols
}

// mergeSymbolResultsRoundRobin 使用轮询算法公平地合并多个分词的搜索结果
// 确保每个分词的结果都能公平地被包含在最终结果中
func mergeSymbolResultsRoundRobin(allSymbols [][]symbol.SymbolSearchResult, limit int) []symbol.SymbolSearchResult {
	if len(allSymbols) == 0 {
		return nil
	}

	// 去重set，防止重复添加相同的符号
	seen := make(map[string]bool)
	var merged []symbol.SymbolSearchResult

	// 记录每个分词结果组的当前索引
	indices := make([]int, len(allSymbols))

	// 轮询直到达到限制或所有结果用完
	for len(merged) < limit {
		added := false

		// 遍历每个分词的结果
		for i, symbols := range allSymbols {
			// 如果当前分词还有结果未处理
			if indices[i] < len(symbols) {
				sym := symbols[indices[i]]
				// 检查是否已经添加过这个符号
				if !seen[sym.SymbolKey] {
					seen[sym.SymbolKey] = true
					merged = append(merged, sym)
					added = true
					// 达到限制就退出
					if len(merged) >= limit {
						break
					}
				}
				indices[i]++
			}
		}

		// 如果这一轮没有添加任何新的符号，说明所有结果都处理完了
		if !added {
			break
		}
	}

	return merged
}

// removeDuplicateSymbols 去除重复的符号，基于SymbolKey进行去重
func removeDuplicateSymbols(symbols []symbol.SymbolSearchResult) []symbol.SymbolSearchResult {
	seen := make(map[string]bool)
	uniqueSymbols := make([]symbol.SymbolSearchResult, 0, len(symbols))

	for _, sym := range symbols {
		if !seen[sym.SymbolKey] {
			seen[sym.SymbolKey] = true
			uniqueSymbols = append(uniqueSymbols, sym)
		}
	}

	return uniqueSymbols
}

// readFileByLine 按照行号来读取文件内容
func readFileByLine(filePath string, startLine uint32, endLine uint32) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("an error occurred while opening the file: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var currentLine uint32
	currentLine = 0

	var buffer bytes.Buffer
	for scanner.Scan() {
		if currentLine >= startLine && currentLine <= endLine {
			buffer.WriteString(scanner.Text())
			buffer.WriteByte('\n') // 保留原始换行符
		} else if currentLine > endLine {
			break // 提前退出循环
		}
		currentLine++
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("an error occurred while reading the file: %v", err)
	}

	return buffer.String(), nil
}

// AnalyzeDependencies 实现依赖分析
func (op *operatorImpl) AnalyzeDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[DependencyAnalysisResult] {
	result := CodebaseToolResult[DependencyAnalysisResult]{
		Type: TypeDependencyAnalysis,
	}

	analysisOptions := dependency.AnalysisOptions{
		Depth:                 2, // 默认分析深度
		IncludeExternal:       false,
		GenerateVisualization: true,
	}

	var graph *dependency.DependencyGraph
	var err error

	if options.SubDir != "" {
		// 分析指定模块
		graph, err = op.dependencyOp.AnalyzeModuleDependencies(ctx, options.WorkspaceURI, options.SubDir, analysisOptions)
	} else {
		// 分析整个项目
		graph, err = op.dependencyOp.AnalyzeProjectDependencies(ctx, options.WorkspaceURI, analysisOptions)
	}

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = DependencyAnalysisResult{Graph: graph}
	return result
}

// GetCodeStatistics 实现代码统计
func (op *operatorImpl) GetCodeStatistics(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[CodeStatisticsResult] {
	result := CodebaseToolResult[CodeStatisticsResult]{
		Type: TypeCodeStatistics,
	}

	statsOptions := statistics.StatisticsOptions{
		IncludeHidden:     false,
		IncludeAuthors: <AUTHORS>
		IncludeComplexity: true,
		IncludeHistory:    false,
	}

	var stats interface{}
	var err error

	if options.SubDir != "" {
		// 获取指定目录的统计信息
		stats, err = op.statisticsOp.GetDirectoryStats(ctx, options.WorkspaceURI, options.SubDir, statsOptions)
	} else {
		// 获取整个项目的统计信息
		stats, err = op.statisticsOp.GetProjectStats(ctx, options.WorkspaceURI, statsOptions)
	}

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = CodeStatisticsResult{Stats: stats}
	return result
}

const (
	RecommendFileByFile  = "recommend_file_by_file"
	RecommendFileByQuery = "recommend_file_by_query"
	MaxRecommendFileSize = 3
)

// RecommendFiles 实现文件推荐
func (op *operatorImpl) RecommendFiles(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileRecommendationResult] {
	result := CodebaseToolResult[FileRecommendationResult]{
		Type: TypeFileRecommendation,
	}

	if options.Limit <= 0 {
		options.Limit = MaxRecommendFileSize
	}

	var recommendFiles []recommend.RecommendationFile
	var err error

	// 基于文件内容推荐
	err, recommendFiles = op.recommendOp.RecommendFile(ctx, &recommend.RecommendParams{
		FilePath: options.FilePath,
		LineRange: definition.LineRange{
			StartLine: uint32(options.StartLine),
			EndLine:   uint32(options.EndLine),
		},
		Query:         options.RawQuery,
		WorkspacePath: options.WorkspaceURI,
		MaxCount:      options.Limit,
	})

	if err != nil {
		result.Error = err.Error()
		return result
	}

	result.Result = FileRecommendationResult{Files: recommendFiles}
	return result
}

func (op *operatorImpl) GetFileDependencies(ctx context.Context, options CodebaseToolOptions) CodebaseToolResult[FileDependenciesResult] {
	result := CodebaseToolResult[FileDependenciesResult]{
		Type: TypeFileDependencies,
	}

	language := util.GetLanguageByFilePath(options.FilePath)

	// 过滤非编程语言类型的文件
	if language == definition.PlainText || language == definition.Others || language == definition.Markdown {
		return result
	}

	graphResult, err := op.graphSearcher.ExpandGraph(ctx, graph.ExpandGraphQuery{
		WorkspacePath: options.WorkspaceURI,
		FilePath:      options.FilePath,
		StartLine:     -1,
		EndLine:       -1,
	})

	if err != nil {
		result.Error = err.Error()
		return result
	}

	// 生成 mermaid 图
	mermaidChart := generateMermaidChart(graphResult)

	result.Result = FileDependenciesResult{Dependencies: graphResult, GraphInfo: mermaidChart}
	return result
}

// generateMermaidChart 将 graph 信息转换为 mermaid 图
func generateMermaidChart(graphResult graph.Graph) string {
	if len(graphResult.Nodes) == 0 {
		return "graph TD\n  A[无依赖关系]"
	}

	var buffer strings.Builder
	buffer.WriteString("graph TD\n")

	// 添加节点
	for nodeId, node := range graphResult.Nodes {
		// 清理节点名称，移除特殊字符
		cleanName := cleanNodeName(node.NodeName)
		// 根据节点类型设置不同的样式
		nodeStyle := getNodeStyle(node.NodeType)
		buffer.WriteString(fmt.Sprintf("  %s[%s]%s\n", nodeId, cleanName, nodeStyle))
	}

	buffer.WriteString("\n")

	// 添加边
	for _, edge := range graphResult.Edges {
		// 根据边类型设置不同的箭头样式
		arrowStyle := getArrowStyle(edge.EdgeType)
		buffer.WriteString(fmt.Sprintf("  %s %s %s\n", edge.SourceId, arrowStyle, edge.TargetId))
	}

	return buffer.String()
}

// cleanNodeName 清理节点名称，移除特殊字符
func cleanNodeName(name string) string {
	// 移除可能导致 mermaid 语法错误的字符
	clean := strings.ReplaceAll(name, "[", "(")
	clean = strings.ReplaceAll(clean, "]", ")")
	clean = strings.ReplaceAll(clean, "{", "(")
	clean = strings.ReplaceAll(clean, "}", ")")
	clean = strings.ReplaceAll(clean, "\"", "'")
	clean = strings.ReplaceAll(clean, "\n", " ")
	clean = strings.ReplaceAll(clean, "\r", " ")
	// 限制长度
	if len(clean) > 50 {
		clean = clean[:47] + "..."
	}
	return clean
}

// getNodeStyle 根据节点类型获取样式
func getNodeStyle(nodeType string) string {
	switch nodeType {
	case graph_definition.ModuleMetaType, "Module":
		return ":::module"
	case graph_definition.StructMetaType, "Struct":
		return ":::struct"
	case graph_definition.DataMetaType, "Data":
		return ":::data"
	case graph_definition.ClassMetaType, "Class":
		return ":::CLASS"
	case graph_definition.SealedMetaType, "Sealed":
		return ":::sealed"
	case graph_definition.EnumMetaType, "Enum":
		return ":::enum"
	case graph_definition.ObjectMetaType, "Object":
		return ":::object"
	case graph_definition.InterfaceMetaType, "Interface":
		return ":::interface"
	case graph_definition.AnnotationMetaType, "Annotation":
		return ":::annotation"
	case graph_definition.UnionMetaType, "Union":
		return ":::union"
	case graph_definition.FunctionMetaType, "Function":
		return ":::function"
	case graph_definition.VariableMetaType, "Variable":
		return ":::variable"
	case graph_definition.ConstantMetaType, "Constant":
		return ":::constant"
	case graph_definition.MacrocDefMetaType, "MacrocDef":
		return ":::macroc_def"
	case graph_definition.MacrocFuncMetaType, "MacrocFunc":
		return ":::macroc_func"
	case graph_definition.TypeDefMetaType, "TypeDef":
		return ":::typedef"
	case graph_definition.DBTableMetaType, "DBTable":
		return ":::db_table"
	case graph_definition.MethodMetaType, "Method":
		return ":::method"
	case graph_definition.FieldMetaType, "Field":
		return ":::field"
	case graph_definition.EnumInstanceMetaType, "EnumInstance":
		return ":::enum_instance"
	default:
		return ""
	}
}

// getArrowStyle 根据边类型获取箭头样式
func getArrowStyle(edgeType string) string {
	switch edgeType {
	case graph_definition.Extend, "Extend":
		return "-->"
	case graph_definition.Implement, "Implement":
		return "-.->"
	case graph_definition.ReturnTypeOf, "ReturnTypeOf":
		return "..>"
	case graph_definition.ReturnTypeArgument, "ReturnTypeArgument":
		return "..>"
	case graph_definition.HasMethod, "HasMethod":
		return "-->"
	case graph_definition.HasField, "HasField":
		return "-->"
	case graph_definition.FieldTypeOf, "FieldTypeOf":
		return "..>"
	case graph_definition.FieldTypeArgument, "FieldTypeArgument":
		return "..>"
	case graph_definition.ParamTypeOf, "ParamTypeOf":
		return "..>"
	case graph_definition.ParamTypeArgument, "ParamTypeArgument":
		return "..>"
	case graph_definition.MethodCall, "MethodCall":
		return "-->"
	case graph_definition.MethodReferenceClass, "MethodReferenceClass":
		return "-->"
	case graph_definition.MethodReferenceVariable, "MethodReferenceVariable":
		return "-->"
	case graph_definition.AnnotationBy, "AnnotationBy":
		return "-.->"
	case graph_definition.Expand, "Expand":
		return "-->"
	case graph_definition.TypeDefOf, "TypeDefOf":
		return "..>"
	case graph_definition.TypeDefArgument, "TypeDefArgument":
		return "..>"
	case graph_definition.MacrocDefOf, "MacrocDefOf":
		return "..>"
	case graph_definition.MacrocDefArgument, "MacrocDefArgument":
		return "..>"
	case graph_definition.Component, "Component":
		return "-->"
	case graph_definition.Override, "Override":
		return "-.->"
	default:
		return "-->"
	}
}
