package database

import (
	_ "embed"
)

//go:embed sql/db_init.sql
var DbInitSql string

//go:embed sql/db_vacuum.sql
var dbVacuumSql string

//go:embed sql/show_version.sql
var showVersionSql string

//go:embed sql/db_upgrade_session_add_orgId.sql
var dbUpgradeAddOrgIdSql string

//go:embed sql/db_upgrade_session_add_local_support.sql
var dbUpgradeAddLocalSupportSql string

//go:embed sql/db_upgrade_record_add_extra.sql
var dbUpgradeAddExtraSql string

//go:embed sql/db_upgrade_record_add_filter_status.sql
var dbUpgradeAddFilterStatusSql string

//go:embed sql/db_upgrade_record_add_system_role_content.sql
var dbUpgradeRecordAddSystemRoleContentSql string

//go:embed sql/db_upgrade_session_add_sessionType.sql
var dbUpgradeSessionAddSessionTypeSql string

//go:embed sql/db_upgrade_record_add_sessionType.sql
var dbUpgradeRecordAddSessionTypeSql string

//go:embed sql/db_upgrade_record_add_reasoning_content.sql
var dbUpgradeRecordAddReasoningContentSql string

//go:embed sql/db_upgrade_record_add_mode.sql
var dbUpgradeRecordAddModeSql string

//go:embed sql/db_upgrade_session_add_version.sql
var dbUpgradeSessionAddVersionSql string

//go:embed sql/db_upgrade_record_add_chat_prompt.sql
var dbUpgradeRecordAddChatPromptSql string

//go:embed sql/db_upgrade_agent_memory_add_quality_score.sql
var dbUpgradeLingmaMemoryAddQualityScoreSql string

//go:embed sql/db_upgrade_message_add_token_info.sql
var dbUpgradeMessageAddTokenInfoSql string

//go:embed sql/db_select_chat_record.sql
var selectChatRecordSql string

//go:embed sql/db_select_chat_session_with_id.sql
var selectChatSessionWithIdSql string

//go:embed sql/db_select_all_chat_sessions.sql
var selectChatSessionsSql string

//go:embed sql/db_select_all_v1_chat_sessions.sql
var selectV1ChatSessionsSql string

//go:embed sql/db_select_expired_chat_sessions.sql
var selectExpiredChatSessionsSql string

//go:embed sql/db_select_chat_record_with_sessionId.sql
var selectChatRecordWithSessionId string

//go:embed sql/db_select_chat_record_for_history_list_with_sessionId.sql
var selectChatRecordForHistoryListWithSessionId string
