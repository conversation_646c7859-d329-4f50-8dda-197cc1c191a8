
create table if not exists chat_session
(
    session_id  varchar(64) primary key,
    user_id   VARCHAR(64) not null ,
    user_name varchar(64),
    session_title varchar(256) not null ,
    project_id  varchar(64) not null ,
    project_uri varchar(512),
    project_name varchar(64),
    gmt_create INTEGER,
    gmt_modified INTEGER,
    org_id   VARCHAR(64) default '',
    session_type VARCHAR(64) DEFAULT '',
    mode VARCHAR(64) DEFAULT '',
    version VARCHAR(64) DEFAULT ''
);


create table if not exists chat_record
(
    request_id varchar(64) primary key ,
    session_id  varchar(64) not null,
    chat_task varchar(64) not null,
    chat_context text,
    system_role_content text,
    question text,
    answer text,
    like_status int,
    gmt_create INTEGER,
    gmt_modified INTEGER,
    finish_status INTEGER,
    filter_status VARCHAR(64) DEFAULT '',
    error_result VARCHAR(1024) DEFAULT '{}',
    code_language VARCHAR(62) DEFAULT '',
    extra text DEFAULT '{}',
    session_type VARCHAR(64) DEFAULT '',
    summary text DEFAULT '',
    intention_type VARCHAR(64) DEFAULT '',
    reasoning_content text,
    mode VARCHAR(64) DEFAULT '',
    chat_prompt text DEFAULT ''
) ;


create table if not exists chat_snapshot
(
    snapshot_id varchar(64) primary key,
    session_id  varchar(64) not null,
    chat_record_id varchar(64),
    status varchar(64),
    name varchar(64),
    description text,
    gmt_create INTEGER,
    gmt_modified INTEGER
);

create table if not exists chat_working_space_file
(
    item_id varchar(64) primary key,
    session_id varchar(64) not null,
    snapshot_id varchar(64) not null,
    file_id varchar(512),
    content_type varchar(64),
    type varchar(64),
    key varchar(512),
    status varchar(64),
    mode varchar(64),
    version varchar(64),
    local_id varchar(64),
    language varchar(64),
    extra text,
    gmt_create INTEGER,
    gmt_modified INTEGER
);

create table if not exists chat_working_space_file_reference
(
    id varchar(64) primary key,
    item_id varchar(64) not null,
    snapshot_id varchar(64) not null,
    file_id varchar(64) not null,
    mode varchar(64) not null
);

create table if not exists agent_memory
(
    id TEXT PRIMARY KEY,
    gmt_create INTEGER,
    gmt_modified INTEGER,
    scope TEXT NOT NULL,
    scope_id TEXT,
    keywords TEXT,
    title TEXT,
    content TEXT NOT NULL,
    session_id TEXT,
    is_merged INTEGER DEFAULT 0,
    freq INTEGER DEFAULT 0,
    source TEXT DEFAULT 'auto',
    token_count INTEGER DEFAULT 0,
    type TEXT NOT NULL,
    user_id TEXT,
    category TEXT,
    retention_score REAL DEFAULT 1.0,
    next_review_time INTEGER DEFAULT 0,
    last_review_time INTEGER DEFAULT 0,
    forget_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active',
    review_history TEXT,
    quality_score REAL DEFAULT 0.0
);

CREATE INDEX IF NOT EXISTS idx_agent_memory_user_id ON agent_memory (user_id);
CREATE INDEX IF NOT EXISTS idx_agent_memory_type ON agent_memory (type);
CREATE INDEX IF NOT EXISTS idx_agent_memory_scope_scope_id ON agent_memory (scope, scope_id);
CREATE INDEX IF NOT EXISTS idx_agent_memory_session_id ON agent_memory (session_id);

create table if not exists chat_message (
    id varchar(64) primary key,
    session_id VARCHAR(64),
    request_id VARCHAR(64),
    role       VARCHAR(64),
    content text,
    summary text,
    tool_result text,
    token_info text,
    gmt_create INTEGER
);

-- 创建 agent_wiki_repo 表
create table if not exists agent_wiki_repo
(
    id                   VARCHAR(64) primary key,
    workspace_path       VARCHAR(64),
    name                 VARCHAR(64),
    progress_status      VARCHAR(64),
    optimized_catalog    TEXT,
    current_document_structure TEXT default '',
    catalogue_think_content TEXT default '',
    recovery_checkpoint  VARCHAR(50),
    last_commit_id       VARCHAR(64),
    last_commit_update   TIMESTAMP,
    gmt_create           TIMESTAMP,
    gmt_modified         TIMESTAMP
    );

-- 添加 workspace_path 的唯一性约束
CREATE UNIQUE INDEX IF NOT EXISTS idx_agent_wiki_repo_workspace_path_unique ON agent_wiki_repo (workspace_path);

-- 创建 agent_wiki_readme 表
create table if not exists agent_wiki_readme
(
    id            VARCHAR(64) primary key,
    repo_id       VARCHAR(64),
    workspace_path VARCHAR(64),
    content       TEXT,
    gmt_create    TIMESTAMP,
    gmt_modified  TIMESTAMP
);

-- 创建 agent_wiki_overview 表
create table if not exists agent_wiki_overview
(
    id            VARCHAR(64) primary key,
    repo_id       VARCHAR(64),
    workspace_path VARCHAR(64),
    content       TEXT,
    gmt_create    TIMESTAMP,
    gmt_modified  TIMESTAMP
);

-- 创建 agent_wiki_item 表
create table if not exists agent_wiki_item
(
    id TEXT PRIMARY KEY,
    catalog_id TEXT NOT NULL,
    repo_id TEXT,
    content TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    extend TEXT,
    progress_status TEXT,
    workspace_path TEXT,
    gmt_create DATETIME NOT NULL,
    gmt_modified DATETIME NOT NULL,
    FOREIGN KEY (catalog_id) REFERENCES agent_wiki_catalog(id)
);

-- 创建 agent_wiki_catalog 表
create table if not exists agent_wiki_catalog
(
    id              VARCHAR(64) primary key,
    repo_id         VARCHAR(64),
    name            VARCHAR(64),
    description     TEXT,
    prompt          TEXT,
    `order`           INTEGER,
    parent_id       VARCHAR(64),
    progress_status VARCHAR(64),
    dependent_files TEXT,
    keywords        TEXT,
    workspace_path  VARCHAR(64),
    gmt_create      TIMESTAMP,
    gmt_modified    TIMESTAMP,
    raw_data        TEXT,
    layer_level     INTEGER
);

create table if not exists task_tree (
     session_id VARCHAR(64) PRIMARY KEY,
     task_tree_json TEXT NOT NULL,
     gmt_create INTEGER,
     gmt_modified INTEGER
);

CREATE INDEX IF NOT EXISTS idx_task_plan_session_id ON task_tree (session_id);
