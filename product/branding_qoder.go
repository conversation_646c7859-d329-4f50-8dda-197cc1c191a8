//go:build qoder

package product

import (
	"fmt"
	"strconv"
	"strings"
)

var messages map[string]string

type BrandingSupporter struct {
}

func init() {
	messages = map[string]string{
		KeyCmdName:       "qoder",
		KeyCmdShort:      "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyCmdLong:       "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyVersionPrefix: "Qoder",
		KeyAboutMessage:  "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",

		// Start command related
		KeyStartShort:      "Start Qoder and serve",
		KeyStartLong:       "Start Qoder and serve",
		KeyStartComment:    "Start qoder server",
		KeyStartServiceLog: "===== Starting qoder service =====",
		KeyHomePath:        "Qoder home: {}",
		KeyCachePath:       "Qoder cache path: {}",
		KeyServerRunning:   "Qoder running on {}({}, {}) at {}, version: {}",
	}
}

// GetMessage 基于args渲染message
// message 格式，支持{}占位符，从左到右替换参数和渲染
// 参考 "Start {} service success."
func (b *BrandingSupporter) GetMessage(key string, args ...interface{}) string {
	var m string
	if msg, exists := messages[key]; exists {
		m = msg
	}
	if m != "" && len(args) > 0 {
		// 使用从左到右的顺序替换{}占位符
		for _, arg := range args {
			placeholder := "{}"
			if strings.Contains(m, placeholder) {
				// 将参数转换为字符串
				var argStr string
				switch v := arg.(type) {
				case string:
					argStr = v
				case int:
					argStr = strconv.Itoa(v)
				case int64:
					argStr = strconv.FormatInt(v, 10)
				case float64:
					argStr = strconv.FormatFloat(v, 'f', -1, 64)
				case bool:
					argStr = strconv.FormatBool(v)
				default:
					argStr = fmt.Sprintf("%v", v)
				}
				// 替换第一个{}占位符
				m = strings.Replace(m, placeholder, argStr, 1)
			} else {
				// 如果没有更多的占位符，停止替换
				break
			}
		}
	}
	return m
}
