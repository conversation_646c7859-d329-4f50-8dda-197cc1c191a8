package codebase

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestReadFileTool_DependencyTokenLimit 测试依赖信息token限制功能
func TestReadFileTool_DependencyTokenLimit(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)

	// 测试默认token限制（500）
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
		// 不设置DependencyTokenLimit，使用默认值500
	})

	// 验证工具创建成功
	assert.NotNil(t, tool)

	// 测试自定义token限制
	customTokenLimit := 100
	toolWithCustomLimit, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath:        testDir,
		DependencyTokenLimit: customTokenLimit,
	})

	assert.NotNil(t, toolWithCustomLimit)
}

// TestTruncateDependencies 测试truncateDependencies方法
func TestTruncateDependencies(t *testing.T) {
	reader := &fileReader{
		workspacePath:        "/test",
		maxLineCount:         300,
		dependencyTokenLimit: 10, // 设置很小的限制用于测试
	}

	// 测试空依赖
	result := reader.truncateDependencies("")
	assert.Equal(t, "", result)

	// 测试短依赖（不超过限制）
	shortDep := "simple"
	result = reader.truncateDependencies(shortDep)
	assert.Equal(t, shortDep, result)

	// 测试长依赖（需要截断） - 创建一个肯定会超过10个token的长字符串
	// 使用simpleascii tokenizer: 大约3个字符 = 1个token，所以10个token = 30个字符
	longDep := strings.Repeat("This is a very long dependency information that should be truncated because it exceeds the token limit. ", 10)
	result = reader.truncateDependencies(longDep)

	// 添加debug输出
	t.Logf("longDep length: %d", len(longDep))
	t.Logf("result: %q", result)
	t.Logf("result length: %d", len(result))

	// 使用simpleascii tokenizer，逻辑是确定的
	// 长依赖应该被截断并包含截断提示
	assert.NotEqual(t, longDep, result, "长依赖应该被截断")
	assert.Contains(t, result, "truncated", "应该包含截断提示")
}

// TestTruncateDependencies_WithDifferentLimits 测试不同token限制下的截断行为
func TestTruncateDependencies_WithDifferentLimits(t *testing.T) {
	testCases := []struct {
		name       string
		tokenLimit int
		input      string
	}{
		{
			name:       "very_small_limit",
			tokenLimit: 5,
			input:      "This is a test dependency that should be truncated because it's too long and exceeds the very small token limit",
		},
		{
			name:       "medium_limit",
			tokenLimit: 20,
			input:      strings.Repeat("dependency info that is quite long and should definitely exceed the medium token limit ", 10),
		},
		{
			name:       "large_limit",
			tokenLimit: 1000,
			input:      "short dependency",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reader := &fileReader{
				workspacePath:        "/test",
				maxLineCount:         300,
				dependencyTokenLimit: tc.tokenLimit,
			}

			result := reader.truncateDependencies(tc.input)

			// 如果输入很短，应该不被截断
			if tc.tokenLimit >= 1000 && tc.input == "short dependency" {
				assert.Equal(t, tc.input, result)
				assert.NotContains(t, result, "truncated")
			} else {
				// 长输入应该被处理
				if tc.tokenLimit < 100 { // 小于token限制的情况下应该被处理
					assert.NotEqual(t, tc.input, result) // 结果应该与输入不同
					assert.Contains(t, result, "truncated", "应该包含截断提示")
				}
			}
		})
	}
}

// TestTruncateDependencies_PreservesLineIntegrity 测试截断保持行的完整性
func TestTruncateDependencies_PreservesLineIntegrity(t *testing.T) {
	reader := &fileReader{
		workspacePath:        "/test",
		maxLineCount:         300,
		dependencyTokenLimit: 10, // 设置很小的限制用于测试
	}

	// 创建多行依赖信息
	multiLineDep := "Line 1: First dependency\nLine 2: Second dependency\nLine 3: Third dependency\nLine 4: Fourth dependency\nLine 5: Fifth dependency"

	result := reader.truncateDependencies(multiLineDep)

	// 验证结果不是空的
	assert.NotEmpty(t, result)

	// 如果被处理，结果应该与原始输入不同
	if len(result) < len(multiLineDep) {
		assert.NotEqual(t, multiLineDep, result)
	}
}
