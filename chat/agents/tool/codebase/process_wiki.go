package codebase

import (
	"cosy/deepwiki/storage"
	"cosy/lang/indexer"
	"cosy/log"
	"strings"
)

// ProcessWikiChunks 处理 Wiki chunks，提取引用的文件路径
func ProcessWikiChunks(chunks []indexer.CodeChunk, projectPath string) map[string]float64 {
	relevantFiles := make(map[string]float64)

	for _, chunk := range chunks {
		chunkId := strings.TrimSuffix(chunk.FilePath, ".md")
		chunkMetadata, err := storage.GlobalStorageService.GetWikiItemByID(chunkId)
		if err != nil {
			log.Debugf("Failed to get wiki item by id %s: %v", chunk.Id, err)
			continue
		}
		if chunkMetadata == nil {
			log.Debugf("Wiki item not found for ID: %s", chunk.Id)
			continue
		}

		score := chunk.Score
		// 读取 Markdown 文件内容
		content := chunkMetadata.Content

		// 从 chunk 的行号开始向下搜索引用
		lines := strings.Split(content, "\n")
		startLine := int(chunk.StartLine)
		endLine := int(chunk.EndLine)
		if startLine >= len(lines) {
			continue
		}

		// 从 chunk 开始位置向下搜索，直到遇到下一个标题或文件末尾
		referencedPaths := extractReferencedPaths(lines, startLine, endLine, projectPath)

		if len(referencedPaths) > 0 {
			for _, path := range referencedPaths {
				relevantFiles[path] = score
			}
		}
	}

	return relevantFiles
}

func extractReferencedPaths(lines []string, startLine, endLine int, projectPath string) []string {
	var paths []string

	// 首先在chunk内部搜索
	for i := startLine; i <= endLine && i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if strings.Contains(line, projectPath) {
			path := extractFilePathFromMarkdownLink(line, projectPath)
			if path != "" {
				paths = append(paths, path)
			}
		}
	}

	if len(paths) == 0 {
		for i := endLine + 1; i < len(lines); i++ {
			line := strings.TrimSpace(lines[i])

			if strings.HasPrefix(line, "#") {
				break
			}

			if strings.HasPrefix(line, "**Sources for this") {
				for j := i + 1; j < len(lines); j++ {
					nextLine := strings.TrimSpace(lines[j])

					if !strings.Contains(nextLine, "file:/") {
						break
					}

					path := extractFilePathFromMarkdownLink(nextLine, projectPath)
					if path != "" {
						paths = append(paths, path)
					}
				}
				break
			}
		}
	}

	return paths
}

func extractFilePathFromMarkdownLink(line, projectPath string) string {
	// 首先尝试查找项目路径的位置
	projectIdx := strings.Index(line, projectPath)
	if projectIdx != -1 {
		// 找到项目路径，使用原有逻辑
		startIdx := projectIdx + len(projectPath)
		if startIdx >= len(line) {
			return ""
		}

		// 跳过路径分隔符
		if line[startIdx] == '/' {
			startIdx++
		}

		endIdx := strings.Index(line[startIdx:], "#")
		if endIdx != -1 {
			endIdx = startIdx + endIdx
		} else {
			endIdx = strings.Index(line[startIdx:], ")")
			if endIdx != -1 {
				endIdx = startIdx + endIdx
			} else {
				return ""
			}
		}

		filePath := line[startIdx:endIdx]
		return filePath
	}

	// 如果没有找到项目路径，尝试查找 file://
	fileProtocolIdx := strings.Index(line, "file://")
	if fileProtocolIdx == -1 {
		return ""
	}

	// 从 file:// 后开始提取文件路径
	startIdx := fileProtocolIdx + len("file://")
	if startIdx >= len(line) {
		return ""
	}

	// 查找结束位置：优先查找 # 锚点，然后查找 ) 括号
	endIdx := strings.Index(line[startIdx:], "#")
	if endIdx != -1 {
		endIdx = startIdx + endIdx
	} else {
		endIdx = strings.Index(line[startIdx:], ")")
		if endIdx != -1 {
			endIdx = startIdx + endIdx
		} else {
			// 没找到明确的结束符，取到行尾
			endIdx = len(line)
		}
	}

	filePath := line[startIdx:endIdx]
	return strings.TrimSpace(filePath)
}
