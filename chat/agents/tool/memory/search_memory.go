package memory

import (
	"context"
	common2 "cosy/chat/chains/common"
	cosyDefinition "cosy/definition"
	"cosy/sls"
	"fmt"
	"strconv"
	"strings"

	"cosy/chat/agents/tool/common"
	"cosy/codebase/semantic"
	"cosy/components"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type SearchMemoryConfig struct {
	WorkspacePath   string                     // 工作空间路径
	FileIndexer     *indexing.ProjectFileIndex // 文件索引器
	Embedder        *components.LingmaEmbedder // 嵌入模型
	ExplanationDesc string                     // explanation字段的描述
}

type SearchMemoryRequest struct {
	Query string `json:"query"` // 查询字符串
	TopK  int    `json:"top_k"` // 返回结果数量，默认10
}

type SearchMemoryResponse struct {
	CodeChunks []indexer.CodeChunk `json:"code_chunks"`
	Message    string              `json:"message"`
}

func NewSearchMemoryTool(config *SearchMemoryConfig) (tool.InvokableTool, error) {
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "search_memory"
	toolDesc := `Search and retrieve relevant codebase memory and knowledge content using advanced semantic search.

WHEN TO USE THIS TOOL:
- User asks questions that require finding information across multiple wiki documents
- User wants to search for content by topics, concepts, or keywords rather than specific document names
- The query is exploratory (e.g., "how to...", "what is...", "explain...")
- You need to find the most relevant codebase information
- User asks about concepts, procedures, or information that might be scattered across different documents
- The query requires understanding context and semantic meaning
- Users require added features, fixed defects, optimized code, implemented functions, etc.

WHEN NOT TO USE THIS TOOL:
- The known context information is already very clear and sufficient to complete the task
- User questions unrelated to the code repository
- The task is too simple, no need to acquire codebase knowledge

EXAMPLES OF APPROPRIATE QUERIES:
- "How do I implement user authentication in this system?"
- "What are the best practices for API security?"
- "Find information about database configuration"
- "How to troubleshoot login issues?"
- "What deployment options are available?"
- "Explain the architecture of this system"

TECHNICAL DETAILS:
This tool uses advanced RAG (Retrieval-Augmented Generation) with hybrid search combining:
- Text-based search for exact keyword matches
- Vector-based semantic search for conceptual understanding  
- LLM-powered reranking to ensure the most relevant results
- Smart scoring to return only high-quality, relevant content

The tool excels at finding relevant information when you don't know exactly where to look, making it perfect for exploratory queries and knowledge discovery.
`

	toolParams := &agentDefinition.Schema{
		Type: "object",
		Properties: map[string]*agentDefinition.Schema{
			"query": {
				Type:        "string",
				Description: "The search query to find relevant wiki content.",
			},
			"top_k": {
				Type:        "integer",
				Description: "Number of top results to return. Default is 10.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"query"},
	}

	toolInfo := &agentDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}

	toolInst := &SearchMemoryTool{
		workspacePath: config.WorkspacePath,
		fileIndexer:   config.FileIndexer,
		embedder:      config.Embedder,
	}

	return tool.NewInvokableTool(toolInfo, toolInst.queryWiki, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type SearchMemoryTool struct {
	workspacePath string
	fileIndexer   *indexing.ProjectFileIndex
	embedder      *components.LingmaEmbedder
}

func QueryWikiContent(ctx context.Context, workspacePath string, fileIndexer *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder, query string, topK int) (*SearchMemoryResponse, error) {
	// 创建临时工具实例
	tool := &SearchMemoryTool{
		workspacePath: workspacePath,
		fileIndexer:   fileIndexer,
		embedder:      embedder,
	}

	// 构建请求
	request := &SearchMemoryRequest{
		Query: query,
		TopK:  topK,
	}

	// 调用查询方法
	return tool.queryWiki(ctx, request)
}

func (w *SearchMemoryTool) queryWiki(ctx context.Context, request *SearchMemoryRequest) (*SearchMemoryResponse, error) {
	log.Infof("call queryWiki, request: %+v", request)
	// 使用提供的路径或默认工作空间路径
	targetPath := w.workspacePath

	if request.Query == "" {
		return &SearchMemoryResponse{
			CodeChunks: []indexer.CodeChunk{},
			Message:    "No query provided",
		}, nil
	}

	// 设置默认 topK
	topK := request.TopK
	if topK <= 0 {
		topK = 10
	}
	if topK > 50 {
		topK = 50
	}

	// 创建 RagOperator
	ragOperator := semantic.NewRagOperator(w.fileIndexer, w.embedder)

	// 构建查询
	query := semantic.RetrieveQuery{
		RawQuery:     request.Query,
		RefinedQuery: request.Query,
		Keywords:     strings.Split(request.Query, " "),
		CodeCategory: indexer.NormalCategory,
	}

	// 设置检索选项
	options := semantic.RetrieveOptions{
		Strategy:                semantic.HybridLLMRerank,
		VectorScoreThreshold:    0.5,
		RerankScoreThreshold:    0.5,
		LLMRerankScoreThreshold: 0.1,
		FilePathPattern:         "",
		RelevantFiles:           []string{},
		ModeType:                semantic.MemoryMode,
	}

	// 执行语义检索
	chunks, err := ragOperator.Retrieve(ctx, query, targetPath, topK, options)
	if err != nil {
		return nil, fmt.Errorf("failed to query wiki content: %w", err)
	}

	message := fmt.Sprintf("Found %d relevant chunks for query: '%s'", len(chunks), request.Query)
	var requestId, sessionId string
	if askParamsValue := ctx.Value(common2.KeyChatAskParams); askParamsValue != nil {
		if rawInputParams, ok := askParamsValue.(*cosyDefinition.AskParams); ok {
			requestId = rawInputParams.RequestId
			sessionId = rawInputParams.SessionId
		} else {
			log.Debugf("KeyChatAskParams context value type assertion failed, expected *cosyDefinition.AskParams, got %T", askParamsValue)
		}
	} else {
		log.Debug("KeyChatAskParams not found in context, using empty requestId and sessionId")
	}
	sls.Report(sls.EventTypeSearchMemoryTool, requestId, map[string]string{
		"chunk_fetch_count": strconv.Itoa(len(chunks)),
		"query":             request.Query,
		"top_k":             strconv.Itoa(topK),
		"session_id":        sessionId,
	})

	return &SearchMemoryResponse{
		CodeChunks: chunks,
		Message:    message,
	}, nil
}

func (w *SearchMemoryTool) convertOutput(ctx context.Context, output interface{}) (*agentDefinition.ToolOutput, error) {
	response, ok := output.(*SearchMemoryResponse)
	if !ok {
		return nil, fmt.Errorf("expected *SearchMemoryResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString(response.Message)
	outputBuilder.WriteString("\n")

	if len(response.CodeChunks) > 0 {
		outputBuilder.WriteString(fmt.Sprintf("\nRetrieved %d chunks:\n", len(response.CodeChunks)))
		for i, chunk := range response.CodeChunks {
			outputBuilder.WriteString(fmt.Sprintf("%d. File: %s", i+1, chunk.FilePath))
			if chunk.Score > 0 {
				outputBuilder.WriteString(fmt.Sprintf(" (Score: %.3f)", chunk.Score))
			}
			outputBuilder.WriteString("\n")

			if chunk.StartLine > 0 && chunk.EndLine > 0 {
				outputBuilder.WriteString(fmt.Sprintf("   Lines: %d-%d\n", chunk.StartLine, chunk.EndLine))
			}

			if chunk.Type != "" {
				outputBuilder.WriteString(fmt.Sprintf("   Type: %s\n", chunk.Type))
			}

			outputBuilder.WriteString(fmt.Sprintf("   Content: %s\n", chunk.Content))
			outputBuilder.WriteString("\n")
		}
	} else {
		outputBuilder.WriteString("\nNo relevant content found.\n")
	}

	return &agentDefinition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
