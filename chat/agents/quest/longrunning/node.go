package longrunning

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	masterCommon "cosy/chat/agents/quest/common"
	masterSupport "cosy/chat/agents/quest/support"
	"cosy/chat/agents/support"
	agentSupport "cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/extension/plan"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"errors"
	"strings"

	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		requestId := rawInputParams.RequestId
		systemPrompt, userPrompt := BuildPrompt(ctx, rawInputParams, agentState.Inputs)
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[long_running_agent] message, requestId=%s, systemMessage=%+v", requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)

		history, ok := masterSupport.GetLongRunningSessionHistory(ctx, sessionId)
		if ok && len(history) > 0 {
			// 把历史消息保存到chat_message中，系统指令排除
			shortTermMemory.AppendMessages(history...)
		}

		userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
		// 判断是否有图片
		contextProviderExtras := agentState.Inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			imageUrl := imageUrls[0]
			chatPartText := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeText,
				Text: userPrompt,
			}
			chatPartImage := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeImageURL,
				ImageURL: &agentDefinition.ChatMessageImageURL{
					URL:      imageUrl,
					MIMEType: "image/png",
				},
			}
			multiContent := []agentDefinition.ChatMessagePart{chatPartText, chatPartImage}
			userMessage.MultiContent = multiContent
		}
		shortTermMemory.AddMessage(userMessage)
		masterSupport.SaveMessageHistory(agentState, userMessage)
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := support.ConvertToModelToolParam(ctx, agentContext.Tools)
		toolParams = append(toolParams, mcp.ListMcpTools()...)

		toolsToken, _ := support.GetToolsToken(toolParams)
		//发起llm调用前做上下文长度进行处理
		//messages, _ := coder.TruncateMessages(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId)
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId, toolsToken)

		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, "")
		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}

		sseCtx, cancelFunc := context.WithCancel(ctx)
		syncer := masterSupport.LLMResponseHandler{
			SessionType:         sessionType,
			SessionId:           sessionId,
			RequestId:           requestId,
			ToolCallCount:       agentState.ToolCallCount,
			CtxForClient:        agentState.CtxForClient,
			CancelFunc:          cancelFunc,
			CallServerRequestId: callServerRequestId,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.SyncContent, syncer.SyncToolCall)
		if err != nil {
			log.Infof("[long_running_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
			syncer.SyncEndAndFillTime(response)
		} else {
			syncer.PostSyncToolCall(sseCtx, response)
			syncer.SyncEndAndFillTime(response)
			log.Infof("[long_running_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
				}
			}
		}
		if err != nil {
			syncer.PostSyncToolCallOnError(sseCtx, response)
		}
		if err != nil {
			// 调用llm失败，转换一下错误，通过err退出
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		masterSupport.SaveMessageHistory(agentState, lastMessage)
		agentState.ShortTermMemory.AddMessage(lastMessage)
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		// 取消工具执行，返回工具取消执行的消息
		messages := make([]*agentDefinition.Message, 0, 2)
		toolCall := lastMessage.ToolCalls[0]
		toolCallResult := "The tool invocation was canceled because it reached the limit. If the user continues, you can re-initiate the tool call."
		callResponse := &agentDefinition.Message{
			Role:       agentDefinition.RoleTypeTool,
			ToolCallID: toolCall.ID,
			Name:       toolCall.Function.Name,
			Content:    toolCallResult,
		}
		messages = append(messages, callResponse)
		for _, message := range messages {
			masterSupport.SaveMessageHistory(agentState, message)
		}
		agentState.ShortTermMemory.AppendMessages(messages...)
		return agentState, cosyErrors.New(cosyErrors.ToolCallOverLimit, "tool call over limit")
	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		availableTools := agentContext.Tools

		coderAgentState := &coderCommon.CoderAgentState{
			Inputs:          agentState.Inputs,
			ShortTermMemory: agentState.ShortTermMemory,
			CtxForClient:    agentState.CtxForClient,
		}
		toolMessages := support.ExecuteTool(ctx, availableTools, coderAgentState, support.DefaultAgentToolParamSupplier, nil)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			masterSupport.SaveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		agentState.ShortTermMemory.AppendMessages(toolMessages...)
		return agentState, nil
	}))

var handleUserRequestNode = graph.NewNode(
	HandleUserRequestNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		historyMessages := agentState.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		assistantMessage := lastTwoMessages[0]
		if assistantMessage.ToolCalls != nil {
			toolCall := assistantMessage.ToolCalls[0]
			if toolCall.Function.Name == "ask_user" {
				agentState.Extra[StateExtraExitForMessage] = true
				taskId, _ := config.GetRemoteModeTaskId()
				if taskId != "" {
					longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusActionRequired, nil)
				}
				return input, nil
			}
		}
		agentStateInChan := agentState.Extra["agent_state_in_chan"].(chan agentSupport.StateInNotice)
		notice := agentSupport.StateInNotice{}
		select {
		case inNotice := <-agentStateInChan:
			log.Debugf("receive notice: %+v", inNotice)
			notice = inNotice
		default:
			log.Debugf("no notice")
			return input, nil
		}
		if notice.Type == agentSupport.StateInNoticeTypePause {
			// 处理暂停
			taskId, _ := config.GetRemoteModeTaskId()
			if taskId != "" {
				longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusPaused, nil)
			}
			for {
				select {
				case inNotice := <-agentStateInChan:
					log.Debugf("receive notice: %+v", inNotice)
					if inNotice.Type == agentSupport.StateInNoticeTypeResume {
						log.Debugf("resume")
						if taskId != "" {
							longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusRunning, nil)
						}
						return input, nil
					} else if inNotice.Type == agentSupport.StateInNoticeTypeExitForMessage {
						log.Debugf("exit for message")
						agentState.Extra[StateExtraExitForMessage] = true
						return input, nil
					}
				case <-ctx.Done():
					return input, nil
				}
			}
		} else if notice.Type == agentSupport.StateInNoticeTypeExitForMessage {
			// 处理退出
			log.Debugf("exit for message")
			agentState.Extra[StateExtraExitForMessage] = true
			return input, nil
		}
		// 其他消息不处理
		return input, nil
	}))

var checkNode = graph.NewNode(
	CheckNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		hasFinishPlan := plan.HasAllTasksFinished(sessionId)
		if hasFinishPlan {
			agentState.Extra[StateExtraHasPlanFinish] = true
		} else {
			agentState.Extra[StateExtraHasPlanFinish] = false
			builder := strings.Builder{}
			builder.WriteString("The tasks have not been fully completed, please check the task list and continue with the execution.")
			message := &agentDefinition.Message{
				Role:    agentDefinition.RoleTypeUser,
				Content: builder.String(),
			}
			agentState.ShortTermMemory.AppendMessages(message)
		}
		return agentState, nil
	}))

// 总结agent输出，要求llm按一定格式要求进行总结
var summaryNode = graph.NewNode(
	SummaryNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		// 创建一个goroutine，用于同步工作区文件变动
		util.GoSafeRoutine(longruntask.UploadGitFileChanges)
		agentState := input.(*masterCommon.MasterAgentState)
		//发起llm调用前做上下文长度进行处理
		userQuery := `
		Please make a summary report， it should be structured as follows:
Context: The context to continue the conversation with. If applicable based on the current task, this should include:
1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.

2. Verification: List all the done verification or how to verify step by step.

Example summary structure:
# Summary:
[Detailed description]
# Verification:
- [Verify 1 details]
- [Verify 2 details ]

Output only the summary of the conversation so far, without any additional commentary or explanation."
`
		userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userQuery}
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId, 0)
		messages = append(messages, userMessage)

		var toolParams []agentLlms.Tool
		////agent下配置mcp tools
		mode := rawInputParams.Mode
		//if mode == definition.SessionModeAgent {
		//	toolParams = append(toolParams, mcp.ListMcpTools()...)
		//}
		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		//sessionId := rawInputParams.SessionId
		//sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, mode)
		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		sseCtx, _ := context.WithCancel(ctx)
		// TODO 临时推流的会话窗口，本地调试效果用
		//tmpSyncer := support.LLMResponseHandler{
		//	SessionType:   sessionType,
		//	SessionId:     sessionId,
		//	RequestId:     requestId,
		//	ToolCallCount: agentState.ToolCallCount,
		//	CtxForClient:  agentState.CtxForClient,
		//	//CancelFunc:    cancelFunc,
		//}
		// TODO 实现单独的summary到服务端的推流，意义可能不大
		syncer := masterSupport.SummaryLLMResponseSyncer{}
		remoteAsk.RequestId = callServerRequestId
		response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.SyncContent, syncer.SyncToolCall)
		if err != nil {
			log.Infof("[common_dev_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			//syncer.PostSyncToolCall(sseCtx, response)
			log.Infof("[common_dev_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				err = cosyErrors.New(cosyErrors.SystemError, response.Content)
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
				}
			}
		}
		if err != nil {
			// 调用llm失败，转换一下错误，通过err退出
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		syncer.SyncPostSummary(ctx, lastMessage)
		return agentState, nil
	}))

var finishNode = graph.NewNode(
	FinishNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*masterCommon.MasterAgentState)
		return agentState, nil
	}))
