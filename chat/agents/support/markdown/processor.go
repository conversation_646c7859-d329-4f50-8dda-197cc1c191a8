// Package markdown provides utilities for Markdown processing
package markdown

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/codebase/file"
	"cosy/codebase/symbol"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"cosy/util/session"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
)

// StreamProcessor handles the processing of streaming content to protect Markdown links
type StreamProcessor struct {
	originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error
	requestId    string
	buffer       strings.Builder
	debugCounter int // Counter for tracking processing operations
	mu           sync.Mutex
}

// NewStreamProcessor creates a new stream processor for the given request
func NewStreamProcessor(requestId string, originalFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error) *StreamProcessor {
	return &StreamProcessor{
		originalFunc: originalFunc,
		requestId:    requestId,
		debugCounter: 0,
	}
}

// ProcessStreamContent processes incoming stream content, handling backtick format
// and converting it to markdown links
func (p *StreamProcessor) ProcessStreamContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// If no content or processor is disabled, pass through directly
	if len(chunk) == 0 || !IsEnabled() {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// Check if this is a content chunk that we should process
	if contentType != definition.StreamingContentTypeContent {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// 将当前块转为字符串
	chunkStr := string(chunk)

	// 检查当前块是否包含反引号
	hasBacktick := strings.Contains(chunkStr, "`")

	// 如果当前块没有反引号，且缓冲区为空，直接发送
	// 这样可以避免处理不需要的内容，提高性能
	if !hasBacktick && p.buffer.Len() == 0 {
		return p.originalFunc(ctx, contentType, chunk)
	}

	// Add current chunk to buffer
	p.buffer.Write(chunk)

	// Get the current buffer content
	content := p.buffer.String()

	// 检查是否包含三个连续的反引号（代码块标记）
	if strings.Contains(content, "```") {
		// 如果包含```，立即处理并发送，不等待配对
		processedContent := p.processBackticks(ctx, content)
		p.buffer.Reset()
		p.debugCounter++
		return p.originalFunc(ctx, contentType, []byte(processedContent))
	}

	// Count backticks in the buffer
	backtickCount := strings.Count(content, "`")

	// 如果没有反引号，直接处理并发送
	if backtickCount == 0 {
		// 无需处理，直接发送
		resultContent := content
		p.buffer.Reset()
		p.debugCounter++
		return p.originalFunc(ctx, contentType, []byte(resultContent))
	}

	// If we have an even number of backticks, we can process the content
	if backtickCount > 0 && backtickCount%2 == 0 {
		// Process the buffer content to replace backtick pairs with links
		processedContent := p.processBackticks(ctx, content)

		// Reset the buffer after processing
		p.buffer.Reset()

		// Send the processed content
		p.debugCounter++
		return p.originalFunc(ctx, contentType, []byte(processedContent))
	}

	// If we have an ongoing content that's getting large, process what we can
	if p.buffer.Len() > 200 {
		return p.FlushBuffer(ctx)
	}

	// If we reach here, we're still collecting backtick content, so don't send anything yet
	return nil
}

// processBackticks processes all backtick pairs in the content and replaces them with links
func (p *StreamProcessor) processBackticks(ctx context.Context, content string) string {
	return ProcessBackticks(ctx, content)
}

func ProcessBackticks(ctx context.Context, content string) string {
	// 不检测代码块，直接处理所有内容
	return processBackticksInText(ctx, content)
}

// processBackticksInText 处理普通文本中的反引号（不包含代码块）
func processBackticksInText(ctx context.Context, content string) string {
	// 先替换三个连续的反引号为特殊标记，避免干扰单个反引号对的匹配
	placeholder := "___TRIPLE_BACKTICK___"
	tempContent := strings.ReplaceAll(content, "```", placeholder)

	// 使用正则表达式匹配单个反引号对，增加长度限制到100字符
	re := regexp.MustCompile("`([^`]{1,100})`")

	// Replace all backtick pairs with links
	processedContent := re.ReplaceAllStringFunc(tempContent, func(match string) string {
		// 获取匹配在原文中的位置
		matchIndex := strings.Index(tempContent, match)
		if matchIndex == -1 {
			return match
		}

		// 检查是否在超链接格式中
		if isInHyperlinkFormat(tempContent, match) {
			return match
		}

		// 检查是否可能是未完成的markdown链接的一部分
		if isPotentialIncompleteMarkdownLink(tempContent, matchIndex, match) {
			return match
		}

		// Extract the content between backticks
		innerContent := match[1 : len(match)-1]

		// Skip empty content or content that is just whitespace
		if strings.TrimSpace(innerContent) == "" {
			return match
		}

		// 检查内容是否已经是markdown链接格式 [text](url)
		if isMarkdownLink(innerContent) {
			return match
		}

		// 增强的代码符号识别逻辑
		if !isEnhancedCodeSymbol(innerContent) {
			return match
		}

		// Convert to markdown link
		url := GetSymbolURL(ctx, innerContent)
		if url == "" {
			// 如果无法获取URL或symbolKey为空时，返回原始内容而不是仅返回innerContent
			// 这样可以保留原始的反引号格式
			return match
		}
		return "[" + innerContent + "](" + url + ")"
	})

	// 恢复三个反引号
	processedContent = strings.ReplaceAll(processedContent, placeholder, "```")

	return processedContent
}

// isPotentialIncompleteMarkdownLink 检查反引号是否可能是未完成的markdown链接的一部分
func isPotentialIncompleteMarkdownLink(content string, matchIndex int, match string) bool {
	// 检查反引号前是否有左方括号
	beforeMatch := content[:matchIndex]
	afterMatch := content[matchIndex+len(match):]

	// 情况1: `xxx`前面有未闭合的[
	// 例如: "[Search`Metrics`" 或 "[`SearchMetrics`"
	lastOpenBracket := strings.LastIndex(beforeMatch, "[")
	lastCloseBracket := strings.LastIndex(beforeMatch, "]")

	// 如果最后一个[在]之后，说明有未闭合的[
	if lastOpenBracket > lastCloseBracket {
		// 检查这个[后面是否可能会形成完整的链接
		// 如果后面没有]或](，那么这可能是一个未完成的链接
		if !strings.Contains(afterMatch, "](") && !strings.Contains(afterMatch, "]") {
			// 但如果内容本身就是完整的链接格式，则不认为是未完成的
			if !isMarkdownLink(match[1 : len(match)-1]) {
				return true
			}
		}
	}

	// 情况2: 检查是否是 ](`xxx` 的模式（链接URL部分）
	// 例如: "](file://`path`)"
	if strings.HasSuffix(beforeMatch, "](") || strings.HasSuffix(beforeMatch, "](file://") ||
		strings.HasSuffix(beforeMatch, "](http://") || strings.HasSuffix(beforeMatch, "](https://") {
		return true
	}

	// 情况3: 检查反引号后面是否紧跟着 ](
	// 例如: "xxx`](xxxx" - 这种情况下反引号可能是链接文本的结束部分
	if strings.HasPrefix(afterMatch, "](") {
		return true
	}

	// 情况4: 检查是否是 [`xxxx 的开始模式（没有结束）
	// 例如: "[`SearchMetrics" （流式输出中断）
	if strings.HasSuffix(beforeMatch, "[") && !strings.Contains(afterMatch, "]") {
		return true
	}

	// 情况5: 检查反引号前后的模式，判断是否可能是不完整的markdown链接
	// 例如: "Search`](" 或 "Metrics`]"
	if strings.HasPrefix(afterMatch, "]") || strings.HasPrefix(afterMatch, "](") {
		// 反引号后面紧跟着 ] 或 ](，可能是链接文本的一部分
		return true
	}

	// 情况6: 检查整个内容是否暗示这是流式输入的片段
	// 如果反引号内容看起来像是路径或URL的一部分
	innerContent := match[1 : len(match)-1]
	if strings.HasPrefix(innerContent, "[") && !strings.Contains(innerContent, "]") {
		// 内容以[开头但没有结束]，可能是未完成的链接文本
		return true
	}

	// 情况7: 检查是否可能是URL的一部分
	// 例如: "/path/to/`file`" 或 "http://example.com/`path`"
	if len(beforeMatch) > 0 {
		// 检查前面是否是路径分隔符或URL的一部分
		lastChar := beforeMatch[len(beforeMatch)-1]
		if lastChar == '/' || lastChar == ':' || lastChar == '.' {
			return true
		}

		// 检查前面是否包含常见的URL协议
		if strings.Contains(beforeMatch, "://") || strings.Contains(beforeMatch, "file://") {
			// 可能是URL中的反引号
			return true
		}
	}

	// 情况8: 检查是否在圆括号内（可能是URL部分）
	// 计算未闭合的圆括号
	openParenCount := strings.Count(beforeMatch, "(") - strings.Count(beforeMatch, ")")
	if openParenCount > 0 {
		// 有未闭合的圆括号，可能在URL部分
		// 进一步检查是否前面有 ](
		if strings.Contains(beforeMatch, "](") {
			lastBracketParen := strings.LastIndex(beforeMatch, "](")
			lastCloseParen := strings.LastIndex(beforeMatch, ")")
			if lastBracketParen > lastCloseParen {
				// ]( 之后没有闭合的 )，说明在URL部分
				return true
			}
		}
	}

	return false
}

// isMarkdownLink 检查内容是否已经是markdown链接格式 [text](url)
func isMarkdownLink(content string) bool {
	// 使用正则表达式检查是否匹配 [text](url) 格式
	linkPattern := regexp.MustCompile(`^\[.*\]\(.*\)$`)
	return linkPattern.MatchString(content)
}

// isInHyperlinkFormat 检查反引号是否在超链接格式中
func isInHyperlinkFormat(content, match string) bool {
	// 查找当前匹配在内容中的位置
	matchIndex := strings.Index(content, match)
	if matchIndex == -1 {
		return false
	}

	// 检查方括号格式：[xxx`yyy`zzz](url) 或 [`xxx`](url)
	// 查找最近的左方括号
	leftBracketIndex := -1
	for i := matchIndex - 1; i >= 0; i-- {
		if content[i] == '[' {
			leftBracketIndex = i
			break
		} else if content[i] == ']' {
			// 如果先遇到右方括号，说明不在同一个方括号对中
			break
		}
	}

	// 如果没找到左方括号，不在超链接中
	if leftBracketIndex == -1 {
		return false
	}

	// 查找匹配后的右方括号和左圆括号
	matchEnd := matchIndex + len(match)
	rightBracketIndex := -1
	leftParenIndex := -1

	// 查找右方括号
	for i := matchEnd; i < len(content); i++ {
		if content[i] == ']' {
			rightBracketIndex = i
			break
		} else if content[i] == '[' {
			// 如果遇到新的左方括号，说明不在同一个方括号对中
			break
		}
	}

	// 如果没找到右方括号，不在超链接中
	if rightBracketIndex == -1 {
		return false
	}

	// 查找右方括号后的左圆括号
	if rightBracketIndex+1 < len(content) && content[rightBracketIndex+1] == '(' {
		leftParenIndex = rightBracketIndex + 1
	}

	// 如果没找到左圆括号，不在超链接中
	if leftParenIndex == -1 {
		return false
	}

	// 查找对应的右圆括号
	rightParenIndex := -1
	for i := leftParenIndex + 1; i < len(content); i++ {
		if content[i] == ')' {
			rightParenIndex = i
			break
		}
	}

	// 如果找到了完整的超链接格式，返回true
	return rightParenIndex != -1
}

// isEnhancedCodeSymbol 增强的代码符号识别逻辑
func isEnhancedCodeSymbol(content string) bool {
	content = strings.TrimSpace(content)

	// 空内容不处理
	if content == "" {
		return false
	}

	// 1. 基本长度和空格检查
	spaceCount := strings.Count(content, " ")

	// 如果包含太多空格，可能是句子片段
	if spaceCount > 3 {
		return false
	}

	// 如果内容太长且包含多个空格，可能是句子片段
	if len(content) > 30 && spaceCount > 1 {
		return false
	}

	// 2. 字符类型检查
	hasLower := false
	hasUpper := false
	hasDigit := false
	hasChinese := false

	for _, r := range content {
		if r >= 'a' && r <= 'z' {
			hasLower = true
		} else if r >= 'A' && r <= 'Z' {
			hasUpper = true
		} else if r >= '0' && r <= '9' {
			hasDigit = true
		} else if r >= 0x4e00 && r <= 0x9fff {
			// 中文字符范围
			hasChinese = true
		}
	}

	// 如果包含中文字符，不太可能是代码符号
	if hasChinese {
		return false
	}

	// 如果是纯数字，不太可能是代码符号
	if hasDigit && !hasLower && !hasUpper {
		// 检查是否包含代码特殊字符
		codeChars := []string{".", "_", "-", "(", ")", "[", "]", "{", "}", ":", ";", "=", "+", "*", "/", "\\", "<", ">", "&", "|", "^", "%", "$", "#", "@", "!"}
		hasCodeChar := false
		for _, char := range codeChars {
			if strings.Contains(content, char) {
				hasCodeChar = true
				break
			}
		}
		// 纯数字且没有代码特殊字符，不是代码符号
		if !hasCodeChar {
			return false
		}
	}

	// 3. 代码符号特征检查
	// 包含常见代码字符的更可能是代码符号
	codeChars := []string{".", "_", "-", "(", ")", "[", "]", "{", "}", ":", ";", "=", "+", "*", "/", "\\", "<", ">", "&", "|", "^", "%", "$", "#", "@", "!"}
	hasCodeChar := false
	for _, char := range codeChars {
		if strings.Contains(content, char) {
			hasCodeChar = true
			break
		}
	}

	// 4. 文件路径特征检查
	isFilePath := strings.Contains(content, "/") || strings.Contains(content, "\\") || strings.Contains(content, ".")

	// 5. 驼峰命名或下划线命名检查
	isCamelCase := hasLower && hasUpper
	isSnakeCase := strings.Contains(content, "_")

	// 6. 常见编程关键词检查
	keywords := []string{
		"function", "class", "method", "var", "let", "const", "def", "func", "int", "string", "bool", "void",
		"public", "private", "protected", "static", "final", "abstract", "interface", "extends", "implements",
		"return", "if", "else", "for", "while", "switch", "case", "break", "continue", "try", "catch", "throw",
		"import", "export", "from", "as", "default", "async", "await", "promise", "callback",
		"null", "undefined", "true", "false", "this", "super", "new", "delete", "typeof", "instanceof",
	}

	lowerContent := strings.ToLower(content)
	isKeyword := false
	for _, keyword := range keywords {
		if lowerContent == keyword || strings.Contains(lowerContent, keyword) {
			isKeyword = true
			break
		}
	}

	// 7. 综合判断逻辑
	// 如果包含代码特征，更可能是代码符号
	if hasCodeChar || isFilePath || isCamelCase || isSnakeCase || isKeyword {
		return true
	}

	// 如果是短的字母数字组合，也可能是代码符号
	if len(content) <= 15 && (hasLower || hasUpper) {
		return true
	}

	// 如果只有字母且不太长，也可能是代码符号
	if len(content) <= 20 && (hasLower || hasUpper) && spaceCount == 0 {
		return true
	}

	// 默认情况下，如果空格不多且不太长，倾向于认为是代码符号
	return spaceCount <= 1 && len(content) <= 25 && (hasLower || hasUpper)
}

// isLikelyCodeSymbol 保留原有的简单判断逻辑作为备用
func isLikelyCodeSymbol(content string) bool {
	// 如果内容包含太多空格，可能不是代码符号
	spaceCount := strings.Count(content, " ")
	if spaceCount > 2 {
		return false
	}

	// 如果内容太长且包含空格，可能是句子片段
	if len(content) > 20 && spaceCount > 0 {
		return false
	}

	return true
}

// FlushBuffer forces processing of any content in the buffer, even with unbalanced backticks
func (p *StreamProcessor) FlushBuffer(ctx context.Context) error {
	// If buffer is empty, nothing to do
	if p.buffer.Len() == 0 {
		return nil
	}

	content := p.buffer.String()

	// Count backticks
	backtickCount := strings.Count(content, "`")

	// If we have no backticks or even number of backticks, process normally
	if backtickCount == 0 || backtickCount%2 == 0 {
		processedContent := p.processBackticks(ctx, content)
		p.buffer.Reset()
		p.debugCounter++
		return p.originalFunc(ctx, definition.StreamingContentTypeContent, []byte(processedContent))
	}

	// Find the last backtick
	lastBacktickPos := strings.LastIndex(content, "`")

	if lastBacktickPos > 0 {
		// Process the content before the last backtick
		firstPart := content[:lastBacktickPos]
		processedFirstPart := p.processBackticks(ctx, firstPart)

		// Keep the remaining content with the unmatched backtick
		remainingPart := content[lastBacktickPos:]

		// 确保处理后的内容不会破坏UTF-8字符
		// 检查处理后文本的最后一个字节是否是多字节UTF-8字符的一部分
		// 如果是，可能会导致乱码，尝试保留原始字符
		combinedOutput := processedFirstPart + remainingPart

		// Reset buffer
		p.buffer.Reset()
		p.debugCounter++

		// 返回合并结果
		return p.originalFunc(ctx, definition.StreamingContentTypeContent, []byte(combinedOutput))
	}

	// If we can't handle it better, just send the raw content
	rawContent := p.buffer.String()
	p.buffer.Reset()
	p.debugCounter++
	return p.originalFunc(ctx, definition.StreamingContentTypeContent, []byte(rawContent))
}

// CleanupProcessor cleans up any resources used by the processor and flushes any remaining content
func (p *StreamProcessor) CleanupProcessor(ctx context.Context) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Flush any remaining content
	if p.buffer.Len() > 0 {
		err := p.FlushBuffer(ctx)
		if err != nil {
			log.Warnf("[MarkdownProcessor] Error flushing buffer during cleanup: %v", err)
		}
	}

	// Reset the buffer
	p.buffer.Reset()
}

// IsEnabled returns whether the Markdown processor is enabled
func IsEnabled() bool {
	return GetConfig().Enabled
}

// SymbolURLGetter 定义符号URL获取函数的类型
type SymbolURLGetter func(ctx context.Context, symbolKey string) string

// GetSymbolURLFunc 是获取符号URL的函数变量，可以在测试中替换
var GetSymbolURLFunc SymbolURLGetter = getSymbolURLImpl

// GetSymbolURL 获取符号对应的URL
func GetSymbolURL(ctx context.Context, symbolKey string) string {
	// 添加超时时间，1秒后自动取消
	timeoutCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	return GetSymbolURLFunc(timeoutCtx, symbolKey)
}

// getSymbolURLImpl 实际实现获取符号URL的函数
func getSymbolURLImpl(ctx context.Context, symbolKey string) string {
	// 使用一个通道和 goroutine 来处理超时
	resultCh := make(chan string, 1)

	go func() {
		if symbolKey == "" {
			resultCh <- ""
			return
		}
		// 检查符号中是否包含特殊字符，如null bytes（\x00）
		if strings.Contains(symbolKey, "\x00") {
			resultCh <- ""
			return
		}
		// 查询符号
		res := getSymbolURL(ctx, symbolKey)
		if res != "" {
			resultCh <- res
			return
		}
		// 查询文件
		res = getFileURL(ctx, symbolKey)
		resultCh <- res
	}()

	// 等待结果或上下文取消
	select {
	case <-ctx.Done():
		// 上下文已取消（超时或其他原因）
		log.Warnf("[MarkdownProcessor] Context done for symbol: %s, reason: %v", symbolKey, ctx.Err())
		return ""
	case result := <-resultCh:
		// 收到结果
		return escapePath(result)
	}
}

func escapePath(path string) string {
	if !strings.ContainsAny(path, "% ") {
		return path
	}
	escapedPath := strings.ReplaceAll(path, "%", "%25")
	escapedPath = strings.ReplaceAll(escapedPath, " ", "%20")
	return escapedPath
}

func getFileURL(ctx context.Context, filePath string) string {
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if fileIndexer == nil {
		return ""
	}
	folder, b := fileIndexer.Environment.WorkspaceInfo.GetWorkspaceFolder()
	if !b {
		return ""
	}

	// 检查WorkspacePath是否为Query的前缀
	if strings.HasPrefix(filePath, folder) {
		// 截取后半部分作为新的Query
		filePath = strings.TrimPrefix(filePath, folder)
	}

	searcher := file.NewBaseFileSearcher()
	_, results := searcher.SearchFile(ctx, &file.FileSearchParams{
		Query:         filepath.Join("**", filePath),
		WorkspacePath: folder,
		RankResult:    true,
	})
	if results.Files == nil || len(results.Files) == 0 {
		return ""
	}
	sessionId := ctx.Value(common.KeySessionId)
	if sessionId != nil && sessionId.(string) != "" {
		session.AddSessionContext(sessionId.(string), []session.SessionFlowContext{
			{
				ContextKey:    results.Files[0].Path,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 3,
			},
		}, false)
	}

	return "file://" + results.Files[0].Path
}

func getSymbolURL(ctx context.Context, symbolKey string) string {
	if symbolKey == "" {
		return ""
	}
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if fileIndexer == nil {
		return ""
	}
	indexer, _ := fileIndexer.GetMetaFileIndexer()
	if indexer == nil {
		return ""
	}
	graphIndexer, _ := fileIndexer.GetGraphFileIndexer()
	if graphIndexer == nil {
		return ""
	}
	searcher := symbol.NewBaseSymbolSearcher(indexer, graphIndexer)
	results, _ := searcher.SearchSymbolByBuiltin(ctx, &symbol.SymbolSearchParams{
		SymbolKey:  symbolKey,
		RankResult: true,
	})
	if len(results) == 0 {
		return ""
	}
	sessionId := ctx.Value(common.KeySessionId)
	if sessionId != nil && sessionId.(string) != "" {
		session.AddSessionContext(sessionId.(string), []session.SessionFlowContext{
			{
				ContextKey:    results[0].FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 3,
			},
		}, false)
	}
	return "file://" + results[0].FilePath + "#L" + strconv.Itoa(int(results[0].LineRange.StartLine)) + "-L" + strconv.Itoa(int(results[0].LineRange.EndLine))
}
