package support

import (
	"bytes"
	"context"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/sse"
	"cosy/user"
	"cosy/util"
	"cosy/util/jsonrepair"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"github.com/google/uuid"

	"github.com/buger/jsonparser"

	"github.com/spf13/cast"

	agentClient "code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	definition2 "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
)

const (
	modelKeyKey     = "modelKey"
	queueTypeKey    = "queueType"
	requestSetIdKey = "requestSetId"
)

type BaseMemoryState interface {
	graph.State

	GetShortTermMemory() memory.ShortTermMemory
	GetCtxForClient() context.Context
}

// GetToolCallArguments 获取tool call的arguments字段，如果解析失败，则尝试修复json
func GetToolCallArguments(toolCall agentClient.ToolCall) string {
	originArguments := toolCall.Function.Arguments
	if originArguments == "" {
		// 补成json格式的
		return "{}"
	}
	// TODO 要删除
	if strings.HasPrefix(originArguments, "\"") {
		// 多一次转义，需要处理掉
		unquoteStr, err := strconv.Unquote(originArguments)
		if err != nil {
			log.Errorf("Error unquoting arguments, toolCallId=%s, arguments=%s", toolCall.ID, originArguments)
			return originArguments
		}
		originArguments = unquoteStr
	}
	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(originArguments), &parameters)
	if err == nil {
		return originArguments
	}
	log.Debugf("Error unmarshalling arguments, toolCallId=%s, toolName: %s, arguments=%s", toolCall.ID, toolCall.Function.Name, originArguments)
	repaired, err := jsonrepair.JSONRepair(originArguments)
	if err != nil {
		log.Errorf("Error repairing json, toolCallId=%s", toolCall.ID)
		// 补成json格式的
		return "{}"
	}
	log.Debugf("Repaired json, toolCallId=%s, arguments=%s", toolCall.ID, repaired)
	return repaired
}

func ParseStreamingChatResponse(ctx context.Context, sseClient *sse.Client, requestId string, sessionId string, extras map[string]string, req *http.Request, timeoutHandler func(req *http.Request, rsp *http.Response), streamingFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent)) (*openaiclient.ChatCompletionResponse, error) {
	defer func() {
		if r := recover(); r != nil {

			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("recover from crash. unmarshalErr: %+v, stack: %s", r, stack)
		}
	}()

	// Parse completionResponse
	completionResponse := openaiclient.ChatCompletionResponse{
		Choices: []*openaiclient.ChatCompletionChoice{
			{},
		},
	}

	doneChan := make(chan error)
	startTime := time.Now()
	go func() {
		err := sseClient.SubscribeWithContext(ctx, time.Duration(287)*time.Second, req, func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
			//判断ctx是否done
			select {
			case <-ctx.Done():
				closeChan <- nil
				return
			default:
			}

			var response definition.ChatResponse
			if string(msg.Event) == "error" {
				//提前结束sse
				closeChan <- errors.ErrInternalServer

				log.Errorf("common agent chat response error, reason=%s", string(msg.Data))
				return
			}

			if string(msg.Event) == "finish" {

				// 删除服务端队列
				userInfo := user.GetCachedUserInfo()
				if userInfo != nil && extras != nil {
					askFinishParam := &definition.AskFinishParam{
						ModelKey:     extras[modelKeyKey],
						QueueType:    extras[queueTypeKey],
						UserId:       userInfo.Uid,
						RequestSetId: extras[requestSetIdKey],
						TimeConsumed: int(time.Since(startTime).Seconds()),
					}
					chatUtil.SendAskFinish(askFinishParam)
				}

				closeChan <- nil

				if string(msg.Data) == sse.ForceFinishReason {
					log.Warnf("force finish sse event.")
				}

				log.Debugf("common agent chat sse finish.")
				return
			}
			//fmt.Println("time="+time.Now().String()+" msg.Data=", string(msg.Data))
			err := json.Unmarshal(msg.Data, &response)
			//log.Debugf("Ask additional data. completionResponse=%s", string(msg.Data))

			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if response.StatusCodeValue != 200 {
				message := response.Body

				modelQueueStatus, isModelQueueError := chatUtil.GetQueueError(errors.New(errors.ModelQueuing, message))
				if isModelQueueError && modelQueueStatus != nil && modelQueueStatus.IsQueued {
					closeChan <- errors.New(errors.ModelQueuing, message)
					log.Infof("common agent chat, model queuing, status: %s", message)
					return
				}

				//提前结束sse
				closeChan <- convertLlmInvokeError(response.StatusCodeValue, message)

				log.Warnf("common agent chat answer finished error, statusCode: %d, message: %s", response.StatusCodeValue, string(msg.Data))
				return
			}

			bodyData := response.Body
			if bodyData == "[DONE]" {
				return
			}
			var streamPayload openaiclient.StreamedChatResponsePayload
			err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamPayload)
			if err != nil {
				log.Errorf("failed to decode stream payload: %v", err)
				return
			}
			// 在这里无法直接访问ask.RequestSetId，我们需要在前面把它存到params.Extra里
			// 使用相同ID作为request_set_id，确保一致性（因为在BuildRemoteChatMessageParam中是相同的）
			requestSetId := requestId
			contentParsingErr := parsingResponseData(ctx, streamPayload, &completionResponse, streamingFunc, toolParsingCallback, requestId, requestSetId)
			if contentParsingErr != nil {
				log.Errorf("common agent chat parsing response data error: %v", contentParsingErr)
			}
		}, timeoutHandler)

		doneChan <- err
	}()

	select {
	case <-ctx.Done():
		log.Debugf("common agent chat canceled: %v", ctx.Err())
	case chatErr := <-doneChan:
		if chatErr != nil {
			log.Errorf("common agent request to remote error. err: %v", chatErr)

			// 检查错误是否是 context deadline exceeded
			// 具体查看 sse/client.go: c.Connection.Do(req) 可能返回 "context deadline exceeded"
			//  - http/client.go#979: err = &timeoutError{err.Error() + " (Client.Timeout or context cancellation while reading body)"}
			if strings.Contains(strings.ToLower(chatErr.Error()), "timeout") {
				log.Warnf("model response timeout detected: %v", chatErr)
				return nil, errors.ErrModelResponseTimeout
			}

			unifiedErr := parseChatErr(chatErr)

			return nil, unifiedErr
		} else {
			log.Debugf("common agent chat finished.")
			break
		}
	}

	return &completionResponse, nil
}

func parsingResponseData(ctx context.Context, streamResponse openaiclient.StreamedChatResponsePayload, response *openaiclient.ChatCompletionResponse, streamingContentFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent), requestId string, requestSetId string) error {
	//是否在解析内容流，内容流需要实时输出
	isParsingContent := true

	if streamResponse.Error != nil {
		return streamResponse.Error
	}

	if streamResponse.ID != "" {
		response.ID = streamResponse.ID
	}
	if streamResponse.Usage != nil {
		response.Usage.CompletionTokens = streamResponse.Usage.CompletionTokens
		response.Usage.PromptTokens = streamResponse.Usage.PromptTokens
		response.Usage.TotalTokens = streamResponse.Usage.TotalTokens
		response.Usage.CompletionTokensDetails.ReasoningTokens = streamResponse.Usage.CompletionTokensDetails.ReasoningTokens
		response.Usage.PromptTokensDetails.CachedTokens = streamResponse.Usage.PromptTokensDetails.CachedTokens
	}

	if len(streamResponse.Choices) == 0 {
		return nil
	}
	choice := streamResponse.Choices[0]
	chunk := []byte(choice.Delta.Content)
	response.Choices[0].Message.Content += choice.Delta.Content
	response.Choices[0].FinishReason = choice.FinishReason
	response.Choices[0].Message.ReasoningContent += choice.Delta.ReasoningContent
	response.Choices[0].Message.Signature += choice.Delta.Signature

	isParsingReasoning := (choice.Delta.ReasoningContent != "" && choice.Delta.Content == "")
	if isParsingReasoning {
		chunk = []byte(choice.Delta.ReasoningContent)
	}

	if choice.Delta.FunctionCall != nil {
		log.Debugf("common agent should not return function call in delta.")
		chunk = updateFunctionCall(response.Choices[0].Message, choice.Delta.FunctionCall)
	}

	if len(choice.Delta.ToolCalls) > 0 {
		var toolParseEvents []*definition.ToolParseEvent

		// 在处理工具调用之前，先强制清空处理器链中的所有处理器
		// 避免工具显示穿插在现有输出中
		if requestSetId != "" {
			// 使用requestSetId清空对应的处理器链（因为处理器是使用这个ID创建的）
			// 这里一定要注意区别于requestId，requestId是每次请求的唯一标识，而requestSetId是每次请求的唯一标识
			err := FlushProcessorChain(ctx, requestSetId)
			if err != nil {
				log.Errorf("[ToolCalls] flush processor chain error: %v", err)
			}
		}

		chunk, response.Choices[0].Message.ToolCalls, toolParseEvents = UpdateToolCalls(response.Choices[0].Message.ToolCalls,
			choice.Delta.ToolCalls)
		for _, e := range toolParseEvents {
			if toolParsingCallback != nil {
				toolParsingCallback(ctx, e)
			}
		}

		isParsingContent = false
	}

	if isParsingContent {
		streamingContentType := definition.StreamingContentTypeContent
		if isParsingReasoning {
			streamingContentType = definition.StreamingContentTypeReasoning
		}

		if streamingContentFunc != nil {
			err := streamingContentFunc(ctx, definition.StreamingContentType(streamingContentType), chunk)
			if err != nil {
				return fmt.Errorf("streaming func returned an error: %w", err)
			}
		}

	}
	return nil
}

func parseChatErr(chatErr error) error {
	cosyErr, is := errors.IsUnifiedError(chatErr)
	if is {
		return cosyErr
	}
	return errors.ErrInternalServer
}

// UpdateToolCalls 解析ToolCall，支持一次选择多个工具
func UpdateToolCalls(tools []openaiclient.ToolCall, delta []*openaiclient.ToolCall) ([]byte, []openaiclient.ToolCall, []*definition.ToolParseEvent) {
	if len(delta) == 0 {
		return []byte{}, tools, nil
	}
	chunk, _ := json.Marshal(delta) // nolint:errchkjson

	result := make([]*definition.ToolParseEvent, 0)
	delta0 := delta[0]
	// 这里的不能用delta0.ID来做为toolCalls开始的标志，因为gemini没有返回id(claude、openai都有)
	if delta0.Function.Name != "" {
		// 新的toolcall开始，说明上一个toolcall的参数解析结束了，
		// 增加一个EndToolParseEvent的event
		if len(tools) > 0 {
			result = append(result, &definition.ToolParseEvent{
				Type: definition.EndToolParseEvent,
				ToolCall: &definition2.ToolCall{
					ID: tools[len(tools)-1].ID,
					Function: definition2.FunctionCall{
						Name:      tools[len(tools)-1].Function.Name,
						Arguments: tools[len(tools)-1].Function.Arguments,
					},
				},
			})
		}

		if delta0.ID == "" {
			// gemini没有id，要兼容一下
			delta0.ID = uuid.New().String()
		}
		//解析到新tool call
		tools = append(tools, *delta0)
		result = append(result, &definition.ToolParseEvent{
			Type: definition.StartToolParseEvent,
			ToolCall: &definition2.ToolCall{
				ID: delta0.ID,
				Function: definition2.FunctionCall{
					Name: delta0.Function.Name,
				},
			},
		})

		return chunk, tools, result
	}
	if len(tools) < 1 {
		return []byte{}, tools, nil
	}
	// 追加到最后一个tool上
	tools[len(tools)-1].Function.Arguments += delta0.Function.Arguments

	parsedArguments := parsePartialArguments(tools[len(tools)-1].Function.Arguments)
	parseEvent := &definition.ToolParseEvent{
		Type: definition.DeltaParsingToolParsingEvent,
		ToolCall: &definition2.ToolCall{
			ID: tools[len(tools)-1].ID,
			Function: definition2.FunctionCall{
				Name: tools[len(tools)-1].Function.Name,
			},
		}}
	if parsedArguments != nil {
		parseEvent.ToolCall.Function.Arguments = util.ToJsonStr(parsedArguments)
	}
	result = append(result, parseEvent)

	return chunk, tools, result
}

// 解析arguments字段，保证是完整json
func parsePartialArguments(argumentsStr string) map[string]any {
	if argumentsStr == "" {
		return nil
	}
	// TODO 要删除
	if strings.HasPrefix(argumentsStr, "\"") {
		if !strings.HasSuffix(argumentsStr, "\"") {
			argumentsStr = argumentsStr + "\""
		}
		// 多一次转义，需要处理掉
		unquoteStr, err := strconv.Unquote(argumentsStr)
		if err != nil {
			return nil
		}
		argumentsStr = unquoteStr
	}
	var parsedJson = make(map[string]any)

	jsonparser.ObjectEach([]byte(argumentsStr), func(key []byte, value []byte, dataType jsonparser.ValueType, offset int) error {
		if dataType == jsonparser.String {
			// Use json.Unmarshal to correctly handle all JSON escape sequences
			var unquotedValue string
			// Add quotes around the value to make it a valid JSON string
			quotedValue := fmt.Sprintf("\"%s\"", strings.ReplaceAll(string(value), "\"", "\\\""))
			if err := json.Unmarshal([]byte(quotedValue), &unquotedValue); err != nil {
				// If unmarshal fails, fall back to the original string with basic replacements
				strValue := string(value)
				strValue = strings.ReplaceAll(strValue, "\\\\", "\\")
				strValue = strings.ReplaceAll(strValue, "\\\"", "\"")
				strValue = strings.ReplaceAll(strValue, "\\/", "/")
				strValue = strings.ReplaceAll(strValue, "\\n", "\n")
				parsedJson[string(key)] = strValue
			} else {
				parsedJson[string(key)] = unquotedValue
			}
		} else if dataType == jsonparser.Number {
			parsedJson[string(key)] = cast.ToInt(string(value))
		} else if dataType == jsonparser.Boolean {
			parsedJson[string(key)] = cast.ToBool(string(value))
		} else {
			parsedJson[string(key)] = value
		}
		return nil
	})
	return parsedJson
}

func convertLlmInvokeError(errCode int, message string) error {
	if errCode == 400 {
		errResp := definition.LlmErrorResponse{}
		if err := json.Unmarshal([]byte(message), &errResp); err == nil {
			if errResp.Error.Code == "invalid_parameter_error" {
				if strings.Contains(errResp.Error.Message, "The tool call is not supported") {
					return &errors.Error{
						Code:    errors.ToolCallNotSupport,
						Message: errResp.Error.Message,
					}
				}
			}
		}
		//兜底系统内部错误
		return errors.ErrInternalServer
	} else if errCode == 418 {
		// error_finish结束的，匹配一下错误内容
		err := convertErrCode418(errCode, message)
		if err != nil {
			return err
		}
	}
	return errors.New(errCode, message)
}

func convertErrCode418(errCode int, message string) error {
	var errorResp ErrorCode418Response
	if err := json.Unmarshal([]byte(message), &errorResp); err != nil {
		log.Debugf("Failed to parse 418 error response: %v", err)
		return nil
	}

	// 解析嵌套的 Message 字段
	var innerResp ErrorCode418InnerResponse
	if err := json.Unmarshal([]byte(errorResp.Message), &innerResp); err != nil {
		log.Debugf("Failed to parse 418 inner response: %v", err)
		return nil
	}

	// 如果有 choices，解析第一个 choice 的 content
	if len(innerResp.Choices) > 0 && innerResp.Choices[0].Delta.Content != "" {
		var contentResp ErrorCode418Content
		if err := json.Unmarshal([]byte(innerResp.Choices[0].Delta.Content), &contentResp); err != nil {
			log.Errorf("Failed to parse 418 content response: %v", err)
			return nil
		}

		// 根据具体的错误类型返回对应的错误
		switch contentResp.Error.Code {
		case "array_above_max_length":
			return &errors.Error{
				Code:    errors.ModelResponseToolOvertLimit,
				Message: contentResp.Error.Message,
			}
		// gpt-4o对于请求中的tool格式不规范会报错
		case "invalid_function_parameters":
			return &errors.Error{
				Code:    errors.ModelResponseInvalidToolSchema,
				Message: contentResp.Error.Message,
			}
		default:
			// claude4 对于请求中的tool格式不规范会报错
			// 检查 Message 中是否包含 "JSON schema is invalid"
			if strings.Contains(contentResp.Error.Message, "JSON schema is invalid") {
				return &errors.Error{
					Code:    errors.ModelResponseInvalidToolSchema,
					Message: contentResp.Error.Message,
				}
			}
			return nil
		}
	}
	return nil
}

func updateFunctionCall(message agentClient.ChatMessage, functionCall *agentClient.FunctionCall) []byte {
	if message.FunctionCall == nil {
		message.FunctionCall = functionCall
	} else {
		message.FunctionCall.Arguments += functionCall.Arguments
	}
	chunk, _ := json.Marshal(message.FunctionCall) // nolint:errchkjson
	return chunk
}

// ErrorCode418Response 用于解析 errCode == 418 的错误响应
type ErrorCode418Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// ErrorCode418InnerResponse 解析 Message 字段中的嵌套 JSON
type ErrorCode418InnerResponse struct {
	Choices []ErrorCode418Choice `json:"choices"`
}

// ErrorCode418Choice 选择项结构
type ErrorCode418Choice struct {
	Index        int               `json:"index"`
	Delta        ErrorCode418Delta `json:"delta"`
	FinishReason string            `json:"finish_reason"`
}

// ErrorCode418Delta delta 结构
type ErrorCode418Delta struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ErrorCode418Content 解析 Content 字段中的具体错误信息
type ErrorCode418Content struct {
	Error ErrorCode418ErrorDetail `json:"error"`
}

// ErrorCode418ErrorDetail 具体的错误详情
type ErrorCode418ErrorDetail struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Param   string `json:"param"`
	Code    string `json:"code"`
}
