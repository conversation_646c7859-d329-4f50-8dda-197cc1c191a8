package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"github.com/google/uuid"
)

type LLMResponseHandler struct {
	SessionId           string
	RequestId           string
	SessionType         string
	CallServerRequestId string    // 请求服务端的requestId
	StartTime           time.Time // 回答开始时间
	EndTime             time.Time // 回答结束时间
	EnableLogTime       bool      // 是否记录时间

	ToolCallCount int // 已经使用的工具次数

	toolCallOverLimit bool                      // 工具使用超过上限
	currentToolName   string                    // 这次llm调用解析处理的工具名
	toolCallExtraMap  map[string]map[string]any // toolCallId - extra 需要保存的额外参数
	syncedArgsMap     map[string][]string       // toolCallId - 已经同步过的参数
	content           string                    // content内容
	answerNotEmpty    bool                      // answer为空
	currentToolBlock  string                    // 当前工具block块
	hasSyncToolBlock  bool                      // 已经同步了当前工具block
	inThink           bool                      // 进入think
	hasStart          bool                      // 是否回答开始

	CtxForClient context.Context
	CancelFunc   func()
}

// OnDeltaContent 增量处理模型结果的content
// see definition.StreamingContentTypeContent
func (s *LLMResponseHandler) OnDeltaContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	deltaAnswer := string(chunk)
	if deltaAnswer == "" {
		return nil
	}
	if contentType == definition.StreamingContentTypeReasoning && s.ToolCallCount > 0 {
		// 非首次不输出thinking
		return nil
	}
	// answer不为空
	if contentType != definition.StreamingContentTypeReasoning {
		// think不影响content是否为空的判断
		s.answerNotEmpty = true
	}
	if !s.inThink && contentType == definition.StreamingContentTypeReasoning {
		s.startThink()
	} else if s.inThink && contentType == definition.StreamingContentTypeContent {
		s.finishThink()
	}
	s.syncContent(deltaAnswer)
	return nil
}

// OnToolParseEvent 处理解析工具事件
func (s *LLMResponseHandler) OnToolParseEvent(ctx context.Context, toolParseEvent *definition.ToolParseEvent) {
	if s.toolCallOverLimit {
		// 到达上限以后不推送工具信息
		return
	}
	switch toolParseEvent.Type {
	case definition.StartToolParseEvent:
		if s.inThink {
			s.finishThink()
		}

		// 在开始处理工具调用之前，刷新处理器链中的内容
		err := FlushProcessorChain(ctx, s.RequestId)
		if err != nil {
			log.Errorf("[LLMResponseHandler] flush processor chain error: %v", err)
		}

		if s.ToolCallCount == coderCommon.GetToolCallLimit() {
			// 工具调用到达上限后，发现存在下一次工具调用
			s.toolCallOverLimit = true
			if s.CancelFunc != nil {
				s.CancelFunc()
			}
			return
		}
		toolCall := toolParseEvent.ToolCall
		// 初始化用来保存extra和args的map
		if s.toolCallExtraMap == nil {
			s.toolCallExtraMap = make(map[string]map[string]any)
		}
		if s.syncedArgsMap == nil {
			s.syncedArgsMap = make(map[string][]string)
		}
		s.toolCallExtraMap[toolCall.ID] = make(map[string]any)
		s.syncedArgsMap[toolCall.ID] = make([]string, 0)
		s.currentToolName = FormatToolName(toolCall.Function.Name)
		s.hasSyncToolBlock = false
		builder := strings.Builder{}
		builder.WriteString(fmt.Sprintf("\n\n```toolCall::%s::%s::%s", s.currentToolName, toolCall.ID, coderCommon.ToolCallStatusInit))
		builder.WriteString("\n```\n\n")
		s.currentToolBlock = builder.String()
		if toolsRequiringExplanation[s.currentToolName] {
			if s.answerNotEmpty {
				// 前面answer不为空，可以推送工具的block块，不然需要等待解析出explanation字段后再推送
				s.syncToolBlock("StartToolParseEvent")
			}
		} else if !toolsRequiringExplanation[s.currentToolName] {
			// 其他工具可以直接推送
			s.syncToolBlock("StartToolParseEvent")
		}
	case definition.DeltaParsingToolParsingEvent:
		if toolParseEvent.ToolCall == nil || toolParseEvent.ToolCall.ID == "" || toolParseEvent.ToolCall.Function.Name == "" {
			return
		}
		toolCallId := toolParseEvent.ToolCall.ID
		arguments := toolParseEvent.ToolCall.Function.Arguments
		if util.Contains(toolCommon.EditFileTools, toolParseEvent.ToolCall.Function.Name) &&
			strings.Contains(arguments, fmt.Sprintf("\"%s\"", coderCommon.ToolCallArgumentNameFilePath)) {
			// 已经处理过file_path
			if util.Contains(s.syncedArgsMap[toolCallId], coderCommon.ToolCallArgumentNameFilePath) {
				return
			}
			// edit_file类工具解析出了file_path参数
			var args struct {
				FilePath string `json:"file_path"`
			}
			if err := json.Unmarshal([]byte(arguments), &args); err != nil {
				log.Error(err)
				return
			}
			s.syncedArgsMap[toolCallId] = append(s.syncedArgsMap[toolCallId], coderCommon.ToolCallArgumentNameFilePath)
			log.Debugf("[common_dev_agent] %s generating, origin_ars=%s", toolParseEvent.ToolCall.Function.Name, arguments)
			if !s.hasSyncToolBlock {
				// 如果前面没有推送过工具block，现在需要推送了
				s.syncToolBlock("DeltaParsingToolParsingEvent.file_path")
			}
			afterPath, err := tools.CheckPath(args.FilePath, s.CtxForClient, s.RequestId, s.SessionId)
			if err != nil {
				// 文件路径非法
				s.toolCallExtraMap[toolCallId][coderCommon.ToolCallExtraFilePathValid] = false
				return
			}
			fileId := uuid.NewString()

			s.toolCallExtraMap[toolCallId][coderCommon.ToolCallExtraFileId] = fileId
			log.Debugf("[common_dev_agent] %s generating, file_path=%s, fileId=%s", toolParseEvent.ToolCall.Function.Name, args.FilePath, fileId)
			// 一起推送arguments里的id_path和result里面的fileId
			editFileSyncer := &EditFileResultSyncer{
				SessionId:    s.SessionId,
				RequestId:    s.RequestId,
				ToolCall:     *toolParseEvent.ToolCall,
				CtxForClient: s.CtxForClient,
			}
			editFileSyncer.Sync(ctx, &apply.EditFileResponse{
				FilePath:   args.FilePath,
				FileStatus: service.GENERATING.String(),
				ApplyResult: &definition.DiffApplyResult{
					WorkingSpaceFileId: fileId,
				},
			})
			// 触发generating消息
			param := definition.DiffApplyParams{
				NeedSave:                 true,
				NeedRecord:               false,
				NeedSyncWorkingSpaceFile: true,
				NeedWebSocketMethod:      false,
				ChatRecordId:             s.RequestId,
				RequestSetId:             s.RequestId,
				SessionId:                s.SessionId,
				RequestId:                uuid.NewString(),
				Stream:                   true,
				Modification:             "",
				WorkingSpaceFile: definition.WorkingSpaceFile{
					Id:       fileId,
					FileId:   afterPath,
					Language: "",
				},
			}
			diffApplyResult := tools.DiffApply(s.CtxForClient, param)
			if !diffApplyResult.IsSuccess {
				// TODO：生成工作区异常处理逻辑?
			}
		} else if !s.hasSyncToolBlock && strings.Contains(arguments, fmt.Sprintf("\"%s\"", coderCommon.ToolCallArgumentNameExplanation)) {
			// 已经处理过explanation
			if util.Contains(s.syncedArgsMap[toolCallId], coderCommon.ToolCallArgumentNameExplanation) {
				return
			}
			// 前面没有同步过工具block，并且解析出explanation参数
			var args struct {
				Explanation string `json:"explanation"`
			}
			if err := json.Unmarshal([]byte(arguments), &args); err != nil {
				log.Error(err)
				return
			}
			s.syncedArgsMap[toolCallId] = append(s.syncedArgsMap[toolCallId], coderCommon.ToolCallArgumentNameExplanation)
			if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
				return
			}
			if toolsRequiringExplanation[s.currentToolName] {
				s.syncContent(args.Explanation)
				s.syncToolBlock("DeltaParsingToolParsingEvent.explanation")
			}
		}
	case definition.EndToolParseEvent:
		if toolParseEvent.ToolCall == nil {
			return
		}
		if !s.hasSyncToolBlock {
			// 如果前面没有推送过工具block，现在需要推送了
			s.syncToolBlock("EndToolParseEvent")
		}
		if !util.Contains(toolCommon.EditFileTools, toolParseEvent.ToolCall.Function.Name) {
			// 非文件编辑类的工具推送一下参数
			SyncToolParamsToClient(ctx, s.SessionId, s.RequestId, *toolParseEvent.ToolCall, coderCommon.ToolCallStatusInit)
		}
	}
}

// PostSyncToolCall 最后的补偿，等EndToolParseEvent逻辑实现以后可以去掉
func (s *LLMResponseHandler) PostSyncToolCall(ctx context.Context, message *agentDefinition.Message) {
	if s.inThink {
		s.finishThink()
	}
	if !s.hasSyncToolBlock && s.currentToolBlock != "" {
		s.syncToolBlock("PostSyncToolCall")
	}
	if message.ToolCalls != nil && len(message.ToolCalls) > 0 {
		for i, _ := range message.ToolCalls {
			toolCallId := message.ToolCalls[i].ID
			if s.toolCallExtraMap[toolCallId] != nil {
				message.ToolCalls[i].Extra = s.toolCallExtraMap[toolCallId]
			}
		}

		// 这里上报最后一个toolcall的参数，补充EndToolParseEvent没处理到的情况
		toolCall := message.ToolCalls[len(message.ToolCalls)-1]
		if !util.Contains(toolCommon.EditFileTools, toolCall.Function.Name) {
			// 非文件编辑类的工具推送一下参数
			SyncToolParamsToClient(ctx, s.SessionId, s.RequestId, toolCall, coderCommon.ToolCallStatusInit)
		}
	}
	if message.Extra == nil {
		message.Extra = make(map[string]any)
	}
	// 这里记录一下写回端侧的答案
	message.Extra[coderCommon.MessageExtraClientAnswer] = s.content
}

// PostSyncToolCallOnError 发生error时，发送一些取消的消息
func (s *LLMResponseHandler) PostSyncToolCallOnError(ctx context.Context, message *agentDefinition.Message) {
	if message == nil || message.ToolCalls == nil || len(message.ToolCalls) == 0 {
		return
	}
	for i, _ := range message.ToolCalls {
		toolCall := message.ToolCalls[i]
		if !util.Contains(toolCommon.EditFileTools, toolCall.Function.Name) {
			continue
		}
		fileId, ok := s.toolCallExtraMap[toolCall.ID][coderCommon.ToolCallExtraFileId].(string)
		if !ok {
			continue
		}
		// 已经触发了edit_file，要触发edit_file的取消消息
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
			Id:     fileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: definition.UnknownErrorCode,
			},
		})
	}
}

func (s *LLMResponseHandler) startThink() {
	s.inThink = true
	s.syncContent("<think>\n")
}

func (s *LLMResponseHandler) finishThink() {
	s.inThink = false
	if strings.HasSuffix(s.content, "</think>") {
		s.syncContent("\n")
	} else {
		s.syncContent("\n</think>\n")
	}
}

func (s *LLMResponseHandler) syncContent(content string) {
	s.content = s.content + content
	PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, content)
}

func (s *LLMResponseHandler) syncToolBlock(stage string) {
	s.hasSyncToolBlock = true
	s.content = s.content + s.currentToolBlock
	PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, stage)
}
