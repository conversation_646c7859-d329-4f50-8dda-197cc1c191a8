package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/log"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	condenserMemory "code.alibaba-inc.com/cosy/lingma-agent-graph/memory/condenser"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tokenizer"
	"encoding/json"
	"fmt"
)

type WikiTokenLimitedMemory struct {
	memory.BaseShortTermMemory
	MaxAllowedSize int
	Model          string
}

func NewWikiTokenLimitedMemory(maxAllowedSize int, model string) *WikiTokenLimitedMemory {
	return &WikiTokenLimitedMemory{
		BaseShortTermMemory: memory.NewBaseShortTermMemoryWithCondenser(NewTokenLimitedCondenser(maxAllowedSize, model)),
		MaxAllowedSize:      maxAllowedSize,
		Model:               model,
	}
}

type WikiTokenLimitedCondenser struct {
	*condenserMemory.BaseCondenser
	maxAllowedSize int
	model          string
}

func NewTokenLimitedCondenser(maxAllowedSize int, model string) *WikiTokenLimitedCondenser {
	return &WikiTokenLimitedCondenser{
		BaseCondenser:  condenserMemory.NewBaseCondenser(),
		maxAllowedSize: maxAllowedSize,
		model:          model,
	}
}

// Condense implements the Condenser interface
func (r *WikiTokenLimitedCondenser) Condense(messages []*definition.Message) ([]*definition.Message, []*definition.Message, error) {
	if len(messages) == 0 {
		return nil, nil, fmt.Errorf("empty messages")
	}
	serializedMessages, err := serializeMessages(messages)
	if err != nil {
		return nil, nil, fmt.Errorf("error serializing messages: %w", err)
	}

	// 如果小于maxAllowedSize*3,一个token对应3个字符，则直接返回，避免无谓的token计算
	if len(serializedMessages) < r.maxAllowedSize*3 {
		return messages, nil, nil
	}

	lastMessage := messages[len(messages)-1]
	if lastMessage.Role == definition.RoleTypeTool && len(lastMessage.Content) > r.maxAllowedSize {
		//一条tool message差不多就超了token，只截取大致最大token限制的10之一的token
		lastMessage.Content = lastMessage.Content[len(lastMessage.Content)-(r.maxAllowedSize/10)*3:]
	}

	tokenizer.NewQwenTokenizer(true)
	tokenCount, err := tokenizer.CalQwenTokenCount(serializedMessages)
	if err != nil {
		return nil, nil, fmt.Errorf("error calculating token count: %w", err)
	}

	removedMessages := []*definition.Message{}

	if len(messages) == 2 {
		if tokenCount > r.maxAllowedSize {
			//只有system 和 user message的时候就超了
			log.Errorf("system + one message already exceeds token limit, cannot condense further. tokenCount=%d, maxAllowedSize=%d",
				tokenCount, r.maxAllowedSize)
			return messages, removedMessages, fmt.Errorf("failed to condense messages within token limit after attempts")
		}
	}
	prefixUserUserMessage, suffixUserMessage, pendingMessage := findAllUserMessages(messages)

	// 使用while循环迭代截断消息，最多执行5次，避免万一的死循环，第一个system message不被截断
	for iteration := 0; iteration < 5 && tokenCount > r.maxAllowedSize && len(pendingMessage) > 1; iteration++ {
		// 每次移除前半部分的消息
		removeCount := (len(pendingMessage) + 1) / 2
		if removeCount == 0 {
			removeCount = 1
		}
		message := pendingMessage[removeCount]
		//截断的时候下一个需要是assistant或是user的message
		if message.Role != definition.RoleTypeAssistant && message.Role != definition.RoleTypeUser {
			removeCount++
		}
		removedMessages = append(removedMessages, pendingMessage[0:removeCount]...)
		remainMessages := pendingMessage[removeCount:]
		pendingMessage = remainMessages
		// 重新序列化和计算token
		serializedMessages, err = serializeMessages(pendingMessage)
		if err != nil {
			return nil, nil, fmt.Errorf("error re-serializing messages: %w", err)
		}

		tokenCount, err = tokenizer.CalQwenTokenCount(serializedMessages)
		if err != nil {
			return nil, nil, fmt.Errorf("error recalculating token count: %w", err)
		}
	}

	// 如果经过几次次迭代后仍然超过限制，只取最近一条user message和最多最近10条message
	if tokenCount > r.maxAllowedSize {
		log.Errorf("failed to condense messages within token limit after attempts")
		remainMessages := []*definition.Message{}

		lastRemainIndex := len(pendingMessage) - 1
		if len(pendingMessage) > 10 {
			lastRemainIndex = len(pendingMessage) - 10
		}
		lastRemainMessage := pendingMessage[lastRemainIndex]
		if lastRemainMessage.Role != definition.RoleTypeAssistant && lastRemainMessage.Role != definition.RoleTypeUser {
			lastRemainIndex++
		}
		// 添加最近消息，但跳过user message以避免重复
		realRemainIndex := lastRemainIndex
		for i, msg := range pendingMessage[lastRemainIndex:] {
			if msg.Role != definition.RoleTypeUser {
				remainMessages = append(remainMessages, msg)
			} else {
				//如果遇到user message则直接退出
				realRemainIndex = lastRemainIndex + i
				break
			}
		}
		removedMessages = append(removedMessages, pendingMessage[0:realRemainIndex]...)
		pendingMessage = remainMessages
	}
	messages = append(prefixUserUserMessage, pendingMessage...)
	messages = append(messages, suffixUserMessage...)
	return messages, removedMessages, nil
}

func findAllUserMessages(messages []*definition.Message) ([]*definition.Message, []*definition.Message, []*definition.Message) {
	prefixUserMessages := []*definition.Message{}
	suffixUserMessages := []*definition.Message{}
	startIndex := 0
	endIndex := len(messages) - 1

	// 找前缀用户消息
	for i := 0; i < len(messages); i++ {
		message := messages[i]
		if message.Role != definition.RoleTypeUser {
			startIndex = i
			break
		}
		prefixUserMessages = append(prefixUserMessages, message)
		startIndex = i + 1 // 如果都是用户消息，startIndex应该是下一个位置
	}

	// 找后缀用户消息
	for i := len(messages) - 1; i >= 0; i-- {
		message := messages[i]
		if message.Role != definition.RoleTypeUser {
			endIndex = i
			break
		}
		suffixUserMessages = append(suffixUserMessages, message)
		endIndex = i - 1 // 如果都是用户消息，endIndex应该是前一个位置
	}

	// 处理所有消息都是用户消息的情况
	if startIndex > endIndex {
		// 所有消息都是用户消息
		return prefixUserMessages, suffixUserMessages, []*definition.Message{}
	}

	pendingMessage := messages[startIndex : endIndex+1]
	return prefixUserMessages, suffixUserMessages, pendingMessage
}

func serializeMessages(messages []*definition.Message) (string, error) {
	jsonBytes, err := json.Marshal(messages)
	if err != nil {
		return "", fmt.Errorf("error marshaling messages: %w", err)
	}
	return string(jsonBytes), nil
}
