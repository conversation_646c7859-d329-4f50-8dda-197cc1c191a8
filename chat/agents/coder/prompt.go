package coder

import (
	"context"
	common2 "cosy/chat/agents/coder/common"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/experiment"
	"cosy/extension"
	"cosy/extension/rule"
	"cosy/global"
	"cosy/indexing"
	"cosy/log"
	"cosy/memory/stm"
	"cosy/prompt"
	"cosy/systemrule"
	"cosy/util"
	_ "embed"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v4/host"
)

//go:embed ask_apply_code_block_format_reminder_prompt.txt
var askApplyCodeBlockFormatReminderPrompt string

func buildPrompt(ctx context.Context, params *definition.AskParams, inputs map[string]any) (string, string) {
	systemPrompt := "You are a helpful assistant that can interact with a computer to solve tasks.\n<IMPORTANT>\n* If user provides a path, you should NOT assume it's relative to the current working directory. Instead, you should explore the file system to find the file before working on it.\n</IMPORTANT>\n如果你认为任务已经完成或者不能继续进展了，请使用finish工具。"
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	workspace := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileInfo, err := os.Stat(projectPath)
	if err == nil && !fileInfo.IsDir() {
		// 使用 filepath.Dir 获取文件所在的目录路径
		log.Debugf("projectPath is not a directory, projectPath :  %s ", projectPath)
		projectPath = filepath.Dir(projectPath)
	}
	//从attach区域取
	//var adjustedContextExtras []definition.CustomContextProviderExtra
	var contextProviderExtras []definition.CustomContextProviderExtra
	if contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]; ok {
		contextProviderExtras = contextExtraValue.([]definition.CustomContextProviderExtra)
		if !util.IsRetryRemoteAsk(params) {
			//adjustedContextExtras = updateWorkspaceFileContentForContexts(ctx, params.SessionId, contextProviderExtras)
		}
	}

	lastConversationInfo := stm.NewConversationInfo()
	sessionId := params.SessionId
	value, exists := stm.ConversationInfoMap[sessionId]
	if exists {
		lastConversationInfo = value
	}
	projectRuleContext, ok := inputs[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
	hasRuleContext, allGlobRulesExcludeMatched, allAlwaysRules, allModelDecisionRules, userRules := fetchRules(projectRuleContext, &lastConversationInfo)

	userPromptByTask, err := buildUserPromptWithChatTask(ctx, params, contextProviderExtras, fileIndexer, inputs, hasRuleContext,
		allGlobRulesExcludeMatched, allAlwaysRules, allModelDecisionRules, userRules)

	var osInfo = ""
	var osVersion, shell string
	osVersion = runtime.GOOS
	// 获取操作系统信息
	hostInfo, err := host.Info()
	if err == nil {
		osVersion = hostInfo.OS + " " + hostInfo.PlatformVersion
		osInfo = hostInfo.OS
	}
	shell = os.Getenv("SHELL")

	var workspaceLanguages []string
	if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
		workspaceLanguages = langStat.GetMostLanguages(0.2)
	}
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	ideInfo := ""
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*definition.IdeConfig)
		if ok {
			ideInfo = ide.IdePlatform + " " + ide.IdeVersion
		}
	}

	memoryPrompt := fetchMemoryPrompt(ctx, inputs)
	if memoryPrompt == lastConversationInfo.MemoryPrompt {
		memoryPrompt = ""
	} else {
		lastConversationInfo.MemoryPrompt = memoryPrompt
	}

	projectWikiPrompt := fetchProjectWikiListPrompt(ctx, inputs)
	isEnableMemoryGuide := false
	if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentWikiGuidedPromptEnabled, experiment.ConfigScopeClient, true) && projectWikiPrompt != "" {
		isEnableMemoryGuide = true
	}

	mode := params.Mode
	systemInput := prompt.CoderAgentSystemPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			IsQoder:   global.IsQoderProduct(),
		},
		WorkspacePath:       projectPath,
		WorkspaceLanguages:  workspaceLanguages,
		PreferredLanguage:   preferredLanguage,
		OsInfo:              osInfo,
		OsVersion:           osVersion,
		Shell:               shell,
		IdeInfo:             ideInfo,
		CurrentSystemTime:   time.Now().Format("2006-01-02"),
		IsEnableMemoryGuide: isEnableMemoryGuide,
		IsEvaluationMode:    global.IsSubAgentMode(),
		IsLingmaProduct:     global.IsLingmaProduct(),
	}

	if mode == definition.SessionModeChat {
		systemPrompt, err = prompt.EncryptedEngine.RenderAskAgentSystemPrompt(systemInput)
		if err != nil {
			log.Error("Failed to render aiDevelopCommonSystemPrompt ", err)
			return systemPrompt, userPromptByTask
		}
	} else {
		//systemPrompt, err = prompt.Engine.RenderCoderAgentSystemPrompt(systemInput)
		systemPrompt, err = prompt.EncryptedEngine.RenderCoderAgentSystemPrompt(systemInput)
		if err != nil {
			log.Error("Failed to render aiDevelopCommonSystemPrompt ", err)
			return systemPrompt, userPromptByTask
		}
	}

	//如果是retry，从retry_text中获取userPrompt，使用的是上一轮的user message中的内容
	userInputQuery := chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
	userRetryInputQuery, _ := inputs[common.KeyCoderAgentRetryUserQuery].(string)
	if userRetryInputQuery != "" {
		//从retry_text中获取
		return systemPrompt, userRetryInputQuery
	}
	if isExcludeMemoryForSystemCommand(params.ChatTask) {
		//增加上existing code的强调，只有agent模式下才增加
		if mode == definition.SessionModeAgent {
			userPromptByTask = userPromptByTask + "\n<reminder>\nWhen making code changes, specify ONLY the precise lines of code that you wish to edit. Represent all unchanged code using the comment of the language you're editing in - example: `// ... existing code ...`\n</reminder>"
		} else if mode == definition.SessionModeChat {
			if lastConversationInfo.FirstConversion {
				userPromptByTask = userPromptByTask + "\n" + askApplyCodeBlockFormatReminderPrompt
			}
		}
		lastConversationInfo.FirstConversion = false
		stm.ConversationInfoMap[sessionId] = lastConversationInfo
		return systemPrompt, userPromptByTask
	}

	referenceCatalogItemsString := ""
	if lastConversationInfo.FirstConversion == true {
		workspaceTreeIndexer, ok := fileIndexer.GetWorkspaceTreeFileIndexer()
		if ok {
			referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(3000)
			if referenceCatalogItemsString == "." {
				referenceCatalogItemsString = ""
			}
		}
	}

	customInstructionsStr := ""
	IsEnableProjectRule := params.PluginPayloadConfig.IsEnableProjectRule
	if IsEnableProjectRule {
		customInstructionsStr = systemrule.GetSystemRuleInfo(ctx, params.RequestId, systemrule.AiAgent) //获取project rules的值
		if customInstructionsStr == lastConversationInfo.ProjectRules {
			customInstructionsStr = ""
		} else {
			lastConversationInfo.ProjectRules = customInstructionsStr
		}
	}

	//对contextDetails的去重处理
	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
	log.Infof("%v", contextDetails)
	uniqueContextDetails := deduplicateContextDetails(contextDetails, lastConversationInfo.ContextDetails)
	if len(uniqueContextDetails) > 0 {
		lastConversationInfo.LastContextDetails = lastConversationInfo.ContextDetails
		lastConversationInfo.ContextDetails = contextDetails
	}

	input := prompt.CoderAgentUserPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		WorkspacePath:                  projectPath,
		ReferenceCatalogItemsString:    strings.TrimSpace(referenceCatalogItemsString),
		MemoryPrompt:                   strings.TrimSpace(memoryPrompt),
		ContextDetails:                 uniqueContextDetails,
		UserInputQuery:                 userInputQuery,
		FirstConversion:                lastConversationInfo.FirstConversion,
		HasRulesContext:                hasRuleContext,
		GlobRulesExcludeAlreadyMatched: allGlobRulesExcludeMatched,
		AlwaysAppliedRules:             allAlwaysRules,
		ModelDecisionRules:             allModelDecisionRules,
		UserRules:                      userRules,
		WikiCataloguePrompt:            projectWikiPrompt,
	}
	if mode == definition.SessionModeChat {
		userPromptByTask, err = prompt.EncryptedEngine.RenderAskAgentUserPrompt(input)
	} else {
		//userPromptByTask, err = prompt.Engine.RenderCoderAgentUserPrompt(input)
		userPromptByTask, err = prompt.EncryptedEngine.RenderCoderAgentUserPrompt(input)
	}

	userPromptByTask = strings.TrimSpace(userPromptByTask)
	lastConversationInfo.FirstConversion = false
	//做个兜底，如果ConversationInfoMap是数量太多就清空之前的值，正常会话关闭都会清理的，除非是同时开多个会话或是之前的会话数据没有正常清理掉
	if len(stm.ConversationInfoMap) >= 5 {
		stm.ConversationInfoMap = make(map[string]stm.ConversationInfo)
	}
	stm.ConversationInfoMap[sessionId] = lastConversationInfo
	if err != nil {
		log.Error("Failed to render freeInputContext ", err)
		return systemPrompt, userPromptByTask
	}
	return systemPrompt, userPromptByTask
}

func fetchNewAddedRules(currentProjectRules []*rule.ProjectRule, historyProjectRules []*rule.ProjectRule) []*rule.ProjectRule {
	var newAddedRules []*rule.ProjectRule
	for _, projectRule := range currentProjectRules {
		if len(historyProjectRules) == 0 {
			newAddedRules = append(newAddedRules, projectRule)
		} else {
			sameRule := false
			for _, historyGlobMatchedRule := range historyProjectRules {
				if projectRule.Name == historyGlobMatchedRule.Name {
					sameRule = true
				}
			}
			if !sameRule {
				newAddedRules = append(newAddedRules, projectRule)
			}
		}
	}
	return newAddedRules
}

func fetchRules(projectRuleContext rule.ProjectRuleContext, lastConversationInfo *stm.ConversationInfo) (hasRuleContext bool, allAlwaysRules, allGlobRules, allModelDecisionRules, userRules []*rule.ProjectRule) {
	hasRuleContext = false
	var newGlobMatchedRules []*rule.ProjectRule
	var newUserManualSelectedRules []*rule.ProjectRule
	historyUserManualSelectedRules := lastConversationInfo.HistoryUserManualSelectedRules
	historyNonUserManualSelectedRules := lastConversationInfo.HistoryNonUserManualSelectedRules
	newGlobRules := fetchNewAddedRules(projectRuleContext.AllGlobRules, historyNonUserManualSelectedRules)
	newAlwaysRules := fetchNewAddedRules(projectRuleContext.AllAlwaysRules, historyNonUserManualSelectedRules)
	newModelDecisionRules := fetchNewAddedRules(projectRuleContext.AllModelDecisionRules, historyNonUserManualSelectedRules)
	newGlobMatchedRules = fetchNewAddedRules(projectRuleContext.GlobMatchedRules, historyUserManualSelectedRules)
	newUserManualSelectedRules = fetchNewAddedRules(projectRuleContext.UserManualSelectedRules, historyUserManualSelectedRules)

	// glob matched和用户手动选择的规则都算作用户的规则
	userRules = append(userRules, newGlobMatchedRules...)
	for _, userManualSelectedRule := range newUserManualSelectedRules {
		if !containsRule(allGlobRules, userManualSelectedRule) {
			userRules = append(userRules, userManualSelectedRule)
		}
	}

	// 排除和userRules重复的规则
	for _, globRule := range newGlobRules {
		if !containsRule(userRules, globRule) {
			allGlobRules = append(allGlobRules, globRule)
		}
	}
	for _, r := range newAlwaysRules {
		if !containsRule(userRules, r) {
			allAlwaysRules = append(allAlwaysRules, r)
		}
	}
	for _, r := range newModelDecisionRules {
		if !containsRule(userRules, r) {
			allModelDecisionRules = append(allModelDecisionRules, r)
		}
	}

	if len(allGlobRules) > 0 {
		historyNonUserManualSelectedRules = append(historyNonUserManualSelectedRules, newGlobRules...)
	}
	if len(allAlwaysRules) > 0 {
		historyNonUserManualSelectedRules = append(historyNonUserManualSelectedRules, newAlwaysRules...)
	}
	if len(allModelDecisionRules) > 0 {
		historyNonUserManualSelectedRules = append(historyNonUserManualSelectedRules, newModelDecisionRules...)
	}
	if len(userRules) > 0 {
		historyUserManualSelectedRules = append(historyUserManualSelectedRules, userRules...)
	}

	lastConversationInfo.HistoryUserManualSelectedRules = historyUserManualSelectedRules
	lastConversationInfo.HistoryNonUserManualSelectedRules = historyNonUserManualSelectedRules

	hasRuleContext = len(allGlobRules) > 0 || len(allAlwaysRules) > 0 || len(allModelDecisionRules) > 0 || len(userRules) > 0
	return hasRuleContext, allGlobRules, allAlwaysRules, allModelDecisionRules, userRules
}

func containsRule(rules []*rule.ProjectRule, targetRule *rule.ProjectRule) bool {
	for _, r := range rules {
		if r.Name == targetRule.Name {
			return true
		}
	}
	return false
}

// fetchMemoryPrompt 获取记忆提示词
func fetchMemoryPrompt(ctx context.Context, inputs map[string]any) string {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryFetchEnable, experiment.ConfigScopeClient, true) {
		return ""
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryChatEditEnable, experiment.ConfigScopeClient, true) {
		if chainUtil.IsChatOrEditAgent(inputs) {
			return ""
		}
	}
	if memoryPrompt, ok := inputs[common.KeyLongTermMemoryPrompt]; ok {
		// 如果已经在前置节点获取过，则直接返回
		return memoryPrompt.(string)
	}
	return ""
}

func fetchProjectWikiListPrompt(ctx context.Context, inputs map[string]any) string {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentWikiFetchEnabled, experiment.ConfigScopeClient, true) {
		return ""
	}
	workspacePath, ok := inputs[common.KeyWorkSpacePath].(string)
	if !ok || workspacePath == "" {
		return ""
	}
	sessionId, _ := ctx.Value(common.KeySessionId).(string)
	requestId, _ := ctx.Value(common.KeyRequestId).(string)

	// storage.GlobalStorageService 查询wiki列表
	wikiItems, err := storage.GlobalStorageService.GetWikiItemsByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Failed to fetch project wiki list: %v", err)
		return ""
	}
	if wikiItems == nil || len(wikiItems) <= 0 {
		return ""
	}
	var promptWikiItemDetails []prompt.WikiCatalogueItem
	for _, wikiItem := range wikiItems {
		promptWikiItemDetails = append(promptWikiItemDetails, prompt.WikiCatalogueItem{
			Description: wikiItem.Description,
			Title:       wikiItem.Title,
			Id:          wikiItem.ID,
		})
	}

	promptInput := prompt.AgentWikiCataloguePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		WikiCatalogueItems: promptWikiItemDetails,
	}
	wikiCataloguePrompt, err := prompt.Engine.RenderProjectWikiCataloguePrompt(promptInput)
	if err != nil {
		log.Errorf("Failed to render project wiki prompt: %v", err)
		return ""
	}
	return wikiCataloguePrompt
}

func buildUserPromptWithChatTask(ctx context.Context,
	params *definition.AskParams,
	contextProviderExtras []definition.CustomContextProviderExtra,
	fileIndexer *indexing.ProjectFileIndex,
	inputs map[string]any,
	hasRuleContext bool,
	allGlobRulesExcludeMatched,
	allAlwaysRules,
	allModelDecisionRules,
	userRules []*rule.ProjectRule) (string, error) {
	switch params.ChatTask {
	case definition.FREE_INPUT:
		_, err := chatUtil.TransformFreeInputChatContext(params.ChatContext, fileIndexer)
		if err != nil {
			return "", err
		}
		userQuery := chainUtil.GetUserInputQuery(params.ChatContext)
		if parsedUserQueryValue, ok := inputs[common.KeyParsedUserInputQueryWithContexts]; ok {
			parsedUserQuery := parsedUserQueryValue.(string)
			userQuery = parsedUserQuery
		}
		return userQuery, nil
	case definition.OPTIMIZE_CODE:
		fileIdentifier := ""
		for _, extra := range contextProviderExtras {
			if extra.Name == definition.PlatformContextProviderSelectedCode {
				fileIdentifier = extra.SelectedItem.Identifier
				break
			}
		}
		input := prompt.SysCommandOptimizeCodePromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			Code:                           fileIdentifier + "\n" + chatUtil.FindSelectedCodeContext(contextProviderExtras),
			Language:                       params.CodeLanguage,
			Text:                           chainUtil.GetUserInputQueryWithoutCode(params.ChatContext),
			HasRulesContext:                hasRuleContext,
			GlobRulesExcludeAlreadyMatched: allGlobRulesExcludeMatched,
			ModelDecisionRules:             allModelDecisionRules,
			UserRules:                      userRules,
			AlwaysAppliedRules:             allAlwaysRules,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandOptimizeCodePrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.EXPLAIN_CODE:
		fileIdentifier := ""
		for _, extra := range contextProviderExtras {
			if extra.Name == definition.PlatformContextProviderSelectedCode {
				fileIdentifier = extra.SelectedItem.Identifier
				break
			}
		}
		input := prompt.SysCommandExplainCodePromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			Code:                           fileIdentifier + "\n" + chatUtil.FindSelectedCodeContext(contextProviderExtras),
			Language:                       params.CodeLanguage,
			Text:                           chainUtil.GetUserInputQueryWithoutCode(params.ChatContext),
			HasRulesContext:                hasRuleContext,
			GlobRulesExcludeAlreadyMatched: allGlobRulesExcludeMatched,
			ModelDecisionRules:             allModelDecisionRules,
			UserRules:                      userRules,
			AlwaysAppliedRules:             allAlwaysRules,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandCodeExplainPrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.CODE_GENERATE_COMMENT:
		fileIdentifier := ""
		for _, extra := range contextProviderExtras {
			if extra.Name == definition.PlatformContextProviderSelectedCode {
				fileIdentifier = extra.SelectedItem.Identifier
				break
			}
		}
		input := prompt.SysCommandCodeGenerateCommentPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			Code:                           fileIdentifier + "\n" + chatUtil.FindSelectedCodeContext(contextProviderExtras),
			Language:                       params.CodeLanguage,
			Text:                           chainUtil.GetUserInputQueryWithoutCode(params.ChatContext),
			HasRulesContext:                hasRuleContext,
			GlobRulesExcludeAlreadyMatched: allGlobRulesExcludeMatched,
			ModelDecisionRules:             allModelDecisionRules,
			UserRules:                      userRules,
			AlwaysAppliedRules:             allAlwaysRules,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandCodeGenerateCommentPrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.GENERATE_TESTCASE:
		testcaseContext, err := chatUtil.TransformTestcaseChatContext(ctx, params.ChatContext, fileIndexer)
		if err == nil && testcaseContext.TestCode != "" {
			params.ChatContext = testcaseContext
		}
		input := Convert2GenerateUnittestPromptInput(params.RequestId, params.SessionId, testcaseContext)
		input.HasRulesContext = hasRuleContext
		input.GlobRulesExcludeAlreadyMatched = allGlobRulesExcludeMatched
		input.ModelDecisionRules = allModelDecisionRules
		input.UserRules = userRules
		input.Text = chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
		userPrompt, err := prompt.Engine.RenderSysCommandGenerateUnittestPrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.TERMINAL_EXPLAIN_FIX:
		terminalExplainFixChatContext, _ := chatUtil.TransformTerminalExplainFixChatContext(params.ChatContext, fileIndexer)

		input := prompt.SysCommandTerminalFixPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			Env:               terminalExplainFixChatContext.Env,
			PreferredLanguage: terminalExplainFixChatContext.PreferredLanguage,
			TerminalContent:   terminalExplainFixChatContext.TerminalContent,
		}

		userPrompt, err := prompt.Engine.RenderSysCommandTerminalFixPrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.ERROR_INFO_ASK:
		errorInfoAskContext := definition.ErrorInfoAskChatContext{}

		jsonData, err := json.Marshal(params.ChatContext)
		if err := json.Unmarshal([]byte(jsonData), &errorInfoAskContext); err != nil {
			return "", err
		}
		input := prompt.SysCommandErrorInfoAskPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			PreferredLanguage: errorInfoAskContext.PreferredLanguage,
			Text:              errorInfoAskContext.Text,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandErrorInfoAskPrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	case definition.CODE_PROBLEM_SOLVE:
		problemChatContext, _ := chatUtil.TransformCodeProblemChatContext(params.ChatContext, fileIndexer)

		input := prompt.SysCommandCodeProblemSolvePromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			WorkspaceLanguages:         problemChatContext.WorkspaceLanguages,
			Dependencies:               problemChatContext.Dependencies,
			ErrorMessages:              problemChatContext.ErrorMessages,
			ErrorMessagesWithCodeLines: problemChatContext.ErrorMessageWithCodeLines,
			FilePath:                   problemChatContext.FilePath,
			FileCode:                   problemChatContext.FileCode,
			Language:                   problemChatContext.Language,
			PreferredLanguage:          problemChatContext.PreferredLanguage,
			Text:                       problemChatContext.Text,
		}
		if len(problemChatContext.ErrorMessageWithCodeLines) > 0 {
			input.Version = "2"
		}
		userPrompt, err := prompt.Engine.RenderSysCommandCodeProblemSolvePrompt(input)
		if err != nil {
			return "", err
		}
		return userPrompt, nil
	default:
		userQuery := chainUtil.GetUserInputQuery(params.ChatContext)
		return userQuery, nil
	}
	return "", nil
}

func Convert2GenerateUnittestPromptInput(requestId, sessionId string, testcaseContext definition.TestcaseGenerationChatContext) prompt.SysCommandGenerateUnittestPromptInput {
	input := prompt.SysCommandGenerateUnittestPromptInput{}
	input.BaseInput = prompt.BaseInput{
		RequestId: requestId,
		SessionId: sessionId,
	}
	input.TestDefinitions = testcaseContext.TestDefinitions
	input.FilePath = testcaseContext.FilePath
	input.ContentForTest = testcaseContext.SelectionCode
	input.Labels = testcaseContext.Labels
	input.Language = testcaseContext.Language
	input.PreferredLanguage = testcaseContext.PreferredLanguage
	input.Code = testcaseContext.Code
	input.Text = testcaseContext.Text

	if testcaseContext.TestCode == "" || testcaseContext.FilePath == "" || testcaseContext.FileName == "" {
		input.NewTestcase = false
	} else {
		input.NewTestcase = true
	}

	stringBuilder := &strings.Builder{}
	if testcaseContext.TestPackage != "" {
		stringBuilder.WriteString(testcaseContext.TestPackage + "\n")
	}
	if testcaseContext.TestImports != "" {
		stringBuilder.WriteString(testcaseContext.TestImports + "\n")
	}
	if testcaseContext.TestCode != "" {
		stringBuilder.WriteString(testcaseContext.TestCode)
	}
	input.ContentForTest = stringBuilder.String()
	input.ParameterClassReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestParameter, testcaseContext.ReferenceCodes)
	input.ReturnClassReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestReturnType, testcaseContext.ReferenceCodes)
	input.ExternalClassReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestClass, testcaseContext.ReferenceCodes)
	input.ExternalFunctionReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestExternalFunction, testcaseContext.ReferenceCodes)
	input.ExternalStaticFunctionReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestExternalStaticFunction, testcaseContext.ReferenceCodes)
	input.ExistTestCodeReferenceCodes = extractReferenceCodesWithType(common2.ReferenceTypeUnittestExistTestCode, testcaseContext.ReferenceCodes)
	return input

}

func extractReferenceCodesWithType(referenceType string, referenceCodes []definition.CodeReference) []definition.CodeReference {
	if referenceCodes == nil || len(referenceCodes) <= 0 {
		return nil
	}
	switch referenceType {
	case common2.ReferenceTypeUnittestReturnType:
		{
			var returnTypeReferenceCodes []definition.CodeReference
			for _, referenceCode := range referenceCodes {
				if referenceCode.Type == common2.ReferenceTypeUnittestReturnType {
					returnTypeReferenceCodes = append(returnTypeReferenceCodes, referenceCode)
				}
			}
			return returnTypeReferenceCodes
		}
	case common2.ReferenceTypeUnittestExternalFunction:
		{
			var externalFunctionReferenceCodes []definition.CodeReference
			for _, referenceCode := range referenceCodes {
				if referenceCode.Type == common2.ReferenceTypeUnittestExternalFunction {
					externalFunctionReferenceCodes = append(externalFunctionReferenceCodes, referenceCode)
				}
			}
			return externalFunctionReferenceCodes
		}
	case common2.ReferenceTypeUnittestParameter:
		{
			var parameterReferenceCodes []definition.CodeReference
			for _, referenceCode := range referenceCodes {
				if referenceCode.Type == common2.ReferenceTypeUnittestParameter {
					parameterReferenceCodes = append(parameterReferenceCodes, referenceCode)
				}
			}
			return parameterReferenceCodes
		}
	case common2.ReferenceTypeUnittestExternalStaticFunction:
		{
			var externalStaticFunctionReferenceCodes []definition.CodeReference
			for _, referenceCode := range referenceCodes {
				if referenceCode.Type == common2.ReferenceTypeUnittestExternalStaticFunction {
					externalStaticFunctionReferenceCodes = append(externalStaticFunctionReferenceCodes, referenceCode)
				}
			}
			return externalStaticFunctionReferenceCodes
		}
	case common2.ReferenceTypeUnittestClass:
		{
			var externalClassReferenceCodes []definition.CodeReference
			for _, referenceCode := range referenceCodes {
				if referenceCode.Type == common2.ReferenceTypeUnittestClass {
					externalClassReferenceCodes = append(externalClassReferenceCodes, referenceCode)
				}
			}
			return externalClassReferenceCodes
		}
	}

	return nil
}

func deduplicateContextDetails(newDetails, oldDetails []*prompt.ContextDetail) []*prompt.ContextDetail {
	// 使用 map 来检查重复项
	seen := make(map[string]bool)
	result := []*prompt.ContextDetail{}

	// 首先添加old的 contextDetails
	for _, detail := range oldDetails {
		contextItems := detail.ContextItems
		for _, contextItem := range contextItems {
			if !seen[contextItem.ItemKey] {
				seen[contextItem.ItemKey] = true
			}
		}

	}

	// 然后添加new的 ContextDetails，如果它们不存在的话
	for _, detail := range newDetails {
		contextItems := detail.ContextItems
		for _, contextItem := range contextItems {
			if !seen[contextItem.ItemKey] {
				seen[contextItem.ItemKey] = true
				result = append(result, detail)
				break
			}
		}
	}

	return result
}

func isExcludeMemoryForSystemCommand(chatTask string) bool {
	return chatTask == definition.EXPLAIN_CODE || chatTask == definition.GENERATE_TESTCASE || chatTask == definition.CODE_GENERATE_COMMENT ||
		chatTask == definition.OPTIMIZE_CODE || chatTask == definition.TERMINAL_EXPLAIN_FIX || chatTask == definition.CODE_PROBLEM_SOLVE || chatTask == definition.ERROR_INFO_ASK
}

func buildCustomInstructsPrompt(ctx context.Context, params *definition.AskParams) (string, string, error) {
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	workspace := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	projectPath, _ := workspace.GetWorkspaceFolder()
	var osInfo = ""
	var osVersion, shell string
	osVersion = runtime.GOOS
	// 获取操作系统信息
	hostInfo, err := host.Info()
	if err == nil {
		osVersion = hostInfo.OS + " " + hostInfo.PlatformVersion
		osInfo = hostInfo.OS
	}
	shell = os.Getenv("SHELL")
	var workspaceLanguages []string
	if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
		workspaceLanguages = langStat.GetMostLanguages(0.2)
	}
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	ideInfo := ""
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*definition.IdeConfig)
		if ok {
			ideInfo = ide.IdePlatform + " " + ide.IdeVersion
		}
	}

	systemInput := prompt.CoderAgentSystemPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		WorkspacePath:      projectPath,
		WorkspaceLanguages: workspaceLanguages,
		PreferredLanguage:  preferredLanguage,
		OsInfo:             osInfo,
		OsVersion:          osVersion,
		Shell:              shell,
		IdeInfo:            ideInfo,
	}
	systemPrompt, err := prompt.EncryptedEngine.RenderAskAgentSystemPrompt(systemInput)
	if err != nil {
		return "", "", err
	}
	commandExtraValue, ok := params.Extra[definition.ChatExtraKeyCommand]
	if !ok {
		log.Errorf("invalid command invocation: %+v", params.Extra)
		return "", "", fmt.Errorf("invalid command invocation: %+v", params.Extra)
	}
	commandExtra := definition.CustomCommandExtra{}
	if err := util.UnmarshalToObject(util.ToJsonStr(commandExtraValue), &commandExtra); err != nil {
		log.Errorf("invalid command invocation: %+v", params.Extra)
		return "", "", fmt.Errorf("invalid command invocation: %+v", params.Extra)
	}
	var command *extension.Command
	for _, c := range extension.GlobalExtensionConfig.Commands {
		if c.Identifier == commandExtra.Identifier {
			command = &c
			break
		}
	}
	if command != nil {
		if command.SystemPrompt != "" {
			systemPrompt = systemPrompt + fmt.Sprintf("\nPlease also follow these instructions in of your responses if relevant to user query. No need to acknowledge these instructions directly in your response. \n‹user_custom_instructions>\n%s\n</user_custom_instructions>\n", command.SystemPrompt)

		}
	}
	ideSdk := extension.BuildSdkTool(ctx)
	commandContext, err := buildCommandInvocationContext(params, fileIndexer)
	if err != nil {
		return "", "", fmt.Errorf("build command error")
	}
	customPrompt, invokeErr := extension.ApiExecutor.InvokeCommand(ctx, commandExtra.Identifier, commandContext, ideSdk)
	if invokeErr != nil {
		log.Errorf("invoke command error: %+v", params.Extra)
		return "", "", fmt.Errorf("invoke command error: %+v", params.Extra)
	}
	customPrompt = fmt.Sprintf("<user_query>\n%s\n</user_query>", customPrompt)
	return systemPrompt, customPrompt, nil
}

func buildCommandInvocationContext(params *definition.AskParams, fileIndexer *indexing.ProjectFileIndex) (definition.ExecutionOptions, error) {
	commandContext := definition.ExecutionOptions{
		RequestId: params.RequestId,
	}
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(params.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return definition.ExecutionOptions{}, fmt.Errorf("build commandContext error")
	}
	contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]
	if ok {
		contextExtras := contextExtraValue.([]definition.CustomContextProviderExtra)
		if len(contextExtras) > 0 {
			for _, extra := range contextExtras {
				if len(extra.ParsedContextItems) <= 0 {
					continue
				}
				providerName := extra.Name
				for _, contextItem := range extra.ParsedContextItems {
					commandContext.AssociatedContexts = append(commandContext.AssociatedContexts, definition.ContextItem{
						ContextProviderName: providerName,
						Identifier:          contextItem.Identifier,
						Key:                 contextItem.Key,
						Value:               contextItem.Value,
						Name:                contextItem.Name,
					})
				}
			}
		}
	}

	commandContext.UserInputText = freeInputContext.Text
	commandContext.SelectedCode = freeInputContext.Code

	return commandContext, err
}
