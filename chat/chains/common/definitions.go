package common

import (
	"cosy/definition"
	"cosy/locale"
	"cosy/log"
	_ "embed"
)

const (

	// KnowledgeRagChunksLimit 知识库检索召回的doc数量限制
	KnowledgeRagChunksLimit = 20

	// WorkspaceRagChunksLimit 本地库内rag召回的doc数量限
	WorkspaceRagChunksLimit = 80

	// WorkspaceEntryLimit 需求分析发给模型的工作空间限定成100个目录
	WorkspaceEntryLimit = 100

	// WorkspaceDependenciesLimit 引用的dependencies数量限制
	WorkspaceDependenciesLimit = 200

	Workspace

	WorkspaceRAGRequirementAnalysisWorkspaceTreeTokenLimitRate = float64(0.65)
	WorkspaceRAGRequirementAnalysisDependenciesTokenLimitRate  = float64(0.15)

	// WorkspaceRagVectorRetrieveScoreThreshold 本地向量召回相似度分数门槛
	WorkspaceRagVectorRetrieveScoreThreshold = float64(0.0)

	// WorkspaceGenerateWorkspaceTreeTokenLimitRate 库内生成目录树token限制
	// rate 0.25 * total
	WorkspaceGenerateWorkspaceTreeTokenLimitRate = float64(0.25)

	// WorkspaceGenerateChunkTokenLimitRate 库内生成chunks的token限制
	// rate 0.55 * total
	WorkspaceGenerateChunkTokenLimitRate = float64(0.55)

	// WorkspaceGenerateDependenciesTokenLimitRate 库内生成目录树token限制
	// rate 0.125 * total
	WorkspaceGenerateDependenciesTokenLimitRate = 0.125

	// WorkspaceRerankTopK 检索后处理时，rerank的topK
	WorkspaceRerankTopK = 80

	// CodebaseLLMRerankScoreThreshold 代码库llm rerank的阈值
	CodebaseLLMRerankScoreThreshold = float64(0.0)
)

var (
	KeyChatAskParams = "key_chat_ask_params"

	// KeyAskParamFeatures 新版tag扩展字段
	KeyAskParamFeatures = "key_ask_param_features"

	KeyEnableWorkspaceRag = "key_enable_workspace_rag"

	KeyEnableKnowledgeRag = "key_enable_knowledge_rag"

	KeyKnowledgeRagDocs = "key_knowledge_rag_docs"

	KeyRequestId = "key_request_id"

	KeySessionId = "key_session_id"

	KeyRequestSetId = "key_request_set_id"

	KeyWorkSpacePath = "key_workspace_path"

	// KeyWorkspaceTreeStructList workspace目录文件树 map结构
	KeyWorkspaceTreeStructList = "key_workspace_tree_struct_list"

	// KeyWorkspaceDependencyList workspace依赖列表
	KeyWorkspaceDependencyList = "key_workspace_dependency_list"

	// KeyUserInputQuery 用户提问及圈选code组装的query
	KeyUserInputQuery = "input_key_user_query"

	// KeyParsedUserInputQueryWithContexts 解析后的user query
	KeyParsedUserInputQueryWithContexts = "input_key_parsed_user_query_with_contexts"

	KeyRefinedQuery = "input_key_refine_query"

	KeyRequirementAnalysisResult = "key_requirement_analysis_result"

	KeyWorkspaceRetrieveResult = "key_workspace_retrieve_result"

	KeyWorkspaceTextRetrieveResult = "key_workspace_text_retrieve_result"

	KeyWorkspaceVectorRetrieveResult     = "key_workspace_vector_retrieve_result"
	KeyWorkspaceUserVectorRetrieveResult = "key_workspace_user_vector_retrieve_result"

	KeyWorkspaceRetrieveChunks = "key_workspace_retrieve_chunks"

	KeyWorkspaceRetrieveRerankResult = "key_workspace_retrieve_rerank_result"

	KeyWorkspaceRelevantFileChunks = "key_workspace_relevant_file_chunks"

	KeyChatAskResult = "key_chat_ask_result"

	KeyLocaleLanguage = "key_locale_language"

	KeyPreferredLanguage = "key_preferred_language"

	KeyRequirementAnalysisPrompt = "input_key_requirement_analysis_prompt"

	KeyWorkspaceRagChatPrompt = "input_key_workspace_rag_chat_prompt"

	KeyIntentDetectChatPrompt = "input_key_intent_detect_chat_prompt"

	KeyCoderIntentDetectResult = "input_key_coder_intent_detect_result"

	KeyUIToCodeIntentDetectResult = "input_key_ui_to_code_intent_detect_result"

	KeyRefinedHistoryQuery = "key_refined_history_query"

	KeyContextProviderExtra = "key_context_provider_extra"

	// KeyStatChunkTokenCountUsage 统计token使用量
	KeyStatChunkTokenCountUsage = "key_stat_chunk_token_count_usage"

	// KeyExtraChatMode 额外的问答模式信息，用于路由特别模型
	KeyExtraChatMode = "chat_mode"

	KeyUI2CodeMode = "ui_2_code_mode"

	// KeyEnableWorkspace 由于codebase功能上线与workspace并存，需要预留此标识判断当前是否使用旧版workspace
	KeyEnableWorkspace = "enable_workspace"

	// KeyAIDeveloperIntentDetectResult AI developer意图识别结果
	KeyAIDeveloperIntentDetectResult = "key_ai_developer_intent_detect_result"

	KeyAIDeveloperIntentUnittest = "unittest"

	KeyAIDeveloperIntentUI2FeCode = "ui_to_code"

	KeyModelConfig = "key_model_config"

	// KeyLongTermMemories 在对话开始时召回的长期记忆
	KeyLongTermMemories = "key_long_term_memories"

	// KeyLongTermMemoryPrompt 长期记忆prompt
	KeyLongTermMemoryPrompt = "key_long_term_memory_prompt"

	// KeyNewLongTermMemories 本轮对话新增的长期记忆
	KeyNewLongTermMemories = "key_new_long_term_memories"

	// KeyCommonAssistantIntentDetectResult assistant场景意图识别结果
	KeyCommonAssistantIntentDetectResult = "key_common_assistant_intent_detect_result"

	// KeyRefinedGenernalQuery 用于通用场景的重构query
	KeyRefinedGenernalQuery = "key_refined_genernal_query"

	// KeyCoderAgentContext 通用agent context
	KeyCoderAgentContext = "key_coder_agent_context"

	// KeyCoderAgentRetryUserQuery 通用agent retry时保存的上轮user query
	KeyCoderAgentRetryUserQuery = "key_coder_agent_retry_user_query"

	// KeyDeepwikiAgentContext deepwiki场景的agent context
	KeyDeepwikiAgentContext = "key_deepwiki_agent_context"

	KeyOptimizedCatalogue = "key_wiki_optimized_catalogue"

	KeyCatalogueThink = "key_wiki_catalogue_think"

	KeyCatalogueRawOutput = "key_wiki_catalogue_raw_output"

	KeyCatalogueResult = "key_wiki_catalogue_result"

	KeyWikiContent = "key_wiki_content"

	KeyCurrentCatalogue = "key_wiki_current_catalogue"

	KeyRelatedCommits = "key_wiki_related_commits"

	KeyRelatedCommitsMapping = "key_wiki_related_commits_mapping"

	KeyRepoInfo = "key_wiki_repo_info"

	KeyCreateDeepwikiRequest = "key_create_deepwiki_request"

	KeyReadmeContent   = "key_wiki_readme_content"
	KeyOverviewContent = "key_wiki_overview_content"

	// 恢复相关keys
	KeyRecoveryCheckpoint = "key_recovery_checkpoint"

	// commit增量更新相关keys
	KeyWikiCommitDiff                = "key_wiki_commit_diff"
	KeyWikiCommitDiffAnalysis        = "key_wiki_commit_diff_analysis"
	KeyWikiConversationRagCodes      = "key_wiki_conversation_rag_codes"
	KeyWikiCommitDiffResponse        = "key_wiki_commit_diff_response"
	KeyWikiCommitDiffAnalysisRequest = "key_wiki_commit_diff_analysis_request"
	KeyWikiCodeChunksAnalysisRequest = "key_wiki_code_chunks_analysis_request"

	// catalogue diff 分流相关keys
	KeyWikiUpdateCatalogues         = "key_wiki_update_catalogues"        // 需要更新的catalogues，给update wiki chain使用
	KeyWikiAddCatalogues            = "key_wiki_add_catalogues"           // 需要新增的catalogues，给generate wiki chain使用
	KeyWikiAllProcessedCatalogues   = "key_wiki_all_processed_catalogues" // 所有处理过的catalogues，兜底使用
	KeyOldWikiContent               = "key_old_wiki_content"
	KeyWikiGenerateStat             = "key_wiki_generate_stat"
	KeyWikiGenerateStatCurrentGraph = "key_wiki_generate_stat_of_current_graph"

	KeyProjectRuleContext = "key_project_rule_context"

	KeyAgentMessages = "key_agent_messages"

	KeyModelQueueStatus      = "key_model_queue_status"
	KeyWikiParentCatalogNode = "key_wiki_parent_catalog_node" // 父目录节点
	KeyWikiCatalogLayerLevel = "key_wiki_catalog_layer_level" // 目录节点层级
)

type RequirementAnalysisResponse struct {
	RequestId string
	//dashscope 请求id
	ModelRequestId string
	Text           string
	Usage          definition.LlmUsage
	Success        bool
}

type RequirementAnalysisResult struct {
	//模型分析后的query
	RefinedQuestion string `json:"refined_question"`
	//是否需要修改代码
	//true：库内生成；false：库内问答
	CodeModify string `json:"code_modify"`
	//关键词列表
	Keywords []string `json:"keywords"`
	//相关文件列表
	RelevantFiles []string `json:"related_files"`
}

type IntentDetectResponse struct {
	RequestId string
	//dashscope 请求id
	ModelRequestId string
	Text           string
	Usage          definition.LlmUsage
	Success        bool
}

// IntentDetectionResult 意图识别结果，目前只有代码生成的意图识别，后续可以扩展更多
type IntentDetectionResult struct {
	// 是否为代码生成意图
	IsCoderIntention bool `json:"is_coder_intention"`
}

// UIToCodeIntentDetectionResult UI转代码意图识别结果
type UIToCodeIntentDetectionResult struct {
	// 是否为图生码意图
	IsUIToCodeIntention bool `json:"is_ui_to_code_intention"`
}

type AIDeveloperIntentDetectionResult struct {
	// 具体意图
	Intent string `json:"intent"`
}

type CommonAgentIntentDetectionResult struct {
	// 识别出的待使用的agent
	AgentName string `json:"agent_name"`

	// 意图识别结果
	Intent string `json:"intent"`
}

func (r *RequirementAnalysisResult) IsCodeModify() bool {
	if r.CodeModify == "" || r.CodeModify == "YES" || r.CodeModify == "yes" {
		return true
	}
	if r.CodeModify == "NO" || r.CodeModify == "no" {
		return false
	}
	return true
}

type ChatProcessDescriptionLocale struct {
}

func (c ChatProcessDescriptionLocale) Translate(localeLang, step string) string {
	if localeLang != definition.LocaleEn && localeLang != definition.LocaleZh {
		log.Warnf("illegal locale lang. lang: %s", localeLang)
		return ""
	}
	return locale.Localize(step, localeLang)

}

var ChatProcessDescriptionLoader ChatProcessDescriptionLocale

type ChatTokenCountInfo struct {
	// 输入token数
	PromptTokens int `json:"prompt_tokens"`
	// 输出token数
	CompletionTokens int `json:"completion_tokens"`
	// 缓存的token数
	CachedTokens int `json:"cached_tokens"`
	// 窗口大小token数
	MaxInputTokens int `json:"max_input_tokens"`
}
