package service

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/storage/database"
	"cosy/user"
	"cosy/util/uri"
	"errors"
	"net/http"
	"time"

	"github.com/robfig/cron/v3"
)

var SessionServiceManager *ChatSessionServiceManager

var chatSessionClearTimer *cron.Cron

// 会话对应的工程目录表
var SessionProjectUriMap map[string]string

func InitSessionService() {

	SessionServiceManager = &ChatSessionServiceManager{
		chatSessionTemplate:    database.ChatSessionTemplate,
		chatRecordOrmTemplate:  database.ChatRecordTemplate,
		chatMessageOrmTemplate: database.ChatMessageTemplate,
		httpclient:             client.GetDefaultClient(),
	}

	SessionProjectUriMap = make(map[string]string)

	// Add timer, report heartbeat periodically
	chatSessionClearTimer = cron.New()

	// Report every 3 hour
	_, _ = chatSessionClearTimer.AddFunc("0 */3 * * *", func() {
		SessionServiceManager.ClearExpiredChatSessions()

		log.Info("finish clear expired chat sessions.")
	})

	//每日凌晨2点，清理db释放空间
	_, _ = chatSessionClearTimer.AddFunc("0 2 * * *", func() {
		if database.ChatDbTemplate != nil {
			err := database.ChatDbTemplate.ExecuteDBVacuum()
			if err != nil {
				log.Errorf("execute db Vacuum error: %v", err)
			} else {
				log.Debugf("execute db Vacuum success.")
			}
		}
	})

	chatSessionClearTimer.Start()
}

type ChatSessionServiceManager struct {
	chatSessionTemplate    *database.ChatSessionOrmTemplate
	chatRecordOrmTemplate  *database.ChatRecordOrmTemplate
	chatMessageOrmTemplate *database.ChatMessageOrmTemplate
	httpclient             *http.Client
}

func (service *ChatSessionServiceManager) ClearExpiredChatSessions() {
	if WorkingSpaceServiceManager != nil {
		if sessions, err := service.chatSessionTemplate.GetExpiredChatSessions(); err == nil && len(sessions) > 0 {
			for _, session := range sessions {
				WorkingSpaceServiceManager.ClearSession(session.SessionId)
			}
		}
	}
	service.chatSessionTemplate.ClearExpiredChatSessions()
}

// 排序规则：
// 1. 当前项目优先
// 2. 最近活跃的优先
// onlyV1Session: 是否只展示v1（非agent模式下的会话）
func (service *ChatSessionServiceManager) ListAllChatSessions(workspaceInfo definition.WorkspaceInfo, onlyV1Session bool) []definition.ChatSession {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return make([]definition.ChatSession, 0)
	}
	projectUri := getRootWorkspace(workspaceInfo)
	if projectUri == "" {
		//空工程不展示
		return nil
	}
	var chatSessions []definition.ChatSession
	if onlyV1Session {
		chatSessions = service.chatSessionTemplate.ListV1ChatSessions(userInfo.Uid, userInfo.OrgId)
	} else {
		chatSessions = service.chatSessionTemplate.ListChatSessions(userInfo.Uid, userInfo.OrgId)
	}

	if len(chatSessions) <= 0 {
		return chatSessions
	}
	//chatSessions = sortWithProject(projectUri, chatSessions)
	// 如果有项目URI，直接过滤当前项目的会话
	if projectUri != "" {
		var projectSessions []definition.ChatSession
		for _, session := range chatSessions {
			if session.ProjectURI == projectUri {
				projectSessions = append(projectSessions, session)
			}
		}
		chatSessions = projectSessions
	}

	if len(chatSessions) <= 50 {
		return chatSessions
	}
	return chatSessions[0:50]
}

// GetSessionRecordsForHistoryBuild 构建问答历史，按创建时间升序
// 截至clearContext事件，排除 异常/retry类型的问答记录
func (service *ChatSessionServiceManager) GetSessionRecordsForHistoryBuild(sessionId string) ([]definition.ChatRecord, error) {

	chatRecords, _ := service.chatRecordOrmTemplate.GetSessionChats(sessionId, definition.PageInfoAll)
	if len(chatRecords) <= 0 {
		return make([]definition.ChatRecord, 0), nil
	}
	var validChatRecords = make([]definition.ChatRecord, 0)
	for i := len(chatRecords) - 1; i >= 0; i-- {
		currentRecord := chatRecords[i]
		if currentRecord.ChatTask == definition.CLEAR_CONTEXT {
			break
		}
		if currentRecord.FinishStatus != 0 {
			//忽略异常记录
			continue
		}
		// 在多重重试情况下，本地db记录是retry task，不能忽略了
		//if currentRecord.ChatTask == definition.RETRY_TASK {
		//	continue
		//}
		validChatRecords = PrependToSlice(validChatRecords, currentRecord)
	}
	return validChatRecords, nil
}

func PrependToSlice(slice []definition.ChatRecord, value definition.ChatRecord) []definition.ChatRecord {
	// 创建一个新的切片，长度比原切片长1，并且容量足够容纳原切片的所有元素和新元素
	newSlice := make([]definition.ChatRecord, len(slice)+1, cap(slice)+1)

	// 将新元素放到新切片的第一个位置
	newSlice[0] = value

	// 将原切片的元素复制到新切片的第二个位置开始
	copy(newSlice[1:], slice)

	return newSlice
}

func getRootWorkspace(workspaceInfo definition.WorkspaceInfo) string {
	if len(workspaceInfo.WorkspaceFolders) <= 0 {
		return ""
	}
	return workspaceInfo.WorkspaceFolders[0].URI
}

func sortWithProject(projectUri string, sortedChatSessions []definition.ChatSession) []definition.ChatSession {
	if projectUri == "" {
		return sortedChatSessions
	}
	var sortedProjectSessions []definition.ChatSession
	var restChatSessions []definition.ChatSession
	for _, session := range sortedChatSessions {
		if session.ProjectURI == projectUri {
			sortedProjectSessions = append(sortedProjectSessions, session)
		} else {
			restChatSessions = append(restChatSessions, session)
		}
	}
	return append(sortedProjectSessions, restChatSessions...)
}

// GetChatSession 查询指定会话及records
func (service *ChatSessionServiceManager) GetChatSession(params definition.GetSessionParam) (definition.ChatSession, error) {
	_, uid, _ := user.GetUserIdAndName()
	if uid == "" {
		return definition.ChatSession{}, errors.New("user not login")
	}
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return definition.ChatSession{}, errors.New("user not login")
	}
	chatSession, err := service.chatSessionTemplate.GetChatSession(params.SessionId)
	if err != nil {
		return definition.ChatSession{}, err
	}
	chatRecords, _ := service.chatRecordOrmTemplate.GetSessionChats(params.SessionId, definition.PageInfoAll)
	chatSession.ChatRecords = chatRecords
	return chatSession, nil
}

// CreateChatSession 插入数据
func (service *ChatSessionServiceManager) GetChatSessionProjectURI(sessionId string) string {
	projectUri := SessionProjectUriMap[sessionId]
	if projectUri == "" {
		if existChatSession, err := service.chatSessionTemplate.GetChatSession(sessionId); err == nil {
			projectUri = existChatSession.ProjectURI
			if projectUri != "" {
				SessionProjectUriMap[sessionId] = projectUri
			}
		} else {
			log.Warnf("GetChatSessionProjectURI sessionId: %s, error: %v", sessionId, err)
		}
	}
	return projectUri
}

// CreateChatSession 插入数据
func (service *ChatSessionServiceManager) CreateChatSession(chatSession definition.ChatSession) {
	SessionProjectUriMap[chatSession.SessionId] = chatSession.ProjectURI
	service.chatSessionTemplate.InsertChatSession(chatSession)
}

func (service *ChatSessionServiceManager) CheckCreateChatSession(chatSession definition.ChatSession) {
	existChatSession, _ := service.chatSessionTemplate.GetChatSession(chatSession.SessionId)
	if existChatSession.SessionId == "" {
		SessionProjectUriMap[chatSession.SessionId] = chatSession.ProjectURI
		service.chatSessionTemplate.InsertChatSession(chatSession)
	}
}

func (service *ChatSessionServiceManager) UpdateChatSession(chatSession definition.ChatSession) {
	service.chatSessionTemplate.UpdateChatSession(chatSession)
}

func (service *ChatSessionServiceManager) DeleteChatSession(param definition.DeleteSessionParam) {
	delete(SessionProjectUriMap, param.SessionId)
	if WorkingSpaceServiceManager != nil {
		WorkingSpaceServiceManager.ClearSession(param.SessionId)
	}
	service.chatSessionTemplate.DeleteChatSession(param.SessionId)
}

func (service *ChatSessionServiceManager) ClearUserChatSessions() {
	_, uid, _ := user.GetUserIdAndName()
	if uid == "" {
		return
	}
	if WorkingSpaceServiceManager != nil {
		if sessions, err := service.chatSessionTemplate.GetUserChatSessions(uid); err == nil {
			for _, session := range sessions {
				WorkingSpaceServiceManager.ClearSession(session.SessionId)
			}
		}
	}
	service.chatSessionTemplate.ClearUserChatSessions(uid)
}

func (service *ChatSessionServiceManager) CreateChat(chatRecord definition.ChatRecord) {
	// 插入数据
	service.chatRecordOrmTemplate.InsertChat(chatRecord)

	defer service.UpdateChatSessionModifiedTime(chatRecord.SessionId)

}

func (service *ChatSessionServiceManager) UpdateChat(chatRecord definition.ChatRecord) {
	service.chatRecordOrmTemplate.UpdateChat(chatRecord)
}

func (service *ChatSessionServiceManager) UpdateChatContent(chatRecord definition.ChatRecord) {
	existChatRecord, _ := service.chatRecordOrmTemplate.GetChat(chatRecord)
	if existChatRecord.RequestId != "" {
		//更新记录
		existChatRecord.Answer = chatRecord.Answer
		existChatRecord.FinishStatus = chatRecord.FinishStatus
		existChatRecord.ErrorResult = chatRecord.ErrorResult
		existChatRecord.GmtModified = time.Now().UnixMilli()
		existChatRecord.FilterStatus = chatRecord.FilterStatus
		service.chatRecordOrmTemplate.UpdateChat(existChatRecord)
	} else {
		//新增记录
		service.chatRecordOrmTemplate.InsertChat(chatRecord)
	}
}

func (service *ChatSessionServiceManager) UpdateChatReasoningContent(chatRecord definition.ChatRecord) {
	existChatRecord, _ := service.chatRecordOrmTemplate.GetChat(chatRecord)
	if existChatRecord.RequestId != "" {
		//更新记录
		existChatRecord.ReasoningContent = chatRecord.ReasoningContent
		existChatRecord.GmtModified = time.Now().UnixMilli()
		service.chatRecordOrmTemplate.UpdateChat(existChatRecord)
	}
}

func (service *ChatSessionServiceManager) UpdateChatSessionModifiedTime(chatSessionId string) {
	existChatSession, _ := service.chatSessionTemplate.GetChatSession(chatSessionId)
	if existChatSession.SessionId != "" {
		existChatSession.GmtModified = time.Now().UnixMilli()
		service.chatSessionTemplate.UpdateChatSession(existChatSession)
	}
}

func (service *ChatSessionServiceManager) UpdateChatLike(chatRecord definition.ChatRecord, likeStatus int) {
	existChatRecord, _ := service.chatRecordOrmTemplate.GetChat(chatRecord)
	if existChatRecord.RequestId != "" {
		//更新记录
		existChatRecord.LikeStatus = likeStatus
		existChatRecord.GmtModified = time.Now().UnixMilli()
		service.chatRecordOrmTemplate.UpdateChat(existChatRecord)
	}
}

func (service *ChatSessionServiceManager) GetChat(chatRecord definition.ChatRecord) (definition.ChatRecord, error) {
	return service.chatRecordOrmTemplate.GetChat(chatRecord)
}

func (service *ChatSessionServiceManager) DeleteChat(param definition.DeleteSessionChatParam) error {
	err := service.chatRecordOrmTemplate.DeleteChat(param.SessionId, param.RequestId)
	_ = service.chatMessageOrmTemplate.DeleteChatMessage(param.SessionId, param.RequestId)
	chatRecords, _ := service.chatRecordOrmTemplate.GetSessionChats(param.SessionId, definition.NewPageInfo(1, 20))
	if len(chatRecords) == 0 {
		if WorkingSpaceServiceManager != nil {
			WorkingSpaceServiceManager.ClearSession(param.SessionId)
		}
		service.chatSessionTemplate.DeleteChatSession(param.SessionId)
	}

	stable.GoSafe(context.Background(), func() {
		requestParam := map[string]string{
			"sessionId": param.SessionId,
			"requestId": param.RequestId,
		}
		req, _ := remote.BuildBigModelAuthRequest(http.MethodPost, uri.BuildUrlWithParams(definition.UrlPathDeleteChatByRequestId, requestParam), nil)
		resp, err := service.httpclient.Do(req)
		if err != nil {
			log.Errorf("failed to delete remote chat. error=" + err.Error())
			return
		}
		defer func() {
			if resp != nil {
				resp.Body.Close()
			}
		}()
	}, stable.SceneInlineEdit)

	return err
}

func (service *ChatSessionServiceManager) CreateChatMessage(chatMessage definition.ChatMessage) {
	service.chatMessageOrmTemplate.InsertChatMessage(chatMessage)
}

func (service *ChatSessionServiceManager) GetChatMessageBySession(sessionId string) ([]definition.ChatMessage, error) {
	return service.chatMessageOrmTemplate.ListChatMessageBySession(sessionId)
}
func (service *ChatSessionServiceManager) GetChatMessageByRequest(requestId string) ([]definition.ChatMessage, error) {
	return service.chatMessageOrmTemplate.ListChatMessageByRequest(requestId)
}

func (service *ChatSessionServiceManager) UpdateChatMessageToolResult(id, sessionId, toolResult string) error {
	return service.chatMessageOrmTemplate.UpdateChatMessageToolResult(id, sessionId, toolResult)
}

func (service *ChatSessionServiceManager) UpdateChatMessageTokenInfo(id, tokenInfo string) {
	service.chatMessageOrmTemplate.UpdateChatMessageTokenInfo(id, tokenInfo)
}

func (service *ChatSessionServiceManager) UpdateChatMessageSummary(id, summary string) error {
	return service.chatMessageOrmTemplate.UpdateChatMessageSummary(id, summary)
}

func (service *ChatSessionServiceManager) RemoveChatMessageSummary(id string) error {
	return service.chatMessageOrmTemplate.RemoveChatMessageSummary(id)
}
