package eval

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/experiment"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/sls"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	coderCommon "cosy/chat/agents/coder/common"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

// WikiEvalChain wiki知识补充效果的评估
type WikiEvalChain struct {
}

func NewWikiEvalChain() *WikiEvalChain {
	return &WikiEvalChain{}
}

// WikiAnalysis 表示单条wiki知识的分析结果
type WikiAnalysis struct {
	Analysis      string // 分析内容
	WikiID        int    // Wiki ID
	UsageLevel    int    // 使用程度
	QualityImpact int    // 质量影响
}

// WikiOverallEvaluation 表示wiki总体评估结果
type WikiOverallEvaluation struct {
	TotalScore  int    // 总分
	Suggestions string // 建议
}

// WikiEvaluationResult 包含完整的wiki评估结果
type WikiEvaluationResult struct {
	Analyses []WikiAnalysis        // wiki分析列表
	Overall  WikiOverallEvaluation // 总体评估
}

func (c WikiEvalChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryEvalEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	allowEval, err := c.checkAllowEval(ctx, inputs)
	if err != nil {
		log.Debugf("check allow wiki eval error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if !allowEval {
		return inputs, nil
	}

	// 从agent的短期记忆中提取wiki chunks
	wikiChunks, err := c.extractWikiChunksFromMemory(inputs)
	if err != nil {
		log.Debugf("extract wiki chunks error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if len(wikiChunks) == 0 {
		log.Debugf("no wiki chunks found, requestId=%s", requestId)
		return inputs, nil
	}

	go func() {
		startTime := time.Now()
		messages, wikiChunksMap, err := c.buildEvalRequestFromChunks(requestId, inputs, wikiChunks)
		if err != nil {
			log.Debugf("build wiki eval request error, reason=%v, requestId=%s", err, requestId)
			return
		}
		outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 30*time.Second)
		if err != nil {
			log.Debugf("[wiki]-[eval] wiki eval model error, reason=%v, requestId=%s", err, requestId)
			return
		}
		outputText := outputResp.Text
		evalResult := c.parseOutputFromChunks(inputs, outputText, wikiChunksMap)
		if evalResult == nil {
			log.Debugf("[wiki]-[eval] wiki eval result is nil, model requestId: %s", outputResp.RequestId)
			return
		}
		log.Debugf("[wiki]-[eval] wiki eval result: %d, model requestId: %s time cost: %s", evalResult.Overall.TotalScore, outputResp.RequestId, time.Since(startTime))
		log.Debugf("[wiki]-[eval] wiki eval output: %v", outputText)
	}()
	return inputs, nil
}

// extractWikiChunksFromMemory 从agent的短期记忆中提取search_memory tool调用的结果
func (c WikiEvalChain) extractWikiChunksFromMemory(inputs map[string]any) ([]indexer.CodeChunk, error) {
	// 尝试从chat messages中获取
	requestId := inputs[common.KeyRequestId].(string)
	messages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get chat messages: %w", err)
	}

	var wikiChunks []indexer.CodeChunk

	// 遍历消息，查找search_memory tool的调用结果
	for _, message := range messages {
		if message.Role == "tool" {
			// 方法1: 从ToolResult字段解析
			if message.ToolResult != "" {
				chunks := c.extractChunksFromToolResult(message.ToolResult)
				wikiChunks = append(wikiChunks, chunks...)
			}

			// 方法2: 从Content字段解析（可能直接存储agent message格式）
			if strings.Contains(message.Content, "search_memory") || strings.Contains(message.Content, "code_chunks") {
				chunks := c.extractChunksFromContent(message.Content)
				wikiChunks = append(wikiChunks, chunks...)
			}
		}
	}

	log.Debugf("extracted %d wiki chunks from memory for request %s", len(wikiChunks), requestId)
	return wikiChunks, nil
}

// extractChunksFromToolResult 从ToolResult字段提取chunks
func (c WikiEvalChain) extractChunksFromToolResult(toolResult string) []indexer.CodeChunk {
	var chunks []indexer.CodeChunk

	var toolSyncRequest coderCommon.ToolCallSyncRequest
	err := json.Unmarshal([]byte(toolResult), &toolSyncRequest)
	if err != nil {
		log.Debugf("failed to unmarshal tool result: %v", err)
		return chunks
	}

	// 检查Results字段中是否包含SearchMemoryResponse
	if toolSyncRequest.Results != nil {
		// 尝试将Results转换为SearchMemoryResponse
		resultsBytes, err := json.Marshal(toolSyncRequest.Results)
		if err != nil {
			return chunks
		}

		// 定义临时结构体以避免import循环
		var searchResponse struct {
			CodeChunks []indexer.CodeChunk `json:"code_chunks"`
			Message    string              `json:"message"`
		}
		err = json.Unmarshal(resultsBytes, &searchResponse)
		if err != nil {
			return chunks
		}

		// 判断是否是wiki内容（基于文件路径特征）
		for _, chunk := range searchResponse.CodeChunks {
			if c.isWikiContent(chunk) {
				chunks = append(chunks, chunk)
			}
		}
	}

	return chunks
}

// extractChunksFromContent 从Content字段提取chunks（处理agent message格式）
func (c WikiEvalChain) extractChunksFromContent(content string) []indexer.CodeChunk {
	var chunks []indexer.CodeChunk

	// 尝试解析为agent message格式
	var agentMessage struct {
		Role    string                 `json:"role"`
		Name    string                 `json:"name"`
		Content string                 `json:"content"`
		Extra   map[string]interface{} `json:"extra"`
	}

	err := json.Unmarshal([]byte(content), &agentMessage)
	if err != nil {
		log.Debugf("failed to unmarshal agent message: %v", err)
		return chunks
	}

	// 检查是否是search_memory tool的结果
	if agentMessage.Name == "search_memory" && agentMessage.Extra != nil {
		if toolCallResult, ok := agentMessage.Extra["toolCallResult"].(map[string]interface{}); ok {
			if rawData, ok := toolCallResult["rawData"].(map[string]interface{}); ok {
				extractedChunks := c.extractChunksFromRawData(rawData)
				chunks = append(chunks, extractedChunks...)
			}
		}
	}

	return chunks
}

// isWikiContent 判断CodeChunk是否是wiki内容
func (c WikiEvalChain) isWikiContent(chunk indexer.CodeChunk) bool {
	// 方法1: UUID格式的文件路径（36个字符，包含4个短横线）
	if len(chunk.FilePath) == 36 && strings.Count(chunk.FilePath, "-") == 4 {
		return true
	}

	// 方法2: 检查文件名是否是UUID格式
	if len(chunk.FileName) == 36 && strings.Count(chunk.FileName, "-") == 4 {
		return true
	}

	// 方法3: 检查是否以.md结尾（兼容其他格式）
	if strings.HasSuffix(chunk.FilePath, ".md") {
		return true
	}

	return false
}

// extractChunksFromRawData 从tool result的RawData中提取wiki chunks
func (c WikiEvalChain) extractChunksFromRawData(rawData interface{}) []indexer.CodeChunk {
	var chunks []indexer.CodeChunk

	// 尝试转换为map[string]interface{}
	if dataMap, ok := rawData.(map[string]interface{}); ok {
		if codeChunksData, ok := dataMap["code_chunks"].([]interface{}); ok {
			for _, chunkData := range codeChunksData {
				chunkBytes, err := json.Marshal(chunkData)
				if err != nil {
					continue
				}
				var chunk indexer.CodeChunk
				err = json.Unmarshal(chunkBytes, &chunk)
				if err != nil {
					continue
				}
				if c.isWikiContent(chunk) {
					chunks = append(chunks, chunk)
				}
			}
		}
	}

	return chunks
}

// checkAllowEval 检查是否允许评估
func (c WikiEvalChain) checkAllowEval(ctx context.Context, inputs map[string]any) (bool, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryFetchEnable, experiment.ConfigScopeClient, true) {
		// 是否启用重构查询的配置项
		return false, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryChatEditEnable, experiment.ConfigScopeClient, true) {
		if chainUtil.IsChatOrEditAgent(inputs) {
			return false, nil
		}
	}
	// 总是允许评估，具体的wiki内容检测在extractWikiChunksFromMemory中进行
	return true, nil
}

// buildEvalRequestFromChunks 构建wiki评估请求（基于CodeChunk）
func (c WikiEvalChain) buildEvalRequestFromChunks(requestId string, inputs map[string]any, chunks []indexer.CodeChunk) ([]*agentDefinition.Message, map[string]indexer.CodeChunk, error) {
	systemPrompt, err := prompt.Engine.RenderWikiEvalSystemPrompt(prompt.BaseInput{RequestId: requestId})
	if err != nil {
		return nil, nil, err
	}
	userPrompt, chunksMap, err := c.renderUserPromptFromChunks(chunks, inputs)
	if err != nil {
		return nil, nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), chunksMap, nil
}

// renderUserPromptFromChunks 基于CodeChunk渲染用户prompt（与memory eval保持一致的格式）
func (c WikiEvalChain) renderUserPromptFromChunks(chunks []indexer.CodeChunk, inputs map[string]any) (string, map[string]indexer.CodeChunk, error) {
	// 构建对话信息 - 复用现有的buildChatPrompt逻辑
	conversation, err := c.buildConversation(inputs)
	if err != nil {
		log.Debugf("failed to build conversation, using placeholder: %v", err)
		conversation = "对话信息获取失败，使用占位符"
	}

	sb := strings.Builder{}
	chunksMap := make(map[string]indexer.CodeChunk)

	for index, chunk := range chunks {
		id := fmt.Sprintf("%d", index+1)
		chunksMap[id] = chunk

		// 获取wiki文档的标题（从文件路径中提取或使用内容摘要）
		title := chunk.FilePath
		if chunk.FileName != "" && chunk.FileName != chunk.FilePath {
			title = chunk.FileName
		}

		// 取内容前100个字符作为摘要，与memory eval保持一致的格式
		content := chunk.Content
		if len(content) > 100 {
			content = content[:100] + "..."
		}

		// 使用与memory eval一致的格式：换行分隔
		wikiContent := fmt.Sprintf("%s: %s", title, content)
		sb.WriteString(fmt.Sprintf("<content id=\"%s\">\n%s\n</content>\n", id, wikiContent))
	}

	userPrompt := fmt.Sprintf(`对话消息：
<conversation>
%s
</conversation>

Wiki知识:
<wiki_knowledge>
%s
</wiki_knowledge>

**注意：**
- 如果对话中没有明确遵循和使用Wiki知识信息，禁止编造虚假评分
- 如果对话没有遵循Wiki知识规则，只影响使用程度，即0分，不影响质量分`, conversation, sb.String())

	return userPrompt, chunksMap, nil
}

// buildConversation 构建对话信息（参考MemoryEvalChain的实现）
func (c WikiEvalChain) buildConversation(inputs map[string]any) (string, error) {
	var conversation string
	var err error
	if !chainUtil.IsChatOrEditAgent(inputs) {
		conversation, err = c.buildMessagePrompt(inputs)
	}
	if conversation == "" || err != nil {
		conversation, err = c.buildChatPrompt(inputs)
	}
	return conversation, err
}

// buildChatPrompt 构建聊天提示（参考MemoryEvalChain的实现）
func (c WikiEvalChain) buildChatPrompt(inputs map[string]any) (string, error) {
	sessionId := inputs[common.KeySessionId].(string)
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQuery(rawInputParams.ChatContext)
	userQuery = codeBlockRegex.ReplaceAllString(userQuery, "")
	if newQuery, ok := inputs[common.KeyRefinedGenernalQuery]; ok {
		userQuery = newQuery.(string)
	}
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if validRecords == nil || len(validRecords) <= 0 {
		return "", fmt.Errorf("sessionId=%s, no valid records", sessionId)
	}
	currentRecord := validRecords[len(validRecords)-1]
	answer := currentRecord.Answer
	sb := strings.Builder{}
	sb.WriteString(fmt.Sprintf("[user]: %s\n", userQuery))
	sb.WriteString(fmt.Sprintf("[assistant]: %s\n", answer))
	return sb.String(), nil
}

// buildMessagePrompt 构建消息提示（参考MemoryEvalChain的实现）
func (c WikiEvalChain) buildMessagePrompt(inputs map[string]any) (string, error) {
	requestId := inputs[common.KeyRequestId].(string)
	messages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		return "", err
	}
	if len(messages) <= 0 {
		return "", fmt.Errorf("requestId=%s, no messages", requestId)
	}
	flatMessage := util.FlattenConversation(messages)
	return flatMessage, nil
}

// parseOutputFromChunks 解析评估输出（基于CodeChunk）
func (c WikiEvalChain) parseOutputFromChunks(inputs map[string]any, outputText string, chunksMap map[string]indexer.CodeChunk) *WikiEvaluationResult {
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)

	parseResult := ParseWikiEvalOutput(outputText)
	if parseResult == nil {
		log.Warnf("parse wiki eval output error, requestId=%s", requestId)
		return nil
	}

	evalData := map[string]any{}
	evalData["totalScore"] = strconv.Itoa(parseResult.Overall.TotalScore)
	evalData["suggestions"] = parseResult.Overall.Suggestions
	evalItems := []map[string]any{}

	for _, analysis := range parseResult.Analyses {
		evalItem := map[string]any{}
		chunk, ok := chunksMap[strconv.Itoa(analysis.WikiID)]
		if !ok {
			continue
		}
		evalItem["analysis"] = analysis.Analysis
		evalItem["wikiId"] = strconv.Itoa(analysis.WikiID)
		evalItem["wikiFilePath"] = chunk.FilePath
		evalItem["wikiFileName"] = chunk.FileName
		evalItem["wikiUsageLevel"] = strconv.Itoa(analysis.UsageLevel)
		evalItem["wikiQualityImpact"] = strconv.Itoa(analysis.QualityImpact)
		// wiki内容摘要（前200字符）
		content := chunk.Content
		if len(content) > 200 {
			content = content[:200] + "..."
		}
		evalItem["wikiContentSummary"] = content

		evalItems = append(evalItems, evalItem)
	}

	evalData["evalItems"] = evalItems
	evalDataJson, _ := json.Marshal(evalData)
	eventData := map[string]string{
		"requestId":       requestId,
		"sessionId":       sessionId,
		"totalScore":      strconv.Itoa(parseResult.Overall.TotalScore),
		"modelRequestId":  "", // modelRequestId is not available in this context
		"evalData":        string(evalDataJson),
		"wikiChunksCount": strconv.Itoa(len(chunksMap)),
	}
	go sls.Report(sls.EventTypeChatWikiEval, requestId, eventData)

	return parseResult
}

// getStringFromMetadata 安全地从metadata中获取字符串值
func getStringFromMetadata(metadata map[string]interface{}, key string) string {
	if value, ok := metadata[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// ParseWikiEvalOutput 解析wiki评估输出（适配memory eval prompt的实际输出格式）
func ParseWikiEvalOutput(outputText string) *WikiEvaluationResult {
	// 首先移除开头的记忆分析标记（实际模型输出格式）
	outputText = strings.TrimPrefix(outputText, "===Wiki分析===")
	outputText = strings.TrimSpace(outputText)

	// 分割记忆分析和总体评估部分
	parts := strings.Split(outputText, "===总体评估===")
	if len(parts) == 0 {
		log.Debugf("[wiki]-[eval] no parts found, returning nil")
		return nil
	}

	// 处理分析部分
	analysisText := strings.TrimSpace(parts[0])
	log.Debugf("[wiki]-[eval] analysis text: %s", analysisText)
	var analyses []string

	// 使用空行分割每条分析
	currentAnalysis := ""
	for _, line := range strings.Split(analysisText, "\n") {
		if strings.TrimSpace(line) == "" {
			if currentAnalysis != "" {
				analyses = append(analyses, strings.TrimSpace(currentAnalysis))
				currentAnalysis = ""
			}
		} else {
			if currentAnalysis != "" {
				currentAnalysis += "\n"
			}
			currentAnalysis += line
		}
	}
	// 添加最后一条分析（如果有）
	if currentAnalysis != "" {
		analyses = append(analyses, strings.TrimSpace(currentAnalysis))
	}

	log.Debugf("[wiki]-[eval] found %d analyses", len(analyses))

	// 处理总体评估部分
	overallEval := ""
	if len(parts) > 1 {
		overallEval = "===总体评估===\n" + strings.TrimSpace(parts[1])
	}

	// 调用ParseWikiEvaluation进行结构化
	result := ParseWikiEvaluation(analyses, overallEval)
	if result == nil {
		log.Debugf("[wiki]-[eval] ParseWikiEvaluation returned nil")
	} else {
		log.Debugf("[wiki]-[eval] ParseWikiEvaluation success with %d analyses", len(result.Analyses))
	}
	return result
}

// ParseWikiEvaluation 解析并结构化wiki评估信息
func ParseWikiEvaluation(analyses []string, overallEval string) *WikiEvaluationResult {
	result := &WikiEvaluationResult{
		Analyses: make([]WikiAnalysis, 0),
	}

	// 解析每条wiki分析
	for i, analysis := range analyses {
		wikiAnalysis := parseWikiAnalysis(analysis)
		if wikiAnalysis != nil {
			result.Analyses = append(result.Analyses, *wikiAnalysis)
		} else {
			log.Debugf("[wiki]-[eval] failed to parse analysis %d", i)
		}
	}
	if len(result.Analyses) == 0 {
		log.Debugf("[wiki]-[eval] no valid analyses found, returning nil")
		return nil
	}

	// 解析总体评估
	if overallEval != "" {
		result.Overall = parseWikiOverallEvaluation(overallEval)
	}

	return result
}

// parseWikiAnalysis 解析单条wiki分析（适配memory eval的输出格式）
func parseWikiAnalysis(content string) *WikiAnalysis {
	var analysis WikiAnalysis

	// 使用字符串处理来提取信息
	lines := strings.Split(content, "\n")
	validCount := 0
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 提取分析内容
		if strings.HasPrefix(line, "分析:") {
			analysis.Analysis = strings.TrimPrefix(line, "分析:")
			analysis.Analysis = strings.TrimSpace(analysis.Analysis)
			validCount++
			continue
		}

		// 提取Wiki ID（实际模型输出格式）
		if strings.HasPrefix(line, "Wiki ID:") {
			idStr := strings.TrimPrefix(line, "Wiki ID:")
			idStr = strings.TrimSpace(idStr)
			id, err := strconv.Atoi(idStr)
			if err == nil {
				analysis.WikiID = id
				validCount++
			} else {
				log.Debugf("[wiki]-[eval] failed to parse wiki ID: %s, error: %v", idStr, err)
			}
			continue
		}

		// 提取使用程度
		if strings.HasPrefix(line, "使用程度:") {
			levelStr := strings.TrimPrefix(line, "使用程度:")
			levelStr = strings.TrimSpace(levelStr)
			level, err := strconv.Atoi(levelStr)
			if err == nil {
				analysis.UsageLevel = level
				validCount++
			} else {
				log.Debugf("[wiki]-[eval] failed to parse usage level: %s, error: %v", levelStr, err)
			}
			continue
		}

		// 提取质量影响
		if strings.HasPrefix(line, "质量影响:") {
			impactStr := strings.TrimPrefix(line, "质量影响:")
			impactStr = strings.TrimSpace(impactStr)
			impact, err := strconv.Atoi(impactStr)
			if err == nil {
				analysis.QualityImpact = impact
				validCount++
			} else {
				log.Debugf("[wiki]-[eval] failed to parse quality impact: %s, error: %v", impactStr, err)
			}
			continue
		}
	}

	log.Debugf("[wiki]-[eval] parseWikiAnalysis summary: validCount=%d, analysis=%s, wikiID=%d",
		validCount, analysis.Analysis, analysis.WikiID)

	// 只有当至少有分析内容和ID时才返回结果
	if validCount >= 2 && analysis.Analysis != "" && analysis.WikiID > 0 {
		log.Debugf("[wiki]-[eval] parseWikiAnalysis success")
		return &analysis
	}
	log.Debugf("[wiki]-[eval] parseWikiAnalysis failed: insufficient valid fields")
	return nil
}

// parseWikiOverallEvaluation 解析wiki总体评估
func parseWikiOverallEvaluation(content string) WikiOverallEvaluation {
	var eval WikiOverallEvaluation

	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 提取总分
		if strings.HasPrefix(line, "总分:") {
			scoreStr := strings.TrimPrefix(line, "总分:")
			scoreStr = strings.TrimSpace(scoreStr)
			scoreStr = strings.TrimSuffix(scoreStr, "分")
			score, err := strconv.Atoi(scoreStr)
			if err == nil {
				eval.TotalScore = score
			}
			continue
		}

		// 提取建议
		if strings.HasPrefix(line, "建议:") {
			eval.Suggestions = strings.TrimPrefix(line, "建议:")
			eval.Suggestions = strings.TrimSpace(eval.Suggestions)
			continue
		}
	}

	// 如果没有解析到总体评估，基于单项分析计算默认值
	if eval.TotalScore == 0 && eval.Suggestions == "" {
		log.Debugf("[wiki]-[eval] no overall evaluation found, using default values")
		eval.TotalScore = 5 // 默认中等评分
		eval.Suggestions = "Wiki知识库信息已被分析，建议查看具体项目的使用情况"
	}

	return eval
}

func (c WikiEvalChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c WikiEvalChain) GetInputKeys() []string {
	return []string{}
}

func (c WikiEvalChain) GetOutputKeys() []string {
	return []string{}
}

func (c WikiEvalChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
