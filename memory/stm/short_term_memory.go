package stm

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	chainUtil "cosy/chat/chains/chain"
	chainsCommon "cosy/chat/chains/common"
	"cosy/extension/rule"
	"regexp"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	//"cosy/chat/agents/coder"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	cosyDf "cosy/definition"
	logger "cosy/log"
	"cosy/prompt"
	"cosy/util"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
)

type ConversationInfo struct {
	FirstConversion                   bool `default:"true"`
	ProjectRules                      string
	MemoryPrompt                      string
	ContextDetails                    []*prompt.ContextDetail
	LastContextDetails                []*prompt.ContextDetail
	HistoryUserManualSelectedRules    []*rule.ProjectRule
	HistoryNonUserManualSelectedRules []*rule.ProjectRule
}

func NewConversationInfo() ConversationInfo {
	return ConversationInfo{
		FirstConversion: true,
	}
}

var (
	ConversationInfoMap = make(map[string]ConversationInfo)
)

// 从db中获取会话历史，通用研发Agent的场景，以及智能问答和AI程序员的对话数据的处理
func GetMessageHistory(ctx context.Context, sessionId string) ([]*definition.Message, bool, bool) {
	// 先根据session Id 获取所有的chat_record
	// 如果chat_record的类型不是 Agent 则进行信息转换为一对message
	// 如果chat_record的类型是 Agent 则从chat_message中获取对话历史message
	messages := make([]*definition.Message, 0)
	removeSummaryChatMessageIds := make([]string, 0)
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil {
		log.Fatalf("complete feedback context error. err: %v", err)
		return nil, false, false
	}
	hasAskMode := false
	//1 智能问答中的企业自定义指令    	chatPrompt
	//2 AI程序员普通问答  			chatPrompt
	//3 AI程序员多文件编辑 			chatPrompt
	//4 AI程序员图生码				chatPrompt
	//5 AI程序员testagent 			skip
	//6 智能问答中的普通问答			chatPrompt，会话恢复中需要用到
	//7 智能问答中的预置指令           server端拼装，会话恢复中需要用到(新tab会改造)
	for _, record := range records {
		chatTaskType := record.ChatTask
		intentionType := record.IntentionType
		if record.Mode == cosyDf.SessionModeLongRunningSub {
			// 跳过子agent的记录
			continue
		}
		// common agent
		if intentionType == cosyDf.AIDeveloperIntentDetectCommonAgent {
			chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(record.RequestId)
			if err != nil {
				log.Printf("Error getting chat messages from database: %v", err)
				continue
			}
			for _, chatMessage := range chatMessages {
				if "system" == chatMessage.Role {
					//system role的信息不用构造返回，每次新的对话会实时组装
					continue
				}
				if chatMessage.Summary != "" {
					removeSummaryChatMessageIds = append(removeSummaryChatMessageIds, chatMessage.Id)
					//如果有总结的内容，获取summary消息列表
					messages = make([]*definition.Message, 0)
					var summaryMessages []*definition.Message
					if err := json.Unmarshal([]byte(chatMessage.Summary), &summaryMessages); err == nil {
						//使用从summary表获取的消息列表
						messages = append(messages, summaryMessages...)
					} else {
						// 兼容旧数据，该字段存储的直接是summary内容
						messages = append(messages, &definition.Message{
							Content: chatMessage.Summary,
							Role:    definition.RoleTypeUser,
						})
					}
					continue
				}
				var message definition.Message
				err := json.Unmarshal([]byte(chatMessage.Content), &message)
				if err != nil {
					log.Printf("Error unmarshalling message from JSON: %v", err)
					continue // Skip this message and continue with the next one
				}
				//清除Extra的内容
				message.Extra = nil
				messages = append(messages, &message)
			}
			if len(messages) > 0 {
				lastMessage := messages[len(messages)-1]
				// 最近一条的message是assistant同时存在tool call需要补充一个tool的message，不然接口会报错
				if lastMessage.Role == definition.RoleTypeAssistant {
					toolCalls := lastMessage.ToolCalls
					if toolCalls != nil && len(toolCalls) > 0 {
						toolCall := lastMessage.ToolCalls[0]
						toolCallResult := "The tool invocation was canceled."
						callResponse := &definition.Message{
							Role:       definition.RoleTypeTool,
							ToolCallID: toolCall.ID,
							Name:       toolCall.Function.Name,
							Content:    toolCallResult,
						}
						messages = append(messages, callResponse)
					}
				}
				if record.Mode == cosyDf.SessionModeChat {
					hasAskMode = true
				}
			}
			// 系统指令
		} else if chatTaskType == cosyDf.EXPLAIN_CODE || chatTaskType == cosyDf.GENERATE_TESTCASE ||
			chatTaskType == cosyDf.CODE_GENERATE_COMMENT || chatTaskType == cosyDf.OPTIMIZE_CODE {
			userPrompt, err := buildUserPromptWithChatTask(ctx, record)
			if err != nil {
				userMessage := &definition.Message{Role: definition.RoleTypeUser, Content: record.Question}
				messages = append(messages, userMessage)
			} else {
				userMessage := &definition.Message{Role: definition.RoleTypeUser, Content: userPrompt}
				messages = append(messages, userMessage)
			}
			assistantMessage := &definition.Message{Role: definition.RoleTypeAssistant, Content: record.Answer}
			messages = append(messages, assistantMessage)
			hasAskMode = true
		} else if common.KeyAIDeveloperIntentUnittest == record.IntentionType {
			//testagent先跳过，最终的值没有体现在answer中，不是很好拼llm的回答
			continue
		} else {
			answer := record.Answer
			if "" == answer {
				//如果没有answer则忽略掉
				continue
			}
			answer = removeCodeChunk(answer)
			chatContextMap := make(map[string]interface{})
			util.UnmarshalToObject(record.ChatContext, &chatContextMap)
			if chatPrompt, ok2 := chatContextMap["chatPrompt"].(string); ok2 && chatPrompt != "" {
				//存在chatPrompt，直接使用
				userMessage := &definition.Message{Role: definition.RoleTypeUser, Content: chatPrompt}
				messages = append(messages, userMessage)
			} else {
				userMessage := &definition.Message{Role: definition.RoleTypeUser, Content: record.Question}
				messages = append(messages, userMessage)
			}
			assistantMessage := &definition.Message{Role: definition.RoleTypeAssistant, Content: record.Answer}
			messages = append(messages, assistantMessage)
			hasAskMode = true
		}
	}

	// 移除除最后一条以外的summary
	if len(removeSummaryChatMessageIds) > 1 {
		for _, id := range removeSummaryChatMessageIds[:len(removeSummaryChatMessageIds)-1] {
			service.SessionServiceManager.RemoveChatMessageSummary(id)
		}
	}
	return messages, hasAskMode, true
}

func removeCodeChunk(answer string) string {
	// 匹配代码块开始的格式: ```language::filename::uuid
	pattern := "^```([a-zA-Z]+)::(.*?)::([a-f0-9-]{36})$"
	re, err := regexp.Compile(pattern)
	if err != nil {
		return answer
	}

	lines := strings.Split(answer, "\n")
	var builder strings.Builder
	inCodeBlock := false

	for i := 0; i < len(lines); i++ {
		line := lines[i]

		// 检查是否匹配代码块开始标记
		if matches := re.FindStringSubmatch(line); matches != nil {
			// 找到代码块开始,设置标记
			inCodeBlock = true
			if len(matches) == 4 {
				filename := matches[2]
				builder.WriteString(filename)
			}
			continue
		}

		// 检查代码块结束标记
		if line == "```" && inCodeBlock {
			inCodeBlock = false
			continue
		}

		// 如果不在代码块内,保留该行
		if !inCodeBlock {
			builder.WriteString(line)
			builder.WriteString("\n")
		}
	}

	return builder.String()
}

func processCodeChunk(answer string) string {
	// 处理之前的代码格式，避免生成的fileId有问题导致展示出错
	pattern := "^```([a-zA-Z]+)::(.*?)::([a-f0-9-]{36})$"

	re, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Println("Error compiling regex:", err)
		return answer
	}

	// 按行分割文本
	lines := strings.Split(answer, "\n")
	var builder strings.Builder
	for _, line := range lines {
		if line == "" {
			builder.WriteString("\n")
			continue // 跳过空行
		}

		matches := re.FindStringSubmatch(line)
		if len(matches) == 4 {
			language := matches[1]
			filename := matches[2]
			// 去除语言之后的文字
			trimmedLine := "```" + language + "::" + filename
			builder.WriteString(trimmedLine)
		} else {
			builder.WriteString(line)
		}
		builder.WriteString("\n")
	}
	return builder.String()
}

// 增加会话历史
func AddMessageHistory(sessionId string, requestId string, message *definition.Message) {
	// 将 message 序列化为 JSON
	messageJSON, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshalling message to JSON: %v", err)
		// 考虑是否需要在这里返回错误
	}
	var messageMap map[string]interface{}
	err = json.Unmarshal(messageJSON, &messageMap)
	if err != nil {
		log.Printf("Error unmarshalling JSON: %v", err)
		// 考虑是否需要在这里返回错误
	}
	var chatMessage = cosyDf.ChatMessage{
		Id:        uuid.NewString(),
		RequestId: requestId,
		SessionId: sessionId,
		Content:   string(messageJSON),
		Role:      messageMap["role"].(string),
		GmtCreate: time.Now().UnixMilli(),
	}
	if message.Role == definition.RoleTypeTool {
		//记录到chat_message表
		keyToolCallRequest, ok := message.Extra[coderCommon.KeyToolCallRequest].(*coderCommon.ToolCallSyncRequest)
		if ok {
			resultStr, err := json.Marshal(keyToolCallRequest)
			if err != nil {
				log.Printf("Error unmarshalling tool result JSON: %v", err)
				return
			}
			chatMessage.ToolResult = string(resultStr)
			message.Extra[coderCommon.KeyToolCallRequest] = nil
		}
	}

	if message.Role == definition.RoleTypeAssistant {
		tokenInfo := chainsCommon.ChatTokenCountInfo{
			PromptTokens:     message.ResponseMeta.Usage.PromptTokens,
			CompletionTokens: message.ResponseMeta.Usage.CompletionTokens,
			CachedTokens:     message.ResponseMeta.Usage.PromptTokensDetails.CachedTokens,
		}
		tokenInfoJson, err := json.Marshal(tokenInfo)
		if err != nil {
			logger.Errorf("Error marshalling tokenInfo JSON: %v", err)
		} else {
			tokenInfoJsonStr := string(tokenInfoJson)
			logger.Debugf("[single assistant token usage count]sessionId:%v,requestId:%v;data:%v", sessionId, requestId, tokenInfoJsonStr)
			chatMessage.TokenInfo = tokenInfoJsonStr
		}
	}

	service.SessionServiceManager.CreateChatMessage(chatMessage)
}

// GetMessageHistoryByRequestId 根据requestId 获取对话的message信息
func GetMessageHistoryByRequestId(requestId string) ([]*definition.Message, bool) {
	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		log.Printf("Error getting chat messages from database: %v", err)
		return nil, false
	}

	var messages []*definition.Message
	for _, chatMessage := range chatMessages {
		var message definition.Message
		err := json.Unmarshal([]byte(chatMessage.Content), &message)
		if err != nil {
			log.Printf("Error unmarshalling message from JSON: %v", err)
			continue // Skip this message and continue with the next one
		}
		messages = append(messages, &message)
	}

	return messages, len(messages) > 0
}

func buildUserPromptWithChatTask(ctx context.Context, record cosyDf.ChatRecord) (string, error) {
	lastContext := map[string]any{}
	err := json.Unmarshal([]byte(record.ChatContext), &lastContext)
	if err != nil {
		return "", nil
	}
	switch record.ChatTask {
	case cosyDf.EXPLAIN_CODE:
		userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
		return userQuery, nil
	case cosyDf.OPTIMIZE_CODE:
		contextProviderExtras, err := getCustomContextProviderExtra(record)
		if err != nil {
			userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
			return userQuery, nil
		}
		input := prompt.SysCommandOptimizeCodePromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: record.RequestId,
				SessionId: record.SessionId,
			},
			Code:     chatUtil.FindSelectedCodeContext(contextProviderExtras),
			Language: record.CodeLanguage,
			Text:     record.Question,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandOptimizeCodePrompt(input)
		if err != nil {
			userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
			return userQuery, nil
		}
		return userPrompt, nil
	case cosyDf.CODE_GENERATE_COMMENT:
		contextProviderExtras, err := getCustomContextProviderExtra(record)
		if err != nil {
			userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
			return userQuery, nil
		}
		input := prompt.SysCommandCodeGenerateCommentPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: record.RequestId,
				SessionId: record.SessionId,
			},
			Code:     chatUtil.FindSelectedCodeContext(contextProviderExtras),
			Language: record.CodeLanguage,
			Text:     record.Question,
		}
		userPrompt, err := prompt.Engine.RenderSysCommandCodeGenerateCommentPrompt(input)
		if err != nil {
			userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
			return userQuery, nil
		}
		return userPrompt, nil
	case cosyDf.GENERATE_TESTCASE:
		userQuery := record.Question + chainUtil.GetUserInputQuery(lastContext)
		return userQuery, nil
	}
	return record.Question, nil
}

func getCustomContextProviderExtra(record cosyDf.ChatRecord) ([]cosyDf.CustomContextProviderExtra, error) {
	if record.Extra == "" {
		return []cosyDf.CustomContextProviderExtra{}, nil
	}

	var extraContext map[string]json.RawMessage
	err := json.Unmarshal([]byte(record.Extra), &extraContext)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal Extra: %w", err)
	}

	contextValue, ok := extraContext["context"]
	if !ok {
		return []cosyDf.CustomContextProviderExtra{}, nil
	}

	var contextProviderExtras []cosyDf.CustomContextProviderExtra
	err = json.Unmarshal(contextValue, &contextProviderExtras)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal context: %w", err)
	}

	return contextProviderExtras, nil
}
