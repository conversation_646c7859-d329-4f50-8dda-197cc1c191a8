package definition

import "time"

const (

	// ExperimentKeyWorkspaceRagChunkTopK 库内rag重排序后取的topK个文档
	ExperimentKeyWorkspaceRagChunkTopK = "workspace.rag.chunk.topk"

	// ExperimentKeyWorkspaceRagDependenciesLimit 引用的dependencies数量限制
	ExperimentKeyWorkspaceRagDependenciesLimit = "workspace.rag.dependencies.limit"

	// ExperimentKeyWorkspaceRagVectorRetrieveScoreThreshold 本地向量召回相似度分数门槛
	ExperimentKeyWorkspaceRagVectorRetrieveScoreThreshold = "workspace.rag.vector.retrieve.score.threshold"

	// ExperimentKeyKnowledgeRagChunkTopK 知识库检索召回的topk doc数量限制
	ExperimentKeyKnowledgeRagChunkTopK = "knowledge.rag.chunk.topk"

	// ExperimentKeyWorkspaceRagEmbeddingRecallStrategy 本地embedding召回策略
	//- refined_query_only 仅对refined_query进行向量化和召回
	//- both 对原始query和refined_query_only同时向量化和召回
	ExperimentKeyWorkspaceRagEmbeddingRecallStrategy = "workspace.rag.embedding.recall.strategy"

	// ExperimentKeyWorkspaceRagTextRetrieveBoostParameters 库内全文检索的参数，json格式
	ExperimentKeyWorkspaceRagTextRetrieveBoostParameters = "workspace.rag.text.retrieve.boost.parameters"

	// ExperimentKeyWorkspaceRagRerankStrategy 库内rag重排序算法
	// - rrf_merge
	// - vector_merge
	ExperimentKeyWorkspaceRagRerankStrategy = "workspace.rag.rerank.strategy"

	// ExperimentKeyEnableCompletionRag 是否开启补全的rag能力
	ExperimentKeyEnableCompletionRag = "completion.rag.enable"

	// ExperimentKeyEnableCompletionCodebaseRag 是否开启整个代码库范围的补全rag能力
	ExperimentKeyEnableCompletionCodebaseRag = "completion.codebase.rag.v2.enable"

	// ExperimentKeyCompletionCodebaseRagLang 库内补全rag能力支持的语言
	ExperimentKeyCompletionCodebaseRagLang = "completion.codebase.rag.lang"

	// ExperimentKeyCompletionCodebaseRagThreshold 整个代码库范围的补全rag的阈值，如果没有阈值，则不卡阈值
	ExperimentKeyCompletionCodebaseRagThreshold = "completion.codebase.rag.threshold"

	// ExperimentKeyEnableCompletionCodebaseRagActiveFile 整个代码库范围的补全rag是否启用对最近激活文件的优先级
	ExperimentKeyEnableCompletionCodebaseRagActiveFile = "completion.codebase.rag.active.file.enable"

	// ExperimentKeyCompletionCodebaseRagMaxCount 整个代码库范围的补全rag最大召回数量
	ExperimentKeyCompletionCodebaseRagMaxCount = "completion.codebase.rag.max.count"

	// ExperimentKeyEnableCompletionPrefixCacheRerank 是否启用针对补全prefix cache的引用排序优化
	ExperimentKeyEnableCompletionPrefixCacheRerank = "completion.prefix.cache.rerank.v2.enable"

	// ExperimentKeyEnableCompletionPrefixTruncateCache 是否当补全针对上文截断时启用缓存，尽可能减伤上文变动
	ExperimentKeyEnableCompletionPrefixTruncateCache = "completion.prefix.truncate.cache.enable"

	// ExperimentKeyEnableQueryRefFromIDE 是否允许从IDE中获取引用信息
	ExperimentKeyEnableQueryRefFromIDE = "query.ref.from.ide.enable"

	// ExperimentKeyExcludeQueryRefFromIDELang 排除从IDE中获取引用信息的语言及IDE组合列表 jetbrains-java,vscode-javascript
	ExperimentKeyExcludeQueryRefFromIDELang = "query.ref.from.ide.exclude.lang"

	// ExperimentKeyEnableCompletionRecursiveSimilar 是否允许启用递归相似度兜底
	ExperimentKeyEnableCompletionRecursiveSimilar = "completion.recursive.similar.enable"

	// ExperimentKeyCompletionSimilarMaxChunkSize 补全相似度片段的最大长度
	ExperimentKeyCompletionSimilarMaxChunkSize = "completion.similar.max.chunk.size"

	// ExperimentKeyCompletionSimilarMaxChunkTokenSize 补全相似度片段的最大token长度
	ExperimentKeyCompletionSimilarMaxChunkTokenSize = "completion.similar.max.chunk.tokens.size"

	// ExperimentKeyCompletionSimilarMaxMatchTokenSize 相似代码计算中用于匹配部分的最大token长度
	ExperimentKeyCompletionSimilarMaxMatchTokenSize = "completion.similar.max.match.tokens.size"

	// ExperimentKeyCompletionDependenciesMaxSize 补全依赖的最大长度
	ExperimentKeyCompletionDependenciesMaxSize = "completion.dependencies.max.size"

	// ExperimentKeyCompletionRemoteRagLang 补全的远程rag能力支持的语言，默认为 "javascript,typescript,vue,java,c-sharp,go,python,c/c++"，待模型支持全部语言后可删除该feature
	ExperimentKeyCompletionRemoteRagLang = "completion.rag.remote.lang"

	// ExperimentKeyCompletionRemoteRagSyncMode 手动触发补全时，使用同步调用RAG召回，待模型支持全部语言后可删除该feature
	ExperimentKeyCompletionRemoteRagSyncModeEnable = "completion.rag.remote.syncmode.enable"

	// ExperimentKeyCompletionRemoteRagSyncMode 手动触发补全时，使用同步调用RAG召回，待模型支持全部语言后可删除该feature
	ExperimentKeyCompletionRemoteRagSyncModeTimeout = "completion.rag.remote.syncmode.timeout"

	// ExperimentKeyCompletionRemoteRagRefreshCacheEnable 补全的远程rag是否开启缓存刷新
	ExperimentKeyCompletionRemoteRagRefreshCacheEnable = "completion.rag.remote.refreshcache.enable"

	// ExperimentKeyDisableCompletionFilter 是否关闭安全过滤中补全场景的过滤
	ExperimentKeyDisableCompletionFilter = "content.filter.disable.completion.filter"

	// ExperimentKeyDisableChatFilter 是否关闭安全过滤中问答场景的过滤
	ExperimentKeyDisableChatFilter = "content.filter.disable.chat.filter"

	// ExperimentKeyEnableClipCompletionRefPrompt 是否开启补全引用prompt裁剪
	ExperimentKeyEnableClipCompletionRefPrompt = "completion.clip.ref.prompt.enable"

	// ExperimentKeyClipCompletionRefThresholdRadio 补全引用prompt裁剪的剩余长度比例阈值，[0~1], 这个值越大越不容易裁剪，值越小月容易裁剪，0等同于1
	ExperimentKeyClipCompletionRefThresholdRadio = "completion.clip.ref.threshold.radio"

	// ExperimentKeyDiffApplyMaxOriginalCodeTokenLimit diff apply 最大源文件输入token限制
	ExperimentKeyDiffApplyMaxOriginalCodeTokenLimit = "diff.apply.max.original.code.token.limit"

	// ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit diff apply chunk 模式下最大源文件输入token限制
	ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit = "diff.apply.bychunk.max.original.code.token.limit"

	// ExperimentKeyDiffApplyByChunkMinOriginalCodeTokenLimit diff apply chunk 模式下最小源文件输入token限制
	ExperimentKeyDiffApplyByChunkMinOriginalCodeTokenLimit = "diff.apply.bychunk.min.original.code.token.limit"

	// ExperimentKeyDiffApplyByChunkMaxModificationChunkLimit diff apply chunk 模式下最多解决方案 chunk 数量限制
	ExperimentKeyDiffApplyByChunkMaxModificationChunkLimit = "diff.apply.bychunk.max.modification.chunk.limit"

	// ExperimentKeyDiffApplyByChunkRate diff apply chunk 模式下分流比例
	ExperimentKeyDiffApplyByChunkRate = "diff.apply.bychunk.rate"

	// ExperimentKeyDiffApplyRetryForModificationRate diff apply 模式下解决方案重试比例，即解决方案 / 原文的长度比例小于多少时重试
	ExperimentKeyDiffApplyRetryForModificationRate = "diff.apply.retry.for.modification.rate"

	// ExperimentKeyDiffApplyFixForModificationRate1 diff apply 模式下解决方案修复比例1
	ExperimentKeyDiffApplyFixForModificationRate1 = "diff.apply.fix.for.modification.rate1"

	// ExperimentKeyDiffApplyFixForModificationRate2 diff apply 模式下解决方案修复比例2
	ExperimentKeyDiffApplyFixForModificationRate2 = "diff.apply.fix.for.modification.rate2"

	// ExperimentKeyDiffApplyForBigFileRate diff apply 模式下触发大文件拆分比例
	ExperimentKeyDiffApplyForBigFileRate = "diff.apply.fix.for.big.file.rate"

	// ExperimentKeyDiffApplyByReApplyRate 触发 ReApply 的比例
	ExperimentKeyDiffApplyByReApplyRate = "diff.apply.by.reapply.rate"

	// ExperimentKeyDiffApplyAutoLintRate 触发 AutoLint 的比例
	ExperimentKeyDiffApplyAutoLintRate = "diff.apply.auto.lint.rate"

	// ExperimentKeyDiffApplyRetryMinLimit diff apply 模式下解决方案重试限制，原文行数最小要超过 minLimit 时才重试
	ExperimentKeyDiffApplyRetryMinLimit = "diff.apply.retry.min.limit"

	// ExperimentKeyChatSseTimeoutSeconds 问答sse超时时间，单位：秒
	ExperimentKeyChatSseTimeoutSeconds = "chat.sse.timeout"

	// ExperimentKeyContextRecommendFileMaxRecommendCount 上下文推荐文件数量
	ExperimentKeyContextRecommendFileMaxRecommendCount = "context.recommend.file.max_recommend_count"
	// ExperimentKeyContextRecommendFileMaxRecommendRound 上下文推荐文件轮数
	ExperimentKeyContextRecommendFileMaxRecommendRound = "context.recommend.file.max_recommend_round"
	// ExperimentKeyContextRecommendFileEnable 上下文推荐文件是否开启
	ExperimentKeyContextRecommendFileEnable = "context.recommend.file.enable"
	// ExperimentKeyMemoryRefineQueryEnable 是否开启长记忆补全的query refine能力
	ExperimentKeyMemoryRefineQueryEnable = "memory.refine.query.v2.enable"
	// ExperimentKeyMemoryRerankStrategy 记忆检索的rerank策略
	ExperimentKeyMemoryRerankStrategy = "memory.rerank.strategy"
	// ExperimentKeyRerankApiThreshold API检索阈值
	ExperimentKeyRerankApiThreshold = "memory.rerank.api.threshold"
	// ExperimentKeyRerankRuleThreshold 规则检索阈值
	ExperimentKeyRerankRuleThreshold = "memory.rerank.rule.threshold"
	// ExperimentKeyRerankFusionTimeout 融合检索超时时间
	ExperimentKeyRerankFusionTimeout = "memory.rerank.fusion.timeout"
	// ExperimentKeyMemoryExtractMessageEnable 是否开启长记忆补全的message提取能力
	ExperimentKeyMemoryExtractMessageEnable = "memory.extract.message.enable"
	// ExperimentKeyMemoryVectorRetrieveThreshold 向量检索阈值
	ExperimentKeyMemoryVectorRetrieveThreshold = "memory.vector.retrieve.threshold"
	// ExperimentKeyMemoryMaxForgotCount 最大忘记次数
	ExperimentKeyMemoryMaxForgotCount = "memory.max.forget.count"
	// ExperimentKeyMemoryForgotThreshold 忘记阈值
	ExperimentKeyMemoryForgotThreshold = "memory.forget.threshold"
	// ExperimentMemoryFetchEnable 是否开启记忆获取能力，如果关闭，将不会使用记忆
	ExperimentMemoryFetchEnable = "memory.fetch.enable"
	// ExperimentMemoryAutoExtractEnable 是否开启记忆自动提取能力
	ExperimentMemoryAutoExtractEnable = "memory.auto.extract.v2.enable"
	// ExperimentMemoryInitExtractEnable 是否开启记忆初始化提取能力
	ExperimentMemoryInitExtractEnable = "memory.init.extract.v2.enable"
	// ExperimentMemoryExcludeCategory 记忆排除分类
	ExperimentMemoryExcludeCategory = "memory.exclude.category"
	// ExperimentMemoryRetentionScoreParam 记忆保留分数权重
	ExperimentMemoryRetentionScoreParam = "memory.retention.score.param"
	// ExperimentKeyAgentToolCallLimit Agent使用工具次数限制
	ExperimentKeyAgentToolCallLimit = "agent.tool.call.limit"
	// ExperimentMemoryEvalEnable 是否开启记忆评估能力
	ExperimentMemoryEvalEnable = "memory.eval.enable"
	// ExperimentMemoryQualityScoreThreshold 记忆质量分数阈值
	ExperimentMemoryQualityScoreThreshold = "memory.quality.score.threshold"
	// ExperimentMemoryChatEditEnable 开启Chat和Edit模式的记忆获取
	ExperimentMemoryChatEditEnable = "memory.chatedit.v2.enable"

	// ExperimentTruncateKeyFreeInput 自由问答场景总体上下文大小限制，公有云默认50k，专有云默认5800
	ExperimentTruncateKeyFreeInput = "truncate.free.input"
	// ExperimentTruncateKeyAiDevelop AiDevelop场景总体上下文大小限制，公有云默认60k，专有云默认5800
	ExperimentTruncateKeyAiDevelop = "truncate.ai.develop"
	// ExperimentTruncateKeyMultimodal 多模态场景上下文大小限制，公有云默认25k，专有云5800
	ExperimentTruncateKeyMultimodal = "truncate.multimodal"

	// ExperimentCodebaseGraphKeyGlobalSwitch 代码图谱全局开关
	ExperimentCodebaseGraphKeyGlobalSwitch = "codebase.graph.global.switch"
	// ExperimentCodebaseGraphKeySupportParseNodeLanguageList 代码图谱解析Node支持语言
	ExperimentCodebaseGraphKeySupportParseNodeLanguageList = "codebase.graph.support.parse.node.language.list"
	// ExperimentCodebaseGraphKeySupportParseRelationLanguageList 代码图谱解析Node支持语言
	ExperimentCodebaseGraphKeySupportParseRelationLanguageList = "codebase.graph.support.parse.relation.language.list"
	// ExperimentCodebaseGraphKeyManagerScanInterval 代码图谱manager扫描间隔，单位毫秒
	ExperimentCodebaseGraphKeyManagerScanInterval = "codebase.graph.manager.scan.interval"
	// ExperimentCodebaseGraphKeyManagerCompensateRound 代码图谱manager使用mtree补偿的间隔轮次
	ExperimentCodebaseGraphKeyManagerCompensateRound = "codebase.graph.manager.compensate.round"
	// ExperimentCodebaseGraphKeyWorkerScanInterval 代码图谱worker扫描间隔，单位毫秒
	ExperimentCodebaseGraphKeyWorkerScanInterval = "codebase.graph.worker.scan.interval"
	// ExperimentCodebaseGraphKeyWorkerScanBatch 代码图谱worker扫描批次大小
	ExperimentCodebaseGraphKeyWorkerScanBatchSize = "codebase.graph.worker.scan.batch.size"
	// ExperimentCodebaseGraphKeyWorkerSingleFileTimeout 代码图谱worker执行单任务超时时间，单位毫秒
	ExperimentCodebaseGraphKeyWorkerSingleFileTimeout = "codebase.graph.worker.single.file.timeout"
	// ExperimentCodebaseGraphKeyWorkerIdleTime 代码图谱worker idle时间，单位毫秒
	ExperimentCodebaseGraphKeyWorkerIdleTime = "codebase.graph.worker.idle.time"
	// ExperimentCodebaseGraphKeyStageMinThreshold 代码图谱推进下一stage最小阈值
	ExperimentCodebaseGraphKeyStageMinThreshold = "codebase.graph.stage.min.threshold"
	// ExperimentCodebaseGraphKeyMaxFileCount 代码图谱最大文件数目
	ExperimentCodebaseGraphKeyMaxFileCount = "codebase.graph.max.file.count"
	// ExperimentCodebaseGraphKeySearchByIdeSwitch 代码图谱search by ide开关
	ExperimentCodebaseGraphKeySearchByIdeSwitch = "codebase.graph.search.by.ide.switch"
	// ExperimentCodebaseGraphKeyRemoteImportSwitch 代码图谱远程数据导入导出开关
	ExperimentCodebaseGraphKeyRemoteImportSwitch = "codebase.graph.remote.import.switch"
	// ExperimentWikiConcurrentTaskCount wiki并发任务数
	ExperimentWikiConcurrentTaskCount = "wiki.concurrent.task.count"
	// ExperimentWikiFallbackConcurrentTaskCount wiki补偿并发任务数
	ExperimentWikiFallbackConcurrentTaskCount = "wiki.fallback.concurrent.task.count"
	// ExperimentWikiIncrConcurrentTaskCount wiki增量并发任务数
	ExperimentWikiIncrConcurrentTaskCount = "wiki.increment.concurrent.task.count"
	// ExperimentWikiGlobalEnabled wiki全局开关
	ExperimentWikiGlobalEnabled = "wiki.global.enabled"
	// ExperimentWikiFetchEnabled wiki获取开关，关闭后禁用search_memory工具
	ExperimentWikiFetchEnabled = "wiki.fetch.enabled"
	// ExperimentWikiGuidedPromptEnabled wiki在提示词中的强引导开关，关闭后，智能体中不增加引导提示词
	ExperimentWikiGuidedPromptEnabled = "wiki.guided.prompt.enabled"
)

const (
	DefaultCodebaseGraphKeyGlobalSwitch                     = true
	DefaultCodebaseGraphKeySupportParseNodeLanguageList     = ".java,.go,.py,.kt,.cs,.js,.jsx,.ts,.tsx,.vue,.cpp,.cc,.c++,.h,.hpp"
	DefaultCodebaseGraphKeySupportParseRelationLanguageList = ".java,.go"
	DefaultCodebaseGraphKeyManagerScanInterval              = 10000
	DefaultCodebaseGraphKeyManagerCompensateRound           = 30
	DefaultCodebaseGraphKeyWorkerScanInterval               = 100
	DefaultCodebaseGraphKeyWorkerSingleFileTimeout          = 5000
	DefaultCodebaseGraphKeyWorkerScanBatchSize              = 20
	DefaultCodebaseGraphKeyWorkerIdleTime                   = 10000
	DefaultCodebaseGraphKeyStageMinThreshold                = 0.98
	DefaultCodebaseGraphKeyMaxFileCount                     = 15000
	DefaultCodebaseGraphKeySearchByIdeSwitch                = true
	DefaultCodebaseGraphKeyRemoteImportSwitch               = true
)

const (
	// WorkspaceRagEmbeddingRecallStrategyBoth both 对原始query和refined_query_only同时向量化和召回
	WorkspaceRagEmbeddingRecallStrategyBoth = "both"

	// WorkspaceRagEmbeddingRecallStrategyRefinedQueryOnly refined_query_only 仅对refined_query进行向量化和召回
	WorkspaceRagEmbeddingRecallStrategyRefinedQueryOnly = "refined_query_only"

	// WorkspaceRagRerankStrategyRrfMerge rrf_merge
	WorkspaceRagRerankStrategyRrfMerge = "rrf_merge"

	// WorkspaceRagRerankStrategyVectorMerge vector_merge
	WorkspaceRagRerankStrategyVectorMerge = "vector_merge"

	// WorkspaceRagRerankStrategyLLMRerankMerge llm_rerank_merge
	WorkspaceRagRerankStrategyLLMRerankMerge = "llm_rerank_merge"
)

const (
	// DefaultCompletionChunkMaxToken 默认补全RAG的token长度
	DefaultCompletionChunkMaxToken = 256
)

const (
	// ExperimentKeyEmbeddingTokenBucketLimit 上下文推荐文件是否开启
	// embedding服务，单机器，qps的限制
	ExperimentKeyEmbeddingTokenBucketLimit = "embedding.machine.qps.limit"

	// DefaultEmbeddingTokenBucketLimit 默认的embedding令牌桶的数量
	// embedding令牌桶用来控制单用户全局最大的embedding qps
	// 默认值不能设置低于1，服务端下发设置可以低于1
	DefaultEmbeddingTokenBucketLimit = 5

	// ExperimentKeyMaxClientStorageFileNum 最多支持的文件数量
	ExperimentKeyMaxClientStorageFileNum = "embedding.machine.storage.file.num.limit"

	// DefaultMaxAutoIndexFileNum 默认自动触发全量索引的最大文件数量
	DefaultMaxAutoIndexFileNum = 10000

	// DefaultClientMaxStorageFileNum 默认最多支持6000个文件
	DefaultClientMaxStorageFileNum = 6000

	// DefaultServerMaxStorageFileNum 服务端单库最大存储文件数量
	DefaultServerMaxStorageFileNum = 100000

	// DefaultServerSyncInterval 服务端索引同步间隔
	DefaultServerSyncInterval = 1 * time.Second

	// DefaultUploadFileInterval 上传文件间隔
	DefaultUploadFileInterval = 1 * time.Second

	// DefaultMaxMTreeFileSize 建树时，最大1MB的文件，大于1MB文件过滤
	DefaultMaxMTreeFileSize = 1 * 1024 * 1024

	// DefaultMinMTreeFileSize 建树时，最小10个字节的文件才建树
	DefaultMinMTreeFileSize = 10

	// DefaultPerRequestMaxUploadFileSize 单次上传最大上传合计9MB的文件
	// 防止溢出，因此调的比10MB稍微小一些
	DefaultPerRequestMaxUploadFileSize = 9 * 1024 * 1024

	// DefaultPerRequestMaxUploadFileNum 最大单次上传文件数量
	DefaultPerRequestMaxUploadFileNum = 1000

	// DefaultPerRequestMaxUpdateNodeNum 最大单次更新节点的数量
	// 这个影响数据库写入
	DefaultPerRequestMaxUpdateNodeNum = 100

	// DefaultPerRequestMaxCheckFileStatusNum
	// 每次检查文件状态请求的文件数量
	// 同时复用为每次更新节点的一批的数量，一次更新100个服务端节点
	DefaultPerRequestMaxCheckFileStatusNum = 1000

	// DefaultPerRequestMaxQueryNodeNum
	// 每次向服务端请求的节点数量
	DefaultPerRequestMaxQueryNodeNum = 100

	// DefaultClientAsyncVectorIndexFileCountThreshold 区分大仓库小仓库文件数量的阈值
	// 当大于这个文件数量时，认为是大仓库，使用客户端异步索引
	// 当小于这个文件数量时，认为是小仓库，使用客户端同步索引
	DefaultClientAsyncVectorIndexFileCountThreshold = 60

	// DefaultClientAsyncPerRequestMaxFetchEmbedFileNum 单次http请求取回Embedding数据的文件数量
	DefaultClientAsyncPerRequestMaxFetchEmbedFileNum = 5

	// DefaultClientAsyncVectorIndexGrayUserLastNum
	// 默认客户端向量索引灰度用户Id最后的数字，小于这个参数的参与灰度
	// TODO 这里目前是全部开启
	DefaultClientAsyncVectorIndexGrayUserLastNum = 10
)

var (
	// DefaultCodebaseCompletionRagEnable 默认是否开启整个代码库范围的补全rag能力
	DefaultCodebaseCompletionRagEnable = true

	// DefaultnRemoteRagLang 知识库RAG默认支持的语言
	DefaultRemoteRagLang = "javascript,typescript,vue,java,c-sharp,go,python,c/c++"

	// DefaultRemoteRagSyncModeEnable 默认开启知识库RAG的同步模式
	DefaultRemoteRagSyncModeEnable = true

	// DefaultRemoteRagSyncModeTimeout 知识库RAG的同步模式的超时时间,单位 ms
	DefaultRemoteRagSyncModeTimeout = 300

	DefaultRemoteRagRefreshCacheEnable = true
)
