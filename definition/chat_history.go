package definition

type ChatRecord struct {
	RequestId         string `json:"requestId" db:"request_id"`
	SessionId         string `json:"sessionId" db:"session_id"`
	ChatTask          string `json:"chatTask" db:"chat_task"`
	ChatContext       string `json:"chatContext" db:"chat_context"`
	SystemRoleContent string `json:"systemRoleContent" db:"system_role_content"` //系统提示词
	Question          string `json:"question" db:"question"`
	Answer            string `json:"answer" db:"answer"`
	LikeStatus        int    `json:"likeStatus" db:"like_status"`
	GmtCreate         int64  `json:"gmtCreate" db:"gmt_create"`
	GmtModified       int64  `json:"gmtModified" db:"gmt_modified"`
	FinishStatus      int64  `json:"finishStatus" db:"finish_status"` // 0 正常/ 1 异常
	FilterStatus      string `json:"filterStatus" db:"filter_status"`
	ErrorResult       string `json:"errorResult" db:"error_result"`
	CodeLanguage      string `json:"codeLanguage" db:"code_language"`
	Extra             string `json:"extra" db:"extra"`
	SessionType       string `json:"sessionType" db:"session_type"`
	Summary           string `json:"summary" db:"summary"`
	IntentionType     string `json:"intentionType" db:"intention_type"`
	ReasoningContent  string `json:"reasoningContent" db:"reasoning_content"`
	Mode              string `json:"mode" db:"mode"`              //common agent新版，选择模式
	ChatPrompt        string `json:"chatPrompt" db:"chat_prompt"` //用户提示词
}

type ChatSession struct {
	OrgID        string `json:"orgId" db:"org_id"`
	UserID       string `json:"userId" db:"user_id"`
	UserName     string `json:"userName" db:"user_name"`
	SessionId    string `json:"sessionId" db:"session_id"`
	SessionTitle string `json:"sessionTitle" db:"session_title"`
	//project uri hash
	ProjectId   string       `json:"projectId" db:"project_id"`
	ProjectURI  string       `json:"projectUri" db:"project_uri"`
	ProjectName string       `json:"projectName" db:"project_name"`
	GmtCreate   int64        `json:"gmtCreate" db:"gmt_create"`
	GmtModified int64        `json:"gmtModified" db:"gmt_modified"`
	ChatRecords []ChatRecord `json:"chatRecords" db:"-"`
	SessionType string       `json:"sessionType" db:"session_type"`
	Mode        string       `json:"mode" db:"mode"`       //common agent新版，选择模式
	Version     string       `json:"version" db:"version"` //版本，用于兼容处理
}

// ChatMessage 保存和llm交互时的一个message对象信息
type ChatMessage struct {
	Id         string `json:"id" db:"id"`
	RequestId  string `json:"requestId" db:"request_id"`
	SessionId  string `json:"sessionId" db:"session_id"`
	Role       string `json:"role" db:"role"`
	Content    string `json:"content" db:"content"`
	Summary    string `json:"summary" db:"summary"`
	ToolResult string `json:"toolResult" db:"tool_result"`
	TokenInfo  string `json:"tokenInfo" db:"token_info"`
	GmtCreate  int64  `json:"gmtCreate" db:"gmt_create"`
}
