package service

import (
	"cosy/deepwiki/storage"
	"cosy/definition"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"
)

// GenerateCatalogueResponse Step2的响应 - 结构化目录
type GenerateCatalogueResponse struct {
	Structure definition.DocumentationStructure `json:"structure"`
	RawJSON   string                            `json:"raw_json"` // 原始JSON字符串，用于调试
}

// GenerateCatalogueService 文档目录生成服务
type GenerateCatalogueService struct {
}

// NewGenerateCatalogueService 创建文档目录生成服务
func NewGenerateCatalogueService() *GenerateCatalogueService {
	return &GenerateCatalogueService{}
}

// ExtractThinkContent 从响应中提取<think>标签内的内容
func (s *GenerateCatalogueService) ExtractThinkContent(response string) (string, error) {
	// 查找<think>标签
	thinkRegex := regexp.MustCompile(`<analysis>([\s\S]*?)</analysis>`)
	matches := thinkRegex.FindStringSubmatch(response)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}

	return "", fmt.Errorf("could not find <analysis> tag in response")
}

// ParseDocumentationStructure 解析LLM响应中的文档结构JSON
func (s *GenerateCatalogueService) ParseDocumentationStructure(response string) (*definition.DocumentationStructure, string, error) {
	// 查找<documentation_structure>标签
	structureRegex := regexp.MustCompile(`<documentation_structure>([\s\S]*?)</documentation_structure>`)
	matches := structureRegex.FindStringSubmatch(response)

	var jsonContent string
	if len(matches) > 1 {
		extractedContent := strings.TrimSpace(matches[1])

		// 尝试从提取的内容中找到JSON对象
		jsonContent = s.extractPureJSON(extractedContent)
		if jsonContent == "" {
			return nil, extractedContent, fmt.Errorf("no valid JSON found in documentation_structure tag")
		}
	} else {
		// 尝试查找JSON代码块
		jsonBlockRegex := regexp.MustCompile("```json\\s*\\n([\\s\\S]*?)\\n```")
		matches = jsonBlockRegex.FindStringSubmatch(response)
		if len(matches) > 1 {
			jsonContent = strings.TrimSpace(matches[1])
		} else {
			return nil, "", fmt.Errorf("could not find documentation structure in response")
		}
	}

	// 解析JSON - 先尝试解析为标准格式
	var structure definition.DocumentationStructure
	if err := json.Unmarshal([]byte(jsonContent), &structure); err == nil {
		// 标准格式解析成功
		return &structure, jsonContent, nil
	}

	// 标准格式解析失败，尝试解析为数组格式
	var itemsArray []*definition.DocumentationSection
	if err := json.Unmarshal([]byte(jsonContent), &itemsArray); err != nil {
		return nil, jsonContent, fmt.Errorf("failed to parse JSON structure: %w", err)
	}

	// 将数组格式转换为标准格式
	structure = definition.DocumentationStructure{
		Items: itemsArray,
	}

	return &structure, jsonContent, nil
}

// extractPureJSON 从可能包含额外文本的内容中提取纯JSON
func (s *GenerateCatalogueService) extractPureJSON(content string) string {
	// 查找第一个 { 或 [ 的位置
	firstBrace := strings.Index(content, "{")
	firstBracket := strings.Index(content, "[")

	var start int = -1
	var isArray bool = false

	// 确定JSON的起始位置和类型
	if firstBrace == -1 && firstBracket == -1 {
		return ""
	} else if firstBrace == -1 {
		start = firstBracket
		isArray = true
	} else if firstBracket == -1 {
		start = firstBrace
		isArray = false
	} else if firstBracket < firstBrace {
		start = firstBracket
		isArray = true
	} else {
		start = firstBrace
		isArray = false
	}

	// 根据JSON类型查找匹配的结束符
	var end int = -1
	if isArray {
		// 处理数组格式 []
		bracketCount := 0
		for i := start; i < len(content); i++ {
			if content[i] == '[' {
				bracketCount++
			} else if content[i] == ']' {
				bracketCount--
				if bracketCount == 0 {
					end = i
					break
				}
			}
		}
	} else {
		// 处理对象格式 {}
		braceCount := 0
		for i := start; i < len(content); i++ {
			if content[i] == '{' {
				braceCount++
			} else if content[i] == '}' {
				braceCount--
				if braceCount == 0 {
					end = i
					break
				}
			}
		}
	}

	if end == -1 {
		return ""
	}

	// 提取JSON内容
	jsonCandidate := content[start : end+1]

	// 验证是否为有效JSON
	var testObj interface{}
	if err := json.Unmarshal([]byte(jsonCandidate), &testObj); err != nil {
		// 如果JSON无效，尝试清理一下再验证
		cleaned := strings.TrimSpace(jsonCandidate)
		if err2 := json.Unmarshal([]byte(cleaned), &testObj); err2 != nil {
			return ""
		}
		return cleaned
	}

	return jsonCandidate
}

// FlattenSections 将层次结构展平为列表
func (s *GenerateCatalogueService) FlattenSections(structure definition.DocumentationStructure) []*definition.DocumentationSection {
	var result []*definition.DocumentationSection

	var flatten func(sections []*definition.DocumentationSection, parentPath string)
	flatten = func(sections []*definition.DocumentationSection, parentPath string) {
		for _, section := range sections {
			// 设置完整的title路径
			fullTitle := section.Title
			if parentPath != "" {
				fullTitle = parentPath + "." + section.Title
			}

			sectionCopy := section
			sectionCopy.Title = fullTitle
			// 清空children，因为我们要展平
			sectionCopy.Children = nil

			result = append(result, sectionCopy)

			// 递归处理子节点
			if len(section.Children) > 0 {
				flatten(section.Children, fullTitle)
			}
		}
	}

	flatten(structure.Items, "")
	return result
}

func (g *GenerateCatalogueService) GetAllDocumentCatalog(repoId string, repoName string) ([]definition.DocumentCatalog, error) {
	items, err := storage.GlobalStorageService.GetCatalogsByRepoID(repoId)
	if err != nil {
		return nil, err
	}
	finalDocumentCatalogs := storage.ConvertToDocumentCatalogs(items, repoName)
	return finalDocumentCatalogs, nil
}

// GenerateDocumentCatalogs 生成完整的需要生成内容的目录列表
func (s *GenerateCatalogueService) GenerateDocumentCatalogs(sections []*definition.DocumentationSection, warehouseId, documentId string) []definition.DocumentCatalog {
	var documents []definition.DocumentCatalog

	// 递归处理目录层次结构
	var processCatalogueItems func(items []*definition.DocumentationSection, parentId *string, parentPath string, level int)
	processCatalogueItems = func(items []*definition.DocumentationSection, parentId *string, parentPath string, level int) {
		order := 0 // 创建排序计数器

		for _, item := range items {
			// 清理标题，去除空格
			cleanTitle := strings.ReplaceAll(item.Title, " ", "")

			// 生成Unique ID
			itemId := fmt.Sprintf("%s-%s", generateUniqueId(), cleanTitle)

			// 构建完整路径
			fullPath := cleanTitle
			if parentPath != "" {
				fullPath = parentPath + "." + cleanTitle
			}

			// 创建文档目录项
			documentItem := definition.DocumentCatalog{
				Id:            itemId,
				WarehouseId:   warehouseId,
				DocumentId:    documentId,
				Description:   cleanTitle,
				Name:          item.Name,
				Url:           cleanTitle,
				DependentFile: append([]string{}, item.DependentFile...),
				ParentId:      parentId,
				Prompt:        item.Prompt,
				Order:         order,
				Level:         level,
				FullPath:      fullPath,
			}

			documents = append(documents, documentItem)
			order++ // 递增排序计数器

			// 递归处理子项
			if len(item.Children) > 0 {
				processCatalogueItems(item.Children, &itemId, fullPath, level+1)
			}
		}
	}

	// 从根级别开始处理
	processCatalogueItems(sections, nil, "", 0)
	return documents
}

// generateUniqueId 生成Unique ID
func generateUniqueId() string {
	// 使用时间戳和随机数生成Unique ID
	return fmt.Sprintf("doc_%d", time.Now().UnixNano())
}

// ValidateStructure 验证文档结构的完整性
func (s *GenerateCatalogueService) ValidateStructure(structure definition.DocumentationStructure) error {
	if len(structure.Items) == 0 {
		return fmt.Errorf("documentation structure is empty")
	}

	var validate func(sections []*definition.DocumentationSection, path string) error
	validate = func(sections []*definition.DocumentationSection, path string) error {
		for i, section := range sections {
			if section.Title == "" {
				return fmt.Errorf("section at path %s[%d] has empty title", path, i)
			}
			if section.Name == "" {
				return fmt.Errorf("section at path %s[%d] has empty name", path, i)
			}
			if section.Prompt == "" {
				return fmt.Errorf("section at path %s[%d] has empty prompt", path, i)
			}

			// 递归验证子节点
			if len(section.Children) > 0 {
				childPath := fmt.Sprintf("%s[%d].children", path, i)
				if err := validate(section.Children, childPath); err != nil {
					return err
				}
			}
		}
		return nil
	}

	return validate(structure.Items, "items")
}
