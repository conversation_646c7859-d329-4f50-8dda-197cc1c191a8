package service

import (
	"context"
	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	common3 "cosy/deepwiki/common"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/indexing/api"
	"cosy/knowledge"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"regexp"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

const (
	wikiMaxRetryAttempts  = 3
	wikiRetryDelay        = 3 * time.Second
	wikiDefaultConcurrent = 1 // 默认并发任务数
)

type GenerateWikiService struct {
	StorageService *storage.LingmaWikiStorageService
}

func NewGenerateWikiService(storageService *storage.LingmaWikiStorageService) *GenerateWikiService {
	return &GenerateWikiService{
		StorageService: storageService,
	}
}

// SaveDocumentFileItem 保存文档文件项到存储
func (g *GenerateWikiService) SaveDocumentFileItem(fileItem *definition.AgentWikiItem) error {
	err := g.StorageService.CreateWikiItem(fileItem)
	if err != nil {
		return fmt.Errorf("failed to save wiki item: %w", err)
	}

	return nil
}

// RepairMermaidSyntax 修复 Mermaid 图表中的语法问题
func RepairMermaidSyntax(content *string) {
	// 匹配所有 ```mermaid 代码块
	mermaidRegex := regexp.MustCompile("(?s)```mermaid\\s*(.*?)```")
	matches := mermaidRegex.FindAllStringSubmatch(*content, -1)

	for _, match := range matches {
		if len(match) < 2 {
			continue
		}
		mermaidCode := strings.TrimSpace(match[1])

		// 修复后的代码
		cleanedCode := repairMermaidCodeBlock(mermaidCode)

		// 替换原始内容中的旧代码块
		oldBlock := match[0]
		newBlock := fmt.Sprintf("```mermaid\n%s\n```", cleanedCode)

		*content = strings.Replace(*content, oldBlock, newBlock, 1)
	}
}

// repairMermaidCodeBlock 修复单个mermaid代码块的语法问题
func repairMermaidCodeBlock(mermaidCode string) string {
	lines := strings.Split(mermaidCode, "\n")
	var cleanedLines []string

	// 检测图表类型
	diagramType := detectDiagramType(lines)

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 根据图表类型修复每一行的语法
		cleanedLine := repairMermaidLine(line, diagramType)
		if cleanedLine != "" {
			cleanedLines = append(cleanedLines, cleanedLine)
		}
	}

	return strings.Join(cleanedLines, "\n")
}

// detectDiagramType 检测图表类型
func detectDiagramType(lines []string) string {
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "classDiagram") {
			return "classDiagram"
		} else if strings.HasPrefix(line, "sequenceDiagram") {
			return "sequenceDiagram"
		} else if strings.HasPrefix(line, "flowchart") || strings.HasPrefix(line, "graph") {
			return "flowchart"
		} else if strings.HasPrefix(line, "erDiagram") {
			return "erDiagram"
		} else if strings.HasPrefix(line, "stateDiagram") {
			return "stateDiagram"
		}
	}
	return "unknown"
}

// repairMermaidLine 修复单行mermaid语法
func repairMermaidLine(line string, diagramType string) string {
	// 移除行首尾的空白字符
	line = strings.TrimSpace(line)

	// 如果是空行，返回空
	if line == "" {
		return ""
	}

	// 移除多余的引号和括号（只在不合法的位置）
	// 1. 移除中文括号
	line = strings.ReplaceAll(line, "（", "")
	line = strings.ReplaceAll(line, "）", "")

	// 根据图表类型进行特定修复
	switch diagramType {
	case "classDiagram":
		line = repairClassDiagramLine(line)
	case "sequenceDiagram":
		line = repairSequenceDiagramLine(line)
	case "flowchart":
		line = repairFlowchartLine(line)
	case "stateDiagram":
		line = repairStateDiagramLine(line)
	default:
		line = repairGenericLine(line)
	}

	// 通用修复
	line = repairGenericLine(line)

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	return line
}

// repairClassDiagramLine 修复类图语法
func repairClassDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 修复立体声标记中的多余空格
	stereotypeRegex := regexp.MustCompile(`<<\s*([^>]+?)\s*>>`)
	line = stereotypeRegex.ReplaceAllString(line, "<<$1>>")

	// 3. 标准化冒号周围的空格
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	// 4. 修复泛型语法：list~Type~ -> Type[]
	genericRegex := regexp.MustCompile(`\b(list|List|array|Array)~([^~]+)~`)
	line = genericRegex.ReplaceAllString(line, "$2[]")

	// 5. 修复Note语法：Note for -> note for
	noteRegex := regexp.MustCompile(`\bNote\s+for\b`)
	line = noteRegex.ReplaceAllString(line, "note for")

	return line
}

// repairSequenceDiagramLine 修复序列图语法
func repairSequenceDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 只修复明显错误的三重箭头 ">>>" -> ">>"
	line = regexp.MustCompile(`>>>`).ReplaceAllString(line, ">>")

	// 3. 标准化冒号周围的空格
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	return line
}

// repairFlowchartLine 修复流程图语法
func repairFlowchartLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 1. 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 2. 只修复明显错误的箭头格式
	// 标准化箭头周围的空格
	line = regexp.MustCompile(`\s*-->\s*`).ReplaceAllString(line, " --> ")

	return line
}

// repairStateDiagramLine 修复状态图语法
func repairStateDiagramLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 标准化箭头和冒号周围的空格
	line = regexp.MustCompile(`\s*-->\s*`).ReplaceAllString(line, " --> ")
	line = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(line, " : ")

	return line
}

// repairGenericLine 通用语法修复
func repairGenericLine(line string) string {
	// 只进行最基本的修复，避免破坏有效语法

	// 清理多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	return line
}

// GenerateWikiContentWithAgent 使用agent graph的方式生成wiki内容（并发处理）
func (g *GenerateWikiService) GenerateWikiContentWithAgent(ctx context.Context, inputs map[string]any, catalogueResult definition.CatalogueResult, request definition.CreateDeepwikiRequest) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: WikiGenerate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 修复类型转换问题：需要先检查类型
	var documentCatalogs []definition.DocumentCatalog
	switch v := catalogueResult.DocumentCatalogs.(type) {
	case []definition.DocumentCatalog:
		documentCatalogs = v
	case []interface{}:
		// 如果是[]interface{}类型，需要转换每个元素
		for _, item := range v {
			if catalog, ok := item.(definition.DocumentCatalog); ok {
				documentCatalogs = append(documentCatalogs, catalog)
			} else {
				log.Warnf("invalid document catalog item type: %T", item)
			}
		}
	default:
		log.Errorf("Deepwiki: WikiGenerate failed - Repo: %s, Workspace: %s, Error: invalid document catalogs type", repoName, request.WorkspacePath)
		return fmt.Errorf("invalid document catalogs type: %T, expected []DocumentCatalog or []interface{}", v)
	}

	if len(documentCatalogs) == 0 {
		log.Warnf("no documents to generate for repository: %s", catalogueResult.RepositoryName)
		log.Infof("Deepwiki: WikiGenerate end - Repo: %s, Workspace: %s, Documents: 0", repoName, request.WorkspacePath)
		return nil
	}

	log.Infof("Starting concurrent wiki content generation for repository: %s with %d documents",
		catalogueResult.RepositoryName, len(documentCatalogs))

	// 并发处理配置
	maxConcurrentTasks := wikiSupport.GetConcurrentTaskCount(definition.ExperimentWikiConcurrentTaskCount, 5)
	semaphore := make(chan struct{}, maxConcurrentTasks)

	var wg sync.WaitGroup
	var errorsMu sync.Mutex // 专门保护errors slice的锁
	var errors []error
	var completedCount int64 // 使用原子操作的计数器

	// 为每个文档并发调用wiki生成agent
	for _, document := range documentCatalogs {
		wg.Add(1)
		go func(doc definition.DocumentCatalog) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 添加延迟避免过于频繁的请求
			time.Sleep(100 * time.Millisecond)

			log.Debugf("Processing document concurrently: %s for repository: %s", doc.Name, doc.WarehouseId)

			// 为每个 goroutine 创建带有独立 AgentContext 的 context
			ctxForDoc := ctx // 使用原始context作为基础
			// 只深拷贝 AgentContext 以确保并发安全
			if agentContext := ctx.Value(chainsCommon.KeyDeepwikiAgentContext); agentContext != nil {
				if ac, ok := agentContext.(*common3.AgentContext); ok {
					// 深拷贝 AgentContext 并替换到新的 context 中
					ctxForDoc = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, common3.AgentContextDeepCopy(ac))
				}
			}
			// 其他 context key 保持使用原始 ctx，避免复制不全的问题

			// 准备agent输入参数，复制原有inputs
			agentInputs := make(map[string]any)
			for k, v := range inputs {
				agentInputs[k] = v
			}

			err := g.ProcessDocumentWithAgent(ctxForDoc, agentInputs, doc, catalogueResult, request, &completedCount)
			if err != nil {
				errorsMu.Lock()
				errors = append(errors, fmt.Errorf("failed to process document %s: %w", doc.Name, err))
				errorsMu.Unlock()
				log.Errorf("Failed to process document %s: %v", doc.Name, err)
				return
			}

			log.Debugf("Successfully completed processing document: %s", doc.Name)
		}(document)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 处理错误
	if len(errors) > 0 {
		log.Errorf("Wiki content generation completed with %d errors out of %d documents",
			len(errors), len(documentCatalogs))

		// 记录所有错误但不阻止流程继续
		for _, err := range errors {
			log.Errorf("Document generation error: %v", err)
		}

		// 如果失败的文档过多（超过一半），返回错误
		if len(errors) > len(documentCatalogs)/2 {
			log.Debugf("Deepwiki: WikiGenerate failed - Repo: %s, Workspace: %s, Error: too many documents failed", repoName, request.WorkspacePath)
			return fmt.Errorf("too many documents failed to generate (%d/%d), aborting",
				len(errors), len(documentCatalogs))
		}
	}

	log.Infof("Wiki content generation completed successfully for repository: %s. Processed: %d, Failed: %d",
		catalogueResult.RepositoryName, len(documentCatalogs)-len(errors), len(errors))

	// 在所有wiki生成完成后，尝试补偿失败的catalog
	if len(errors) > 0 {
		log.Infof("Deepwiki: Compensation start - Repo: %s, Workspace: %s, Failed: %d", repoName, request.WorkspacePath, len(errors))

		// 首先需要获取repo信息
		repo, err := g.StorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
		if err != nil {
			log.Infof("Deepwiki: Compensation failed - Repo: %s, Workspace: %s, Error: failed to get repo", repoName, request.WorkspacePath)
			log.Errorf("Failed to get repo for compensation: %v", err)
		} else if repo != nil {
			// 尝试补偿失败的catalog
			compensationErr := g.CheckAndCompensateFailedCatalogs(ctx, repo.ID, inputs, request)
			if compensationErr != nil {
				log.Infof("Deepwiki: Compensation failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, compensationErr)
				log.Errorf("Compensation process failed for repository %s: %v", catalogueResult.RepositoryName, compensationErr)
				// 不阻止主流程，继续执行
			} else {
				log.Infof("Deepwiki: Compensation end - Repo: %s, Workspace: %s. Compensation process completed for repository: %s", repoName, request.WorkspacePath, catalogueResult.RepositoryName)
			}
		} else {
			log.Infof("Deepwiki: Compensation failed - Repo: %s, Workspace: %s, Error: repo not found", repoName, request.WorkspacePath)
		}
	}

	log.Infof("Deepwiki: WikiGenerate end - Repo: %s, Workspace: %s, Completed: %d, Failed: %d", repoName, request.WorkspacePath, len(documentCatalogs)-len(errors), len(errors))
	return nil
}

// ProcessDocumentWithAgent 使用agent处理单个文档
func (g *GenerateWikiService) ProcessDocumentWithAgent(ctx context.Context, inputs map[string]any, document definition.DocumentCatalog, catalogueResult definition.CatalogueResult, request definition.CreateDeepwikiRequest, completedCount *int64) error {
	// 获取总文档数量用于进度显示
	var totalDocs int
	if docCatalogs, ok := catalogueResult.DocumentCatalogs.([]definition.DocumentCatalog); ok {
		totalDocs = len(docCatalogs)
	} else if docCatalogs, ok := catalogueResult.DocumentCatalogs.([]interface{}); ok {
		totalDocs = len(docCatalogs)
	}

	// 获取repo信息
	var repoID string
	if repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo); ok {
		repoID = repoInfo.Name
	} else {
		repoID = catalogueResult.RepositoryName
	}

	// 获取当前进度
	currentProgress := atomic.LoadInt64(completedCount)

	log.Debugf("Deepwiki: WikiItem start - ID: %s, Repo: %s, Progress: %d/%d", document.Id, repoID, currentProgress, totalDocs)

	// 检查catalog的当前状态，如果已经completed则跳过
	catalog, err := g.StorageService.GetCatalogByID(document.Id)
	if err != nil {
		log.Debugf("Failed to get catalog status: %v", err)
		return fmt.Errorf("failed to get catalog status for %s: %w", document.Id, err)
	}

	if catalog != nil && catalog.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
		log.Debugf("Deepwiki: WikiItem already completed - ID: %s, Repo: %s, Progress: %d/%d, skipping generation",
			document.Id, repoID, currentProgress, totalDocs)

		// 更新完成计数
		atomic.AddInt64(completedCount, 1)
		newProgress := atomic.LoadInt64(completedCount)
		log.Debugf("Deepwiki: WikiItem skipped - ID: %s, Repo: %s, Progress: %d/%d", document.Id, repoID, newProgress, totalDocs)
		return nil
	}

	// 标记catalog和item为processing状态
	err = g.StorageService.MarkCatalogAndItemAsProcessing(document.Id)
	if err != nil {
		log.Errorf("Failed to mark catalog as processing: %v", err)
		// 不阻断流程，只记录错误
	}

	// 创建一个defer函数来确保在任何情况下都能正确更新状态
	var processSuccess bool
	defer func() {
		if processSuccess {
			if err := g.StorageService.MarkCatalogAndItemAsCompleted(document.Id); err != nil {
				log.Errorf("Failed to mark catalog as completed: %v", err)
			} else {
				log.Debugf("Successfully marked catalog %s as completed", document.Id)
			}
		} else {
			if err := g.StorageService.MarkCatalogAndItemAsFailed(document.Id); err != nil {
				log.Errorf("Failed to mark catalog as failed: %v", err)
			} else {
				log.Warnf("Marked catalog %s as failed", document.Id)
			}
		}
	}()

	// 准备agent输入参数，复制原有inputs
	agentInputs := make(map[string]any)
	for k, v := range inputs {
		agentInputs[k] = v
	}

	// 添加当前文档相关的输入
	uniqueId := document.Id
	agentInputs[chainsCommon.KeyCurrentCatalogue] = document

	// 获取必要的输入参数
	requestId := uuid.NewString()

	wikiGenerateGraphId := "GenerateWiki-" + document.DocumentId + "-" + document.Name

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= wikiMaxRetryAttempts; attempt++ {

		log.Debugf("[deepwiki-wiki-generate] Agent generate wiki. wikiGenerateGraphId: %s", wikiGenerateGraphId)

		// 每次重试都创建新的agent实例
		wikiGenerateAgent, err := support.MakeAgent(requestId, common3.WikiGenerateAgentBuilderIdentifier)
		if err != nil {
			processSuccess = false
			log.Debugf("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d, Error: failed to create agent", document.Id, repoID, currentProgress, totalDocs)
			return fmt.Errorf("failed to create wiki generate agent: %w", err)
		}

		wikiSupport.InitCurrentGraphStat(agentInputs, wikiGenerateGraphId)

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in wiki generate agent.RunSync (attempt %d/%d): %v\n%s", attempt, wikiMaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-wiki-generate] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = wikiGenerateAgent.RunSync(ctx, agentInputs)
		}()

		if runErr == nil {
			// Agent执行成功，但需要验证是否生成了有效内容
			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common3.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到agentInputs中
					for key, value := range tempInputs {
						agentInputs[key] = value
					}

					log.Debugf("[deepwiki-wiki-generate] Synced %d keys from agent state to agent inputs for document: %s", len(tempInputs), document.Name)
					messages := agentState.ShortTermMemory.Messages()
					messageJson, err2 := json.Marshal(messages)
					if err2 == nil {
						if err := wikiSupport.SaveWikiGenerateResponseToFile(string(messageJson), "generate_wiki_"+document.Description); err != nil {
							log.Debugf("Failed to save catalogue structure AI response to file: %v", err)
						}
					}
				}
			}

			// 检查是否生成了有效内容（不为空且长度大于等于250字符）
			var hasValidContent bool
			var contentLength int
			var contentPreview string
			const minContentLength = 250

			if content, exists := agentInputs[chainsCommon.KeyWikiContent]; exists {
				if contentStr, ok := content.(string); ok {
					trimmedContent := strings.TrimSpace(contentStr)
					contentLength = len(trimmedContent)

					if trimmedContent != "" && contentLength >= minContentLength {
						hasValidContent = true
						log.Debugf("[deepwiki-wiki-generate] Agent generated valid content for document: %s, length: %d", document.Name, contentLength)
					} else {
						log.Debugf("[deepwiki-wiki-generate] Agent generated insufficient content for document: %s, length: %d (min required: %d)", document.Name, contentLength, minContentLength)
					}
				}
			}

			if hasValidContent {
				if attempt > 1 {
					log.Infof("[deepwiki-wiki-generate] Agent succeeded on attempt %d/%d for document: %s, content length: %d", attempt, wikiMaxRetryAttempts, document.Name, contentLength)
				}
				// 成功时记录统计信息
				wikiSupport.AppendGraphStatToWikiStat(agentInputs)
				//成功时清除lastErr
				lastErr = nil
				break
			} else {
				// Agent执行成功但没有生成有效内容（为空或长度不足），视为失败需要重试
				if contentLength == 0 {
					runErr = fmt.Errorf("agent executed successfully but generated empty content")
					log.Debugf("[deepwiki-wiki-generate] Agent generated empty content for document: %s (attempt %d/%d)", document.Name, attempt, wikiMaxRetryAttempts)
				} else {
					runErr = fmt.Errorf("agent executed successfully but generated insufficient content (length: %d, min required: %d)", contentLength, minContentLength)
					log.Debugf("[deepwiki-wiki-generate] Agent generated insufficient content for document: %s, length: %d (min required: %d) (attempt %d/%d), preview: %s", document.Name, contentLength, minContentLength, attempt, wikiMaxRetryAttempts, contentPreview)
				}
			}
		}

		// 失败时记录统计信息 - 修复：从agentInputs而不是inputs中获取
		currentGraphStat, ok := agentInputs[chainsCommon.KeyWikiGenerateStatCurrentGraph].(*definition.WikiGenerateGraphStat)
		wikiStat, ok2 := agentInputs[chainsCommon.KeyWikiGenerateStat].(*definition.DeepWikiAgentStats)
		if ok && ok2 {
			go func(graphId string, stat *definition.WikiGenerateGraphStat) {
				// 计算当前图的token汇总统计
				stat.CalcTotalTokens()

				wikiStat.UpdateLock.Lock()
				defer wikiStat.UpdateLock.Unlock()

				wikiStat.GraphStats[graphId] = stat

			}(wikiGenerateGraphId, currentGraphStat)
		}

		lastErr = runErr
		log.Debugf("[deepwiki-wiki-generate] Agent failed on attempt %d/%d for document %s: %v", attempt, wikiMaxRetryAttempts, document.Name, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < wikiMaxRetryAttempts {
			log.Debugf("[deepwiki-wiki-generate] Retrying in %v... (attempt %d/%d) for document: %s", wikiRetryDelay, attempt+1, wikiMaxRetryAttempts, document.Name)

			// 检查context是否已被取消
			select {
			case <-ctx.Done():
				processSuccess = false
				log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d, Error: context cancelled", document.Id, repoID, currentProgress, totalDocs)
				return fmt.Errorf("[deepwiki-wiki-generate] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(wikiRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Debugf("[deepwiki-wiki-generate] Agent failed after %d attempts for document %s, last error: %v", wikiMaxRetryAttempts, document.Name, lastErr)
		processSuccess = false
		log.Debugf("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d", document.Id, repoID, currentProgress, totalDocs)
		return fmt.Errorf("wiki generate agent run sync error: %w", lastErr)
	}

	// 获取生成的内容（在重试循环中已经验证过内容存在）
	var generatedContent string
	if content, exists := agentInputs[chainsCommon.KeyWikiContent]; exists {
		if contentStr, ok := content.(string); ok {
			generatedContent = contentStr
			log.Debugf("Retrieved content from agent state inputs for documentId: %s, document: %s", uniqueId, document.Name)
		}
	}

	// 如果这里仍然没有内容，说明有问题（不应该走到这里）
	if strings.TrimSpace(generatedContent) == "" {
		log.Debugf("No wiki content found after successful retry loop for documentId: %s, document: %s. Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d", uniqueId, document.Name, document.Id, repoID, currentProgress, totalDocs)
		processSuccess = false
		return fmt.Errorf("wiki content generation failed: no content available after successful execution for document %s", document.Name)
	}

	log.Debugf("Agent generated content for document %s, length: %d", document.Name, len(generatedContent))

	// 提取文档内容
	extractedContent, err := g.extractDocumentContent(generatedContent)
	if err != nil {
		log.Debugf("failed to extract document content, using full response: %v", err)
		extractedContent = generatedContent
	}
	extractedContent = strings.TrimPrefix(extractedContent, "<docs>")
	extractedContent = strings.TrimSuffix(extractedContent, "</docs>")

	// 修复Mermaid语法错误
	RepairMermaidSyntax(&extractedContent)

	// 获取repo信息以获取正确的repo ID
	repo, err := g.StorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		processSuccess = false
		log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d, Error: failed to get repo", document.Id, repoID, currentProgress, totalDocs)
		return fmt.Errorf("failed to get repo for wiki item creation: %w", err)
	}
	if repo == nil {
		processSuccess = false
		log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d, Error: repo not found", document.Id, repoID, currentProgress, totalDocs)
		return fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}

	// 创建文档文件项
	fileItem := &definition.AgentWikiItem{
		ID:             uuid.NewString(),
		CatalogID:      document.Id,
		Content:        extractedContent,
		Title:          document.Name,
		Description:    document.Description,
		Extend:         "{}",                                       // 空的JSON对象，类似C#中的Extra字典
		ProgressStatus: definition.DeepWikiProgressStatusCompleted, // 标记为完成状态
		RepoID:         repo.ID,
		WorkspacePath:  request.WorkspacePath,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	// 保存文档到存储
	err = g.SaveDocumentFileItem(fileItem)
	if err != nil {
		processSuccess = false
		log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d, Error: failed to save to db", document.Id, repoID, currentProgress, totalDocs)
		return fmt.Errorf("failed to save document file item: %w", err)
	}

	// 添加内存索引
	err = g.addWikiItemIndex(ctx, fileItem.ID, request.WorkspacePath)
	if err != nil {
		log.Errorf("Failed to add memory index for new wiki item: %v", err)
	}

	// 处理即时关系（源文件、代码片段）- 全量生成中每个item生成后立即处理
	log.Debugf("[Full Generation] Processing immediate relations for new wiki item: %s", fileItem.ID)
	knowledge.OnWikiItemCreatedImmediate(ctx, g.StorageService.GetDB(), request.WorkspacePath, fileItem)
	log.Debugf("[Full Generation] Successfully processed immediate relations for new wiki item: %s", fileItem.ID)

	log.Debugf("Successfully processed and saved document: %s (ID: %s, Content length: %d)",
		document.Name, fileItem.ID, len(extractedContent))

	processSuccess = true

	// 更新完成计数
	atomic.AddInt64(completedCount, 1)
	newProgress := atomic.LoadInt64(completedCount)

	log.Infof("Deepwiki: WikiItem end - ID: %s, Repo: %s, Progress: %d/%d", document.Id, repoID, newProgress, totalDocs)
	return nil
}

// extractDocumentContent 从响应中提取文档内容
func (g *GenerateWikiService) extractDocumentContent(response string) (string, error) {
	// 查找<docs>标签中的内容
	docsRegex := regexp.MustCompile(`<docs>([\s\S]*?)</docs>`)
	matches := docsRegex.FindStringSubmatch(response)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}

	// 如果没有找到docs标签，尝试查找其他可能的标签
	contentRegex := regexp.MustCompile(`<content>([\s\S]*?)</content>`)
	matches = contentRegex.FindStringSubmatch(response)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}

	return "", fmt.Errorf("could not find document content in response")
}

// ProcessFailedCatalogsWithCompensation 查找并重新生成失败的catalog，在设置repo为completed前调用
func (g *GenerateWikiService) ProcessFailedCatalogsWithCompensation(ctx context.Context, repoID string, inputs map[string]any, request definition.CreateDeepwikiRequest) error {
	log.Infof("Deepwiki: Compensation start")
	log.Debugf("Starting compensation process for failed catalogs in repo: %s", repoID)

	// 查找失败的catalog
	failedCatalogs, err := g.StorageService.GetCatalogsByRepoIDAndStatus(repoID, definition.DeepWikiProgressStatusFailed)
	if err != nil {
		log.Infof("Deepwiki: Compensation failed")
		return fmt.Errorf("failed to get failed catalogs: %w", err)
	}

	if len(failedCatalogs) == 0 {
		log.Debugf("No failed catalogs found in repo: %s, compensation not needed", repoID)
		log.Infof("Deepwiki: Compensation end")
		return nil
	}

	log.Infof("Found %d failed catalogs in repo %s, starting compensation generation", len(failedCatalogs), repoID)

	// 获取repo信息以正确设置WarehouseId
	repo, err := g.StorageService.GetWikiRepoByID(repoID)
	if err != nil {
		log.Errorf("Deepwiki: Compensation failed")
		return fmt.Errorf("failed to get repo info for compensation: %w", err)
	}
	if repo == nil {
		log.Errorf("Deepwiki: Compensation failed")
		return fmt.Errorf("repo not found for compensation: %s", repoID)
	}

	// 将 LingmaWikiCatalog 转换为 DocumentCatalog
	var failedDocuments []definition.DocumentCatalog
	for _, catalog := range failedCatalogs {
		documentCatalog := definition.DocumentCatalog{
			Id:            catalog.ID,
			WarehouseId:   repo.Name, // 使用正确的repo name而不是空字符串
			DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
			Description:   catalog.Description,
			Name:          catalog.Name,
			Url:           catalog.Description,
			DependentFile: strings.Split(catalog.DependentFiles, ","),
			ParentId: func() *string {
				if catalog.ParentID == "" {
					return nil
				} else {
					return &catalog.ParentID
				}
			}(),
			Prompt:   catalog.Prompt,
			Order:    catalog.Order,
			Level:    0, // 这个值在处理时不重要
			FullPath: catalog.Name,
		}
		failedDocuments = append(failedDocuments, documentCatalog)
	}

	// 使用与正常wiki生成相同的并发处理逻辑
	// 并发处理配置
	maxConcurrentTasks := wikiSupport.GetConcurrentTaskCount(definition.ExperimentWikiFallbackConcurrentTaskCount, 3)
	semaphore := make(chan struct{}, maxConcurrentTasks)

	var wg sync.WaitGroup
	var errorsMu sync.Mutex // 保护errors slice
	var errors []error
	var successCount int64               // 改为int64以支持原子操作
	var compensationCompletedCount int64 // 补偿流程的完成计数器

	// 为每个失败的文档并发调用wiki生成agent
	for _, document := range failedDocuments {
		wg.Add(1)
		go func(doc definition.DocumentCatalog) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 添加延迟避免过于频繁的请求
			time.Sleep(200 * time.Millisecond)

			// 获取当前补偿进度
			currentCompensationProgress := atomic.LoadInt64(&compensationCompletedCount)

			log.Infof("Deepwiki: WikiItem start - ID: %s, Repo: %s, Progress: %d/%d (compensation)", doc.Id, repoID, currentCompensationProgress, len(failedDocuments))
			log.Infof("Compensation processing for failed catalog: %s (ID: %s)", doc.Name, doc.Id)

			// 创建一个空的catalogueResult用于传递给ProcessDocumentWithAgent
			catalogueResult := definition.CatalogueResult{
				RepositoryName:        request.WorkspacePath,
				TotalSections:         len(failedDocuments),
				TotalDocumentCatalogs: len(failedDocuments),
				DocumentCatalogs:      failedDocuments,
			}

			err := g.ProcessDocumentWithAgent(ctx, inputs, doc, catalogueResult, request, &compensationCompletedCount)
			if err != nil {
				errorsMu.Lock()
				errors = append(errors, fmt.Errorf("failed to compensate catalog %s: %w", doc.Name, err))
				errorsMu.Unlock()
				log.Errorf("Compensation failed for catalog %s: %v", doc.Name, err)
				log.Infof("Deepwiki: WikiItem failed - ID: %s, Repo: %s, Progress: %d/%d (compensation)", doc.Id, repoID, currentCompensationProgress, len(failedDocuments))
				return
			}

			atomic.AddInt64(&successCount, 1)
			finalProgress := atomic.LoadInt64(&compensationCompletedCount)
			log.Infof("Successfully compensated catalog: %s", doc.Name)
			log.Infof("Deepwiki: WikiItem end - ID: %s, Repo: %s, Progress: %d/%d (compensation)", doc.Id, repoID, finalProgress, len(failedDocuments))
		}(document)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 处理结果
	finalSuccessCount := atomic.LoadInt64(&successCount)
	if len(errors) > 0 {
		log.Errorf("Compensation completed with %d errors out of %d failed catalogs", len(errors), len(failedDocuments))

		// 记录所有错误但不阻止流程继续
		for _, err := range errors {
			log.Errorf("Compensation error: %v", err)
		}

		// 如果补偿成功率太低（低于50%），返回错误
		if finalSuccessCount < int64(len(failedDocuments)/2) {
			log.Errorf("Deepwiki: Compensation failed")
			return fmt.Errorf("compensation failed for too many catalogs (%d/%d succeeded), aborting",
				finalSuccessCount, len(failedDocuments))
		}
	}

	log.Debugf("Compensation process completed for repo %s. Attempted: %d, Succeeded: %d, Failed: %d",
		repoID, len(failedDocuments), finalSuccessCount, len(errors))

	log.Infof("Deepwiki: Compensation end")
	return nil
}

// CheckAndCompensateFailedCatalogs 检查repo状态，如果有失败的catalog则进行补偿生成
func (g *GenerateWikiService) CheckAndCompensateFailedCatalogs(ctx context.Context, repoID string, inputs map[string]any, request definition.CreateDeepwikiRequest) error {
	// 首先获取repo的状态信息
	statusCount, err := g.StorageService.GetCatalogStatusByRepoID(repoID)
	if err != nil {
		return fmt.Errorf("failed to get catalog status: %w", err)
	}

	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	if failedCount == 0 {
		log.Debugf("No failed catalogs found in repo %s, no compensation needed", repoID)
		return nil
	}

	log.Infof("Found %d failed catalogs in repo %s, starting compensation process", failedCount, repoID)

	// 调用补偿生成函数
	err = g.ProcessFailedCatalogsWithCompensation(ctx, repoID, inputs, request)
	if err != nil {
		return fmt.Errorf("compensation process failed: %w", err)
	}

	// 补偿完成后，重新检查并更新repo状态
	// CheckAndUpdateRepoStatus现在会自动将任何有failed catalog的repo设置为failed状态
	if err := g.StorageService.CheckAndUpdateRepoStatus(repoID); err != nil {
		log.Errorf("Failed to update repo status after compensation: %v", err)
		// 不返回错误，因为补偿本身可能是成功的
	} else {
		log.Debugf("Repo status updated after compensation for repo: %s", repoID)
	}

	log.Infof("Compensation process and repo status update completed for repo: %s", repoID)
	return nil
}

// addWikiItemIndex 添加wiki item的索引
func (g *GenerateWikiService) addWikiItemIndex(ctx context.Context, itemID, workspacePath string) error {
	// 获取repo信息
	repo, err := g.StorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	// 添加内存索引
	go api.MemoryIndexUpdate(ctx, repo.ID, workspacePath, []string{itemID}, []string{})
	log.Debugf("Added memory index for wiki item: %s", itemID)

	return nil
}
