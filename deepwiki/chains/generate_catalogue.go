package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	common2 "cosy/chat/chains/common"
	common3 "cosy/deepwiki/common"
	service2 "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

// 新增常量
const (
	catalogueMaxRetryAttempts = 5
	catalogueRetryDelay       = 5 * time.Second
	maxCatalogueDepth         = 5 // 最大目录深度
)

// 使用队列进行广度优先搜索
type queueItem struct {
	node   *definition.DocumentationSection
	nodeId string
	layer  int
	order  int
}

type GenerateCatalogueChain struct {
	catalogueService *service2.GenerateCatalogueService
}

func NewGenerateCatalogueChain(catalogueService *service2.GenerateCatalogueService) *GenerateCatalogueChain {
	return &GenerateCatalogueChain{
		catalogueService: catalogueService,
	}
}

func (g *GenerateCatalogueChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	// 预检查阶段
	var waitingList []*definition.AgentWikiCatalog
	if shouldSkip, result, waiting, err := g.preCheck(inputs); shouldSkip {
		return result, err
	} else {
		waitingList = waiting
	}

	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	log.Infof("Deepwiki: CatalogueGenerate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 生成目录结构
	documentCatalogs, err := g.generateCatalogueWithQueue(ctx, inputs, waitingList)
	if err != nil {
		return nil, err
	}

	// 构建最终结果
	return g.buildFinalResult(ctx, inputs, documentCatalogs)
}

// preCheck 预检查阶段，检查是否需要跳过生成
func (g *GenerateCatalogueChain) preCheck(inputs map[string]any) (bool, map[string]any, []*definition.AgentWikiCatalog, error) {
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: CatalogueGenerate failed - Error: missing CreateDeepwikiRequest in inputs")
		return true, inputs, nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 优先检查数据库中是否已存在catalogs
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		existingCatalogs, err := storage.GlobalStorageService.GetCatalogsByRepoID(repo.ID)
		if err == nil && len(existingCatalogs) > 0 {
			waitingCatalogs := g.filterWaitingCatalogue(existingCatalogs)
			if len(waitingCatalogs) > 0 {
				log.Debugf("Deepwiki: Found %d waiting catalogs in database, skipping generation - Repo: %s, Workspace: %s",
					len(waitingCatalogs), repoName, request.WorkspacePath)
				return false, inputs, waitingCatalogs, nil
			}
			log.Debugf("Deepwiki: Found %d existing catalogs in database, reconstructing CatalogueResult and skipping generation - Repo: %s, Workspace: %s",
				len(existingCatalogs), repoName, request.WorkspacePath)

			// 从数据库中的catalogs重构CatalogueResult
			catalogueResult := g.reconstructCatalogueResultFromDatabase(existingCatalogs, repo)
			inputs[common2.KeyCatalogueResult] = catalogueResult

			log.Infof("Deepwiki: CatalogueGenerate skipped - Repo: %s, Workspace: %s, Reconstructed: %d catalogs",
				repoName, request.WorkspacePath, len(existingCatalogs))
			return true, inputs, nil, nil
		}
	}

	// 检查inputs中是否已有CatalogueResult（恢复场景的二级检查）
	if existingResult, exists := inputs[common2.KeyCatalogueResult]; exists && existingResult != nil {
		if catalogueResult, ok := existingResult.(definition.CatalogueResult); ok && catalogueResult.TotalSections > 0 {
			log.Infof("Deepwiki: CatalogueResult already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return true, inputs, nil, nil // 直接跳过生成
		}
	}

	return false, nil, nil, nil
}

// filterWaitingCatalogue 获取待处理的目录结构
func (g *GenerateCatalogueChain) filterWaitingCatalogue(catalogues []*definition.AgentWikiCatalog) []*definition.AgentWikiCatalog {
	result := make([]*definition.AgentWikiCatalog, 0)
	for _, catalogue := range catalogues {
		if catalogue.ProgressStatus == definition.DeepWikiProgressStatusWaiting {
			result = append(result, catalogue)
		}
	}
	return result
}

func (g *GenerateCatalogueChain) initRootQueue(ctx context.Context, inputs map[string]any, repo *definition.AgentWikiRepo, waitingList []*definition.AgentWikiCatalog) ([]*queueItem, error) {
	// 首先生成根节点（第0层）
	queue := make([]*queueItem, 0)
	if len(waitingList) > 0 {
		for _, catalogue := range waitingList {
			section, err := g.catalogToSection(catalogue)
			if err != nil {
				log.Debugf("Failed to convert catalogue %s to section: %v", catalogue.ID, err)
				continue
			}
			queue = append(queue, &queueItem{
				node:   section,
				nodeId: catalogue.ID,
				layer:  catalogue.LayerLevel,
				order:  catalogue.Order,
			})
		}
		log.Debugf("[deepwiki-catalogue-generate] Found %d waiting catalogues, skipping generation of root nodes", len(waitingList))
		return queue, nil
	}
	rootStructure, err := g.generateRootNodes(ctx, inputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate root nodes: %w", err)
	}
	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)

	// 将根节点加入队列和结果集
	for order, rootNode := range rootStructure.Items {
		// 保存根节点到数据库
		dbID, err := g.saveSingleNodeToDatabase(ctx, rootNode, repo.ID, request.WorkspacePath, 0, "", order)
		if err != nil {
			log.Errorf("Failed to save root node %s to database: %v", rootNode.Name, err)
		}

		// 如果根节点需要生成子节点，加入队列
		if rootNode.IsHasChildren() {
			queue = append(queue, &queueItem{
				node:   rootNode,
				nodeId: dbID,
				layer:  0,     // 下一层
				order:  order, // 子节点从0开始排序
			})
		}
	}
	log.Debugf("[deepwiki-catalogue-generate] Generated and saved layer 0: %d root nodes", len(rootStructure.Items))
	return queue, nil
}

func (g *GenerateCatalogueChain) catalogToSection(catalogue *definition.AgentWikiCatalog) (*definition.DocumentationSection, error) {
	var section definition.DocumentationSection
	if catalogue.RawData == "" {
		return nil, fmt.Errorf("catalogue raw data is empty")
	}
	err := json.Unmarshal([]byte(catalogue.RawData), &section)
	return &section, err
}

// generateCatalogueWithQueue 使用广度优先搜索方式生成目录结构
func (g *GenerateCatalogueChain) generateCatalogueWithQueue(ctx context.Context, inputs map[string]any, waitingList []*definition.AgentWikiCatalog) ([]definition.DocumentCatalog, error) {
	totalCount := 0

	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	//repoInfo, ok := inputs[common2.KeyRepoInfo].(definition.RepositoryInfo)
	//if !ok {
	//	return nil, fmt.Errorf("invalid repo info")
	//}

	// 获取或创建repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get wiki repo: %w", err)
	}
	if repo == nil {
		return nil, fmt.Errorf("wiki repo not found for workspace path: %s", request.WorkspacePath)
	}

	queue, err := g.initRootQueue(ctx, inputs, repo, waitingList)
	if err != nil {
		return nil, fmt.Errorf("failed to init root queue: %w", err)
	}
	startSections := make([]*definition.DocumentationSection, 0)
	for _, node := range queue {
		startSections = append(startSections, node.node)
	}

	// 广度优先处理队列中的节点
	for len(queue) > 0 {
		// 出队一个节点
		currentItem := queue[0]
		queue = queue[1:]
		if currentItem.layer > maxCatalogueDepth {
			continue
		}

		currentNode := currentItem.node
		currentLayer := currentItem.layer

		log.Debugf("[deepwiki-catalogue-generate] Processing node: %s at layer %d, remaining: %d", currentNode.Title, currentLayer, len(queue))

		// 为当前节点生成子节点
		childStructure, err := g.generateChildNodes(ctx, inputs, currentNode, currentLayer)
		if err != nil {
			log.Errorf("Failed to generate child nodes for %s: %v", currentNode.Name, err)
			// 更新当前节点状态为pending（不再需要生成子节点）
			if currentItem.nodeId != "" {
				g.updateNodeStatus(currentItem.nodeId, definition.DeepWikiProgressStatusPending)
			}
			continue
		}

		// 处理生成的子节点
		for childOrder, childNode := range childStructure.Items {
			currentNode.Children = append(currentNode.Children, childNode)
			totalCount++
			childLayerLevel := currentLayer + 1
			// 保存子节点到数据库
			childDBID, err := g.saveSingleNodeToDatabase(ctx, childNode, repo.ID, request.WorkspacePath, childLayerLevel, currentItem.nodeId, childOrder)
			if err != nil {
				log.Errorf("Failed to save child node %s to database: %v", childNode.Name, err)
				continue
			}

			// 如果子节点还需要生成更深层的子节点，且未达到最大深度，加入队列尾部
			if childNode.IsHasChildren() && currentLayer < maxCatalogueDepth {
				queue = append(queue, &queueItem{
					node:   childNode,
					nodeId: childDBID,
					layer:  childLayerLevel,
					order:  childOrder, // 子节点从0开始排序
				})
			}
		}

		// 更新当前节点状态为pending（子节点已生成完成）
		if currentItem.nodeId != "" {
			g.updateNodeStatus(currentItem.nodeId, definition.DeepWikiProgressStatusPending)
		}

		log.Debugf("[deepwiki-catalogue-generate] Generated %d child nodes for %s at layer %d", len(childStructure.Items), currentNode.Title, currentLayer)
	}

	// 处理剩余队列中未处理的节点（达到最大深度限制）
	for _, remainingItem := range queue {
		if remainingItem.nodeId != "" {
			g.updateNodeStatus(remainingItem.nodeId, definition.DeepWikiProgressStatusPending)
			log.Debugf("[deepwiki-catalogue-generate] Node %s reached max depth, status set to pending", remainingItem.node.Name)
		}
	}

	// 保存完整结构到文件（用于调试）
	sectionJson, err := json.MarshalIndent(startSections, "", "  ")
	if err == nil {
		if err := wikiSupport.SaveWikiGenerateResponseToFile(string(sectionJson), "catalogue_structure_merge"); err != nil {
			log.Debugf("Failed to save catalogue structure AI response to file: %v", err)
		}
	}

	// 生成最终的DocumentCatalog列表
	documentCatalogs, err := g.catalogueService.GetAllDocumentCatalog(repo.ID, repo.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get all document catalogs: %w", err)
	}

	log.Infof("Catalogue generation completed using BFS, total nodes: %d", totalCount)
	return documentCatalogs, nil
}

// generateRootNodes 生成根节点列表
func (g *GenerateCatalogueChain) generateRootNodes(ctx context.Context, inputs map[string]any) (*definition.DocumentationStructure, error) {
	// 设置特殊输入标记，告诉agent生成根节点
	tempInputs := make(map[string]any)
	for k, v := range inputs {
		tempInputs[k] = v
	}

	err := g.runAgentWithRetry(ctx, tempInputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate root nodes: %w", err)
	}

	return g.parseAgentOutput(tempInputs)
}

// generateChildNodes 为指定父节点生成子节点
func (g *GenerateCatalogueChain) generateChildNodes(ctx context.Context, inputs map[string]any, parentNode *definition.DocumentationSection, layerLevel int) (*definition.DocumentationStructure, error) {
	// 设置特殊输入标记，告诉agent生成指定父节点的子节点
	tempInputs := make(map[string]any)
	for k, v := range inputs {
		tempInputs[k] = v
	}
	tempInputs[common2.KeyWikiParentCatalogNode] = parentNode
	tempInputs[common2.KeyWikiCatalogLayerLevel] = layerLevel

	err := g.runAgentWithRetry(ctx, tempInputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate child nodes for parent %s: %w", parentNode.Name, err)
	}

	return g.parseAgentOutput(tempInputs)
}

// runAgentWithRetry 带重试机制运行agent
func (g *GenerateCatalogueChain) runAgentWithRetry(ctx context.Context, inputs map[string]any) error {
	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	var lastErr error
	for attempt := 1; attempt <= catalogueMaxRetryAttempts; attempt++ {
		log.Debugf("[deepwiki-catalogue-generate] Starting agent execution attempt %d/%d - Repo: %s", attempt, catalogueMaxRetryAttempts, repoName)

		agentCtx, _, err := agent.InitAgentContext(ctx)
		if err != nil {
			log.Debugf("[deepwiki-catalogue-generate] Failed to init agent context (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		requestId := uuid.NewString()
		catalogueGenerateAgent, err := support.MakeAgent(requestId, common3.CatalogueGenerateAgentBuilderIdentifier)
		if err != nil {
			log.Debugf("[deepwiki-catalogue-generate] Failed to create agent (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCatalogue")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in agent.RunSync (attempt %d/%d): %v\n%s", attempt, catalogueMaxRetryAttempts, r, stack)
					log.Debugf("[deepwiki-catalogue-generate] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = catalogueGenerateAgent.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			// Agent执行成功，进行状态同步
			if agentContext, ok := agentCtx.Value(common2.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common3.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Debugf("[deepwiki-catalogue-generate] Synced %d keys from agent state to inputs", len(tempInputs))
					messages := agentState.ShortTermMemory.Messages()
					messageJson, err2 := json.Marshal(messages)
					if err2 == nil {
						if err := wikiSupport.SaveWikiGenerateResponseToFile(string(messageJson), "generate_wiki_catalog"); err != nil {
							log.Debugf("Failed to save catalogue plan message AI response to file: %v", err)
						}
					}
				}
			}

			// 验证是否生成了有效的catalogue raw output
			var hasValidContent bool
			var contentLength int
			const minCatalogueContentLength = 300 // 目录结构最少需要100字符

			if rawOutput, exists := inputs[common2.KeyCatalogueRawOutput]; exists {
				if outputStr, ok := rawOutput.(string); ok {
					trimmedContent := strings.TrimSpace(outputStr)
					contentLength = len(trimmedContent)

					if trimmedContent != "" && contentLength >= minCatalogueContentLength {
						hasValidContent = true
						log.Debugf("[deepwiki-catalogue-generate] Agent generated valid catalogue content, length: %d", contentLength)
					} else {
						log.Debugf("[deepwiki-catalogue-generate] Agent generated insufficient catalogue content, length: %d (min required: %d)", contentLength, minCatalogueContentLength)
					}
				}
			}

			if hasValidContent {
				if attempt > 1 {
					log.Debugf("[deepwiki-catalogue-generate] Agent succeeded on attempt %d/%d, content length: %d", attempt, catalogueMaxRetryAttempts, contentLength)
				}
				// 成功时记录统计信息
				wikiSupport.AppendGraphStatToWikiStat(inputs)
				lastErr = nil
				break
			} else {
				// Agent执行成功但没有生成有效内容，视为失败需要重试
				if contentLength == 0 {
					runErr = fmt.Errorf("agent executed successfully but generated empty catalogue content")
					log.Debugf("[deepwiki-catalogue-generate] Agent generated empty catalogue content (attempt %d/%d)", attempt, catalogueMaxRetryAttempts)
				} else {
					runErr = fmt.Errorf("agent executed successfully but generated insufficient catalogue content (length: %d, min required: %d)", contentLength, minCatalogueContentLength)
					log.Debugf("[deepwiki-catalogue-generate] Agent generated insufficient catalogue content, length: %d (min required: %d) (attempt %d/%d)", contentLength, minCatalogueContentLength, attempt, catalogueMaxRetryAttempts)
				}
			}
		}

		// 失败时记录统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Debugf("[deepwiki-catalogue-generate] Agent failed on attempt %d/%d: %v", attempt, catalogueMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < catalogueMaxRetryAttempts {
			log.Infof("[deepwiki-catalogue-generate] Retrying in %v... (attempt %d/%d)", catalogueRetryDelay, attempt+1, catalogueMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			case <-ctx.Done():
				return fmt.Errorf("[deepwiki-catalogue-generate] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(catalogueRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Debugf("[deepwiki-catalogue-generate] Agent failed after %d attempts, last error: %v", catalogueMaxRetryAttempts, lastErr)
		return fmt.Errorf("catalogue agent run sync error: %w", lastErr)
	}

	return nil
}

// parseAgentOutput 解析agent输出并返回DocumentCatalog列表
func (g *GenerateCatalogueChain) parseAgentOutput(inputs map[string]any) (*definition.DocumentationStructure, error) {
	if _, ok := inputs[common2.KeyCatalogueRawOutput]; !ok {
		return nil, fmt.Errorf("catalogue structure generate failed: raw output not found")
	}

	rawOutput := inputs[common2.KeyCatalogueRawOutput].(string)
	log.Debugf("catalogue structure generate completed, content length: %d", len(rawOutput))

	structure, _, err := g.catalogueService.ParseDocumentationStructure(rawOutput)
	if err != nil {
		return nil, fmt.Errorf("failed to parse documentation structure: %w", err)
	}

	// 验证结构完整性
	if err := g.catalogueService.ValidateStructure(*structure); err != nil {
		return nil, fmt.Errorf("catalogue structure validation failed: %w", err)
	}

	return structure, nil
}

// buildFinalResult 构建最终结果
func (g *GenerateCatalogueChain) buildFinalResult(ctx context.Context, inputs map[string]any, documentCatalogs []definition.DocumentCatalog) (map[string]any, error) {
	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	repoInfo, ok := inputs[common2.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return nil, fmt.Errorf("invalid repo info")
	}

	// 构建目录结果结构体
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repoInfo.Name,
		RawJSON:               "", // 可以根据需要生成合并的JSON
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(documentCatalogs),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	inputs[common2.KeyCatalogueResult] = catalogueResult

	// 新增：保存结构化目录到repo的CurrentDocumentStructure字段
	var catalogueGenerationSuccess = false
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		documentCatalogData, err := json.Marshal(catalogueResult.DocumentCatalogs)
		if err != nil {
			log.Errorf("Failed to marshal documentation structure: %v", err)
		} else {
			repo.CurrentDocumentStructure = string(documentCatalogData)
			err = storage.GlobalStorageService.UpdateWikiRepo(repo)
			if err != nil {
				log.Errorf("Failed to update repo with current document structure: %v", err)
			} else {
				catalogueGenerationSuccess = true
			}
		}
	}

	// 目录生成完成并保存成功后更新检查点
	if catalogueGenerationSuccess {
		service2.UpdateCheckpointOnSuccess(request.WorkspacePath, service2.CheckpointCatalogueGenCompleted)
	}

	log.Infof("Deepwiki: CatalogueGenerate end - Repo: %s, Workspace: %s, Generated: %d catalogs", repoName, request.WorkspacePath, len(documentCatalogs))
	return inputs, nil
}

// saveSingleNodeToDatabase 保存单个节点到数据库
func (g *GenerateCatalogueChain) saveSingleNodeToDatabase(ctx context.Context, node *definition.DocumentationSection, repoID string, workspacePath string, layerLevel int, parentDBID string, order int) (string, error) {
	// 生成节点的原始JSON数据
	rawData, err := json.Marshal(node)
	if err != nil {
		return "", fmt.Errorf("failed to marshal node to JSON: %w", err)
	}

	// 确定初始状态
	var initialStatus string
	if node.IsHasChildren() && layerLevel < maxCatalogueDepth {
		initialStatus = definition.DeepWikiProgressStatusWaiting // 需要等待生成子节点
	} else {
		initialStatus = definition.DeepWikiProgressStatusPending // 不需要生成子节点或已达最大深度
	}

	// 生成唯一ID
	nodeID := uuid.NewString()

	// 处理依赖文件
	var dependentFiles []string
	if len(node.DependentFile) > 0 {
		dependentFiles = node.DependentFile
	}

	// 转换为数据库实体
	catalog := &definition.AgentWikiCatalog{
		ID:             nodeID,
		RepoID:         repoID,
		Name:           node.Name,
		Description:    node.Title, // 使用Title作为描述
		Prompt:         node.Prompt,
		ParentID:       parentDBID,
		Order:          order,
		ProgressStatus: initialStatus,
		DependentFiles: strings.Join(dependentFiles, ","),
		Keywords:       "", // 可以根据需要从node中提取关键词
		WorkspacePath:  workspacePath,
		RawData:        string(rawData),
		LayerLevel:     layerLevel,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	err = storage.GlobalStorageService.CreateCatalog(catalog)
	if err != nil {
		return "", fmt.Errorf("failed to save catalog %s: %w", node.Name, err)
	}

	log.Debugf("Successfully saved node to database: %s (ID: %s, Layer: %d, Status: %s)",
		node.Name, catalog.ID, layerLevel, initialStatus)
	return nodeID, nil
}

// updateNodeStatus 更新节点状态
func (g *GenerateCatalogueChain) updateNodeStatus(catalogID string, status string) {
	err := storage.GlobalStorageService.UpdateCatalogStatus(catalogID, status)
	if err != nil {
		log.Errorf("Failed to update catalog %s status to %s: %v", catalogID, status, err)
	} else {
		log.Debugf("Updated catalog %s status to %s", catalogID, status)
	}
}

// convertParentId 转换父级ID指针为字符串
func convertParentId(parentId *string) string {
	if parentId == nil {
		return ""
	}
	return *parentId
}

func (g GenerateCatalogueChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateCatalogueChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateCatalogueChain) GetOutputKeys() []string {
	return []string{}
}

// reconstructCatalogueResultFromDatabase 从数据库中的catalogs重构CatalogueResult
func (g *GenerateCatalogueChain) reconstructCatalogueResultFromDatabase(catalogs []*definition.AgentWikiCatalog, repo *definition.AgentWikiRepo) definition.CatalogueResult {
	// 转换数据库catalogs为DocumentCatalog结构
	var documentCatalogs []definition.DocumentCatalog
	for _, catalog := range catalogs {
		var parentId *string
		if catalog.ParentID != "" {
			parentId = &catalog.ParentID
		}

		var dependentFiles []string
		if catalog.DependentFiles != "" {
			dependentFiles = strings.Split(catalog.DependentFiles, ",")
		}

		docCatalog := definition.DocumentCatalog{
			Id:            catalog.ID,
			Name:          catalog.Name,
			Description:   catalog.Description,
			Prompt:        catalog.Prompt,
			ParentId:      parentId,
			Order:         catalog.Order,
			DependentFile: dependentFiles,
			WarehouseId:   repo.Name,
			DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
		}
		documentCatalogs = append(documentCatalogs, docCatalog)
	}

	// 构建CatalogueResult
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repo.Name,
		RawJSON:               repo.CurrentDocumentStructure, // 从repo中获取原始JSON
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(documentCatalogs),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	log.Debugf("Reconstructed CatalogueResult from database: %d catalogs for repo %s", len(documentCatalogs), repo.Name)
	return catalogueResult
}
