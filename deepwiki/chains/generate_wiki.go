package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	common2 "cosy/chat/chains/common"
	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"fmt"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

type GenerateWikiChain struct {
	wikiService *service.GenerateWikiService
}

func NewGenerateWikiChain() *GenerateWikiChain {
	// 创建默认的wiki service，避免循环依赖
	wikiService := service.NewGenerateWikiService(storage.GlobalStorageService)
	return &GenerateWikiChain{
		wikiService: wikiService,
	}
}

func (g *GenerateWikiChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	// 获取必要的输入参数
	catalogueResult, ok := inputs[common2.KeyCatalogueResult].(definition.CatalogueResult)
	if !ok {
		return nil, fmt.Errorf("missing or invalid catalogue result")
	}

	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return nil, fmt.Errorf("missing or invalid create deepwiki request")
	}

	log.Infof("deepwiki: Starting wiki content generation for repository: %s with %d documents",
		catalogueResult.RepositoryName, catalogueResult.TotalDocumentCatalogs)

	// ✅ 检查是否为恢复场景，只处理未完成的catalogs
	isRecovery, pendingCatalogs, err := g.checkRecoveryScenario(request.WorkspacePath, catalogueResult)
	if err != nil {
		log.Errorf("Failed to check recovery scenario: %v", err)
		// 继续正常处理，不阻断流程
		isRecovery = false
	}

	if isRecovery && len(pendingCatalogs) == 0 {
		log.Infof("deepwiki: All catalogs already completed for repository: %s, skipping wiki generation", catalogueResult.RepositoryName)
		// ✅ 所有catalog已完成，更新检查点
		service.UpdateCheckpointOnSuccess(request.WorkspacePath, service.CheckpointWikiGenCompleted)
		return inputs, nil
	}

	// 初始化agent上下文
	ctx, _, err = agent.InitWikiGenerateAgentContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize wiki agent context: %w", err)
	}

	if isRecovery {
		log.Infof("deepwiki: Recovery mode - processing %d pending catalogs out of %d total for repository: %s",
			len(pendingCatalogs), catalogueResult.TotalDocumentCatalogs, catalogueResult.RepositoryName)

		// 创建过滤后的catalogueResult，只包含未完成的catalogs
		filteredCatalogueResult := definition.CatalogueResult{
			RepositoryName:        catalogueResult.RepositoryName,
			CatalogueStructure:    catalogueResult.CatalogueStructure,
			FlattenedSections:     catalogueResult.FlattenedSections,
			DocumentCatalogs:      pendingCatalogs,
			TotalSections:         catalogueResult.TotalSections,
			TotalDocumentCatalogs: len(pendingCatalogs), // 更新为实际要处理的数量
		}

		// 使用现有的方法处理过滤后的catalogueResult
		err = g.wikiService.GenerateWikiContentWithAgent(ctx, inputs, filteredCatalogueResult, request)
	} else {
		log.Infof("deepwiki: Normal mode - processing all %d catalogs for repository: %s",
			catalogueResult.TotalDocumentCatalogs, catalogueResult.RepositoryName)

		// 调用service处理wiki内容生成，使用agent方式
		err = g.wikiService.GenerateWikiContentWithAgent(ctx, inputs, catalogueResult, request)
	}

	if err != nil {
		log.Errorf("[deepwiki-wiki-generate] generation error: %v", err)
		return nil, err
	}

	// Wiki生成成功后更新检查点
	service.UpdateCheckpointOnSuccess(request.WorkspacePath, service.CheckpointWikiGenCompleted)

	log.Infof("Successfully completed wiki content generation for repository: %s", catalogueResult.RepositoryName)
	return inputs, nil
}

// checkRecoveryScenario 检查是否为恢复场景并返回未完成的catalogs
func (g *GenerateWikiChain) checkRecoveryScenario(workspacePath string, catalogueResult definition.CatalogueResult) (bool, []definition.DocumentCatalog, error) {
	// 获取repo信息
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return false, nil, fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		log.Debugf("No existing repo found, not a recovery scenario")
		return false, nil, nil
	}

	// 只有pending状态才是恢复场景
	if repo.ProgressStatus != definition.DeepWikiProgressStatusPending {
		log.Debugf("Repo status is %s, not a recovery scenario", repo.ProgressStatus)
		return false, nil, nil
	}

	// 获取数据库中已存在的catalogs
	existingCatalogs, err := storage.GlobalStorageService.GetCatalogsByRepoID(repo.ID)
	if err != nil {
		return false, nil, fmt.Errorf("failed to get existing catalogs: %w", err)
	}

	if len(existingCatalogs) == 0 {
		log.Debugf("No existing catalogs found, not a recovery scenario")
		return false, nil, nil
	}

	log.Infof("Recovery scenario detected: found %d existing catalogs in database", len(existingCatalogs))

	// 创建已完成catalog的映射
	completedCatalogMap := make(map[string]bool)
	for _, catalog := range existingCatalogs {
		if catalog.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
			completedCatalogMap[catalog.ID] = true
		}
	}

	// 筛选出未完成的catalogs
	var pendingCatalogs []definition.DocumentCatalog

	// 类型断言获取DocumentCatalogs
	documentCatalogs, ok := catalogueResult.DocumentCatalogs.([]definition.DocumentCatalog)
	if !ok {
		return false, nil, fmt.Errorf("invalid DocumentCatalogs type")
	}

	for _, docCatalog := range documentCatalogs {
		if !completedCatalogMap[docCatalog.Id] {
			pendingCatalogs = append(pendingCatalogs, docCatalog)
		}
	}

	log.Infof("Recovery analysis: %d completed, %d pending out of %d total catalogs",
		len(completedCatalogMap), len(pendingCatalogs), len(documentCatalogs))

	return true, pendingCatalogs, nil
}

func (g *GenerateWikiChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g *GenerateWikiChain) GetInputKeys() []string {
	return []string{}
}

func (g *GenerateWikiChain) GetOutputKeys() []string {
	return []string{}
}
