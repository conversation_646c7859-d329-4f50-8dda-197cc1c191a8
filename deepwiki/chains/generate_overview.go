package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/deepwiki/service"

	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/memory"

	"cosy/util"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

const (
	// 重试配置
	overviewMaxRetryAttempts = 5
	overviewRetryDelay       = 5 * time.Second
)

type GenerateOverviewChain struct {
}

func (g GenerateOverviewChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: Overview failed - Error: missing CreateDeepwikiRequest in inputs")
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 检查inputs中是否已有Overview内容（恢复场景）
	if existingOverview, exists := inputs[chainsCommon.KeyOverviewContent]; exists && existingOverview != nil {
		overviewContent := existingOverview.(string)
		if overviewContent != "" {
			log.Infof("Deepwiki: Overview already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return inputs, nil // 直接跳过生成
		}
	}

	log.Infof("Deepwiki: Overview start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= overviewMaxRetryAttempts; attempt++ {
		agentCtx, _, err := agent.InitAgentContext(ctx)
		if err != nil {
			log.Infof("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
			return nil, err
		}
		requestId, _ := inputs[chainsCommon.KeyRequestId].(string)
		overviewGenerateAgent, err := support.MakeAgent(requestId, common.OverviewGenerateAgentBuilderIdentifier)
		if err != nil {
			log.Infof("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: failed to create agent", repoName, request.WorkspacePath)
			panic(err)
		}

		wikiSupport.InitCurrentGraphStat(inputs, "OverviewGenerateGraph")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in agent.RunSync (attempt %d/%d): %v\n%s", attempt, overviewMaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-overview-generate] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			//TODO 先同步执行，上线后考虑调整成异步执行，因为后续流程没有依赖overview agent的执行结果
			runErr = overviewGenerateAgent.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			// Agent执行成功，但需要验证是否生成了有效内容
			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Debugf("[deepwiki-overview-generate] Synced %d keys from agent state to inputs", len(tempInputs))
				}
			}

			// 检查是否生成了有效的Overview内容（不为空且长度大于等于100字符）
			var hasValidContent bool
			var contentLength int
			const minOverviewContentLength = 200

			if overviewContent, exists := inputs[chainsCommon.KeyOverviewContent]; exists {
				if contentStr, ok := overviewContent.(string); ok {
					trimmedContent := strings.TrimSpace(contentStr)
					contentLength = len(trimmedContent)

					if trimmedContent != "" && contentLength >= minOverviewContentLength {
						hasValidContent = true
						log.Debugf("[deepwiki-overview-generate] Agent generated valid overview content, length: %d", contentLength)
					} else {
						log.Debugf("[deepwiki-overview-generate] Agent generated insufficient overview content, length: %d (min required: %d)", contentLength, minOverviewContentLength)
					}
				}
			}

			if hasValidContent {
				if attempt > 1 {
					log.Infof("[deepwiki-overview-generate] Agent succeeded on attempt %d/%d, overview content length: %d", attempt, overviewMaxRetryAttempts, contentLength)
				}
				// 成功时记录统计信息
				wikiSupport.AppendGraphStatToWikiStat(inputs)
				// 成功时清除lastErr
				lastErr = nil
				break
			} else {
				// Agent执行成功但没有生成有效的Overview内容（为空或长度不足），视为失败需要重试
				if contentLength == 0 {
					runErr = fmt.Errorf("agent executed successfully but generated empty overview content")
					log.Debugf("[deepwiki-overview-generate] Agent generated empty overview content (attempt %d/%d)", attempt, overviewMaxRetryAttempts)
				} else {
					runErr = fmt.Errorf("agent executed successfully but generated insufficient overview content (length: %d, min required: %d)", contentLength, minOverviewContentLength)
					log.Debugf("[deepwiki-overview-generate] Agent generated insufficient overview content, length: %d (min required: %d) (attempt %d/%d)", contentLength, minOverviewContentLength, attempt, overviewMaxRetryAttempts)
				}
			}
		}

		// 收集图统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-overview-generate] Agent failed on attempt %d/%d: %v", attempt, overviewMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < overviewMaxRetryAttempts {
			log.Infof("[deepwiki-overview-generate] Retrying in %v... (attempt %d/%d)", overviewRetryDelay, attempt+1, overviewMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			case <-time.After(overviewRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-overview-generate] Agent failed after %d attempts, last error: %v", overviewMaxRetryAttempts, lastErr)
		log.Errorf("Deepwiki: Overview failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, lastErr)
		return inputs, nil // 不阻断流程，只记录错误
	}

	// 保存Overview内容到数据库
	if overviewContent, exists := inputs[chainsCommon.KeyOverviewContent]; exists && overviewContent != nil {
		//workspace, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)

		overview := wikiSupport.ExtractOverview(overviewContent.(string))

		err := g.saveOverviewToDatabase(ctx, inputs, overview)
		if err != nil {
			log.Errorf("Failed to save Overview to database: %v", err)
			// 不阻断流程继续，只记录错误
		} else {
			// Overview生成并保存成功后更新检查点
			service.UpdateCheckpointOnSuccess(request.WorkspacePath, service.CheckpointOverviewCompleted)
		}
	}

	log.Infof("Deepwiki: Overview end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return inputs, nil
}

// saveOverviewToDatabase 保存Overview内容到数据库
func (g GenerateOverviewChain) saveOverviewToDatabase(ctx context.Context, inputs map[string]any, content string) error {
	// 获取必要的信息
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 获取项目名称
	repoName := getProjectNameFromPath(request.WorkspacePath)

	// 查找repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	var repoID string
	if repo == nil {
		// 如果还没有repo记录，先创建
		repoID = uuid.NewString()
		newRepo := &definition.AgentWikiRepo{
			ID:               repoID,
			WorkspacePath:    request.WorkspacePath,
			Name:             repoName,
			ProgressStatus:   definition.DeepWikiProgressStatusProcessing,
			LastCommitID:     "",
			LastCommitUpdate: time.Now(),
			GmtCreate:        time.Now(),
			GmtModified:      time.Now(),
		}
		err = storage.GlobalStorageService.CreateWikiRepo(newRepo)
		if err != nil {
			return fmt.Errorf("failed to create wiki repo: %w", err)
		}
	} else {
		repoID = repo.ID
	}

	// 创建Overview记录
	overview := &definition.AgentWikiOverview{
		ID:            uuid.NewString(),
		RepoID:        repoID,
		Content:       content,
		WorkspacePath: request.WorkspacePath,
		GmtCreate:     time.Now(),
		GmtModified:   time.Now(),
	}

	err = storage.GlobalStorageService.CreateOverview(overview)
	if err != nil {
		return fmt.Errorf("failed to create Overview: %w", err)
	}

	log.Infof("Successfully saved Overview to database for repository: %s (ID: %s)", repoName, overview.ID)
	return nil
}
func (g GenerateOverviewChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateOverviewChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateOverviewChain) GetOutputKeys() []string {
	return []string{}
}
