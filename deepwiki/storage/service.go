package storage

import (
	"context"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/knowledge"
	"cosy/log"
	"cosy/util/encrypt"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

var GlobalStorageService *LingmaWikiStorageService

// 默认工程目录树节点数限制
const WorkspaceTreeTokenLimit = 15000

// wiki item content 加密解密使用的密钥，与database中的密钥保持一致
var wikiEncryptKey = "QbgzpWzN7tfe43gf"

// wiki加密开关控制，通过环境变量WIKI_ENCRYPTION_ENABLED控制
// 支持的值："true", "false"，默认为false（关闭加密）
func isWikiEncryptionEnabled() bool {
	return false
}

type LingmaWikiStorageService struct {
	db *sql.DB // 数据库连接
}

// doWikiEncrypt 对wiki内容进行加密
func doWikiEncrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}
	text, _ := encrypt.AesEncryptWithBase64(plaintext, wikiEncryptKey)
	if text == "" {
		return "", fmt.Errorf("wiki encrypt error")
	}
	return text, nil
}

// doWikiDecrypt 对wiki内容进行解密
func doWikiDecrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}
	defer func() {
		if r := recover(); r != nil {
			log.Warnf("Recovered from wiki decrypt panic. err: %+v", r)
		}
	}()
	text, _ := encrypt.AesDecryptWithBase64(ciphertext, wikiEncryptKey)
	if text == "" {
		return "", fmt.Errorf("wiki decrypt error")
	}
	return text, nil
}

// isContentEncrypted 检测content是否已经加密
func isContentEncrypted(content string) bool {
	if content == "" {
		return false
	}
	// 尝试解密，如果成功说明是密文，失败说明是明文
	_, err := doWikiDecrypt(content)
	return err == nil
}

// encryptWikiItem 对wiki item的content字段进行加密（防重复加密，支持特性开关控制）
func encryptWikiItem(item *definition.AgentWikiItem) error {
	if item == nil {
		return errors.New("wiki item is nil")
	}

	// 检查是否启用了Wiki加密特性
	if !isWikiEncryptionEnabled() {
		log.Debugf("Wiki encryption is disabled, skipping encryption")
		return nil
	}

	// 如果content已经是密文，直接返回，避免重复加密
	if isContentEncrypted(item.Content) {
		log.Debugf("Content already encrypted for wiki item, skipping encryption")
		return nil
	}

	// 否则进行加密
	if encryptedContent, err := doWikiEncrypt(item.Content); err != nil {
		return err
	} else {
		item.Content = encryptedContent
	}
	return nil
}

// decryptWikiItem 对wiki item的content字段进行解密（仅在有必要时）
func decryptWikiItem(item *definition.AgentWikiItem) error {
	if item == nil {
		return errors.New("wiki item is nil")
	}

	// 如果加密特性未启用，无需解密
	if !isWikiEncryptionEnabled() {
		return nil
	}

	// 尝试解密，如果失败可能是明文内容
	if decryptedContent, err := doWikiDecrypt(item.Content); err != nil {
		// 解密失败时不返回错误，可能是明文内容
		log.Errorf("Failed to decrypt wiki item content, might be plain text: %v", err)
		return nil
	} else {
		item.Content = decryptedContent
	}
	return nil
}

// decryptWikiItems 对多个wiki item的content字段进行解密
func decryptWikiItems(items []*definition.AgentWikiItem) {
	if items != nil && len(items) > 0 {
		for _, item := range items {
			if err := decryptWikiItem(item); err != nil {
				log.Errorf("decrypt wiki item error. item id: %s, err: %+v", item.ID, err)
			}
		}
	}
}

// NewStorageWikiService 创建一个新的 LingmaWikiStorageService
func NewStorageWikiService(db *sql.DB) (*LingmaWikiStorageService, error) {
	if db == nil {
		return nil, fmt.Errorf("db connection is nil")
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &LingmaWikiStorageService{db: db}, nil
}

// Close 关闭数据库连接
func (s *LingmaWikiStorageService) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// GetDB 获取数据库连接（供knowledge关系处理使用）
func (s *LingmaWikiStorageService) GetDB() *sql.DB {
	return s.db
}

// GetWikiRepoByWorkspacePath 根据工作区路径获取wiki仓库
func (s *LingmaWikiStorageService) GetWikiRepoByWorkspacePath(workspacePath string) (*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		WHERE workspace_path = ?
	`

	row := s.db.QueryRow(query, workspacePath)

	repo := &definition.AgentWikiRepo{}
	err := row.Scan(
		&repo.ID,
		&repo.WorkspacePath,
		&repo.Name,
		&repo.ProgressStatus,
		&repo.OptimizedCatalog,
		&repo.CurrentDocumentStructure,
		&repo.CatalogueThinkContent,
		&repo.RecoveryCheckpoint,
		&repo.LastCommitID,
		&repo.LastCommitUpdate,
		&repo.GmtCreate,
		&repo.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki repo by workspace path: %v", err)
		return nil, fmt.Errorf("failed to get wiki repo: %w", err)
	}

	return repo, nil
}

// GetWikiRepoByID 根据ID获取wiki仓库
func (s *LingmaWikiStorageService) GetWikiRepoByID(repoID string) (*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, repoID)

	repo := &definition.AgentWikiRepo{}
	err := row.Scan(
		&repo.ID,
		&repo.WorkspacePath,
		&repo.Name,
		&repo.ProgressStatus,
		&repo.OptimizedCatalog,
		&repo.CurrentDocumentStructure,
		&repo.CatalogueThinkContent,
		&repo.RecoveryCheckpoint,
		&repo.LastCommitID,
		&repo.LastCommitUpdate,
		&repo.GmtCreate,
		&repo.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki repo by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki repo: %w", err)
	}

	return repo, nil
}

// GetAllWikiRepos 获取所有wiki仓库
func (s *LingmaWikiStorageService) GetAllWikiRepos() ([]*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		ORDER BY gmt_create DESC
	`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query all wiki repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.AgentWikiRepo
	for rows.Next() {
		repo := &definition.AgentWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan wiki repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// CreateWikiRepo 创建wiki仓库
func (s *LingmaWikiStorageService) CreateWikiRepo(repo *definition.AgentWikiRepo) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if repo.GmtCreate.IsZero() {
		repo.GmtCreate = now
	}
	if repo.GmtModified.IsZero() {
		repo.GmtModified = now
	}

	query := `
		INSERT INTO agent_wiki_repo 
		(id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		 catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		repo.ID,
		repo.WorkspacePath,
		repo.Name,
		repo.ProgressStatus,
		repo.OptimizedCatalog,
		repo.CurrentDocumentStructure,
		repo.CatalogueThinkContent,
		repo.RecoveryCheckpoint,
		repo.LastCommitID,
		repo.LastCommitUpdate,
		repo.GmtCreate,
		repo.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki repo: %v", err)
		return fmt.Errorf("failed to create wiki repo: %w", err)
	}

	return nil
}

// CreateWikiRepoIfNotExists 原子性创建wiki仓库，如果workspace_path已存在则返回现有的repo
func (s *LingmaWikiStorageService) CreateWikiRepoIfNotExists(repo *definition.AgentWikiRepo) (*definition.AgentWikiRepo, bool, error) {
	// 首先尝试获取现有的repo
	existingRepo, err := s.GetWikiRepoByWorkspacePath(repo.WorkspacePath)
	if err != nil {
		return nil, false, fmt.Errorf("failed to check existing repo: %w", err)
	}

	// 如果已存在，返回现有的repo
	if existingRepo != nil {
		log.Infof("Wiki repo already exists for workspace: %s, ID: %s", repo.WorkspacePath, existingRepo.ID)
		return existingRepo, false, nil
	}

	// 尝试创建新的repo
	err = s.CreateWikiRepo(repo)
	if err != nil {
		// 检查是否是唯一性约束错误
		if isUniqueConstraintError(err) {
			// 并发情况下另一个进程可能已经创建了repo，尝试再次获取
			existingRepo, getErr := s.GetWikiRepoByWorkspacePath(repo.WorkspacePath)
			if getErr != nil {
				return nil, false, fmt.Errorf("failed to get repo after unique constraint error: %w", getErr)
			}
			if existingRepo != nil {
				log.Infof("Wiki repo was created concurrently for workspace: %s, ID: %s", repo.WorkspacePath, existingRepo.ID)
				return existingRepo, false, nil
			}
		}
		return nil, false, fmt.Errorf("failed to create wiki repo: %w", err)
	}

	log.Infof("Successfully created new wiki repo for workspace: %s, ID: %s", repo.WorkspacePath, repo.ID)
	return repo, true, nil
}

// isUniqueConstraintError 检查是否是唯一性约束错误
func isUniqueConstraintError(err error) bool {
	if err == nil {
		return false
	}
	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "unique") ||
		strings.Contains(errStr, "constraint") ||
		strings.Contains(errStr, "duplicate")
}

// UpdateWikiRepo 更新wiki仓库
func (s *LingmaWikiStorageService) UpdateWikiRepo(repo *definition.AgentWikiRepo) error {
	repo.GmtModified = time.Now()

	query := `
		UPDATE agent_wiki_repo 
		SET workspace_path = ?, name = ?, progress_status = ?, 
		    optimized_catalog = ?, current_document_structure = ?, 
		    catalogue_think_content = ?, recovery_checkpoint = ?,
		    last_commit_id = ?, last_commit_update = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		repo.WorkspacePath,
		repo.Name,
		repo.ProgressStatus,
		repo.OptimizedCatalog,
		repo.CurrentDocumentStructure,
		repo.CatalogueThinkContent,
		repo.RecoveryCheckpoint,
		repo.LastCommitID,
		repo.LastCommitUpdate,
		repo.GmtModified,
		repo.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki repo: %v", err)
		return fmt.Errorf("failed to update wiki repo: %w", err)
	}

	return nil
}

// DeleteWikiRepo 删除wiki仓库
func (s *LingmaWikiStorageService) DeleteWikiRepo(repoID string) error {
	query := `DELETE FROM agent_wiki_repo WHERE id = ?`

	_, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete wiki repo: %v", err)
		return fmt.Errorf("failed to delete wiki repo: %w", err)
	}

	return nil
}

// GetCatalogByID 根据ID获取目录
func (s *LingmaWikiStorageService) GetCatalogByID(catalogID string) (*definition.AgentWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_catalog 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, catalogID)

	catalog := &definition.AgentWikiCatalog{}
	err := row.Scan(
		&catalog.ID,
		&catalog.RepoID,
		&catalog.Name,
		&catalog.Description,
		&catalog.Prompt,
		&catalog.ParentID,
		&catalog.Order,
		&catalog.ProgressStatus,
		&catalog.DependentFiles,
		&catalog.Keywords,
		&catalog.WorkspacePath,
		&catalog.GmtCreate,
		&catalog.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki catalog by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki catalog: %w", err)
	}

	return catalog, nil
}

// CreateCatalog 创建目录
func (s *LingmaWikiStorageService) CreateCatalog(catalog *definition.AgentWikiCatalog) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if catalog.GmtCreate.IsZero() {
		catalog.GmtCreate = now
	}
	if catalog.GmtModified.IsZero() {
		catalog.GmtModified = now
	}

	query := `
		INSERT INTO agent_wiki_catalog 
		(id, repo_id, name, description, prompt, parent_id, 
		"order", progress_status, dependent_files, keywords, 
		workspace_path, gmt_create, gmt_modified, raw_data, layer_level)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		catalog.ID,
		catalog.RepoID,
		catalog.Name,
		catalog.Description,
		catalog.Prompt,
		catalog.ParentID,
		catalog.Order,
		catalog.ProgressStatus,
		catalog.DependentFiles,
		catalog.Keywords,
		catalog.WorkspacePath,
		catalog.GmtCreate,
		catalog.GmtModified,
		catalog.RawData,
		catalog.LayerLevel,
	)

	if err != nil {
		log.Errorf("Failed to create wiki catalog: %v", err)
		return fmt.Errorf("failed to create wiki catalog: %w", err)
	}

	return nil
}

// UpdateCatalog 更新目录
func (s *LingmaWikiStorageService) UpdateCatalog(catalog *definition.AgentWikiCatalog) error {
	catalog.GmtModified = time.Now()

	query := `
		UPDATE agent_wiki_catalog 
		SET repo_id = ?, name = ?, description = ?, prompt = ?, 
		    parent_id = ?, "order" = ?, progress_status = ?, 
		    dependent_files = ?, keywords = ?, 
		    workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		catalog.RepoID,
		catalog.Name,
		catalog.Description,
		catalog.Prompt,
		catalog.ParentID,
		catalog.Order,
		catalog.ProgressStatus,
		catalog.DependentFiles,
		catalog.Keywords,
		catalog.WorkspacePath,
		catalog.GmtModified,
		catalog.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki catalog: %v", err)
		return fmt.Errorf("failed to update wiki catalog: %w", err)
	}

	return nil
}

// DeleteCatalog 删除目录
func (s *LingmaWikiStorageService) DeleteCatalog(catalogID string) error {
	// 先获取catalog信息用于清理关系
	catalog, err := s.GetCatalogByID(catalogID)
	if err != nil {
		log.Warnf("Failed to get catalog before deletion: %v", err)
	}

	query := `DELETE FROM agent_wiki_catalog WHERE id = ?`

	_, err = s.db.Exec(query, catalogID)
	if err != nil {
		log.Errorf("Failed to delete wiki catalog: %v", err)
		return fmt.Errorf("failed to delete wiki catalog: %w", err)
	}

	// 清理knowledge关系
	if catalog != nil && catalog.WorkspacePath != "" {
		ctx := context.Background()
		knowledge.OnWikiCatalogDeleted(ctx, s.db, catalog.WorkspacePath, catalogID)
	}

	return nil
}

// GetCatalogsByRepoID 获取指定repo下的所有目录
func (s *LingmaWikiStorageService) GetCatalogsByRepoID(repoID string) ([]*definition.AgentWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified, raw_data, layer_level
		FROM agent_wiki_catalog 
		WHERE repo_id = ?
		ORDER BY layer_level,"order"
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalogs: %w", err)
	}
	defer rows.Close()

	var catalogs []*definition.AgentWikiCatalog
	for rows.Next() {
		catalog := &definition.AgentWikiCatalog{}
		err := rows.Scan(
			&catalog.ID,
			&catalog.RepoID,
			&catalog.Name,
			&catalog.Description,
			&catalog.Prompt,
			&catalog.ParentID,
			&catalog.Order,
			&catalog.ProgressStatus,
			&catalog.DependentFiles,
			&catalog.Keywords,
			&catalog.WorkspacePath,
			&catalog.GmtCreate,
			&catalog.GmtModified,
			&catalog.RawData,
			&catalog.LayerLevel,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan catalog: %w", err)
		}
		catalogs = append(catalogs, catalog)
	}

	return catalogs, nil
}

// GetUnCompleteCatalogsByRepoID 获取指定repo下的未完成的目录
func (s *LingmaWikiStorageService) GetUnCompleteCatalogsByRepoID(repoID string) ([]*definition.AgentWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified, raw_data, layer_level
		FROM agent_wiki_catalog 
		WHERE repo_id = ? AND progress_status!='completed'
		ORDER BY layer_level,"order"
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalogs: %w", err)
	}
	defer rows.Close()

	var catalogs []*definition.AgentWikiCatalog
	for rows.Next() {
		catalog := &definition.AgentWikiCatalog{}
		err := rows.Scan(
			&catalog.ID,
			&catalog.RepoID,
			&catalog.Name,
			&catalog.Description,
			&catalog.Prompt,
			&catalog.ParentID,
			&catalog.Order,
			&catalog.ProgressStatus,
			&catalog.DependentFiles,
			&catalog.Keywords,
			&catalog.WorkspacePath,
			&catalog.GmtCreate,
			&catalog.GmtModified,
			&catalog.RawData,
			&catalog.LayerLevel,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan catalog: %w", err)
		}
		catalogs = append(catalogs, catalog)
	}

	return catalogs, nil
}

// GetCatalogsByRepoIDAndStatus 获取指定repo下指定状态的目录列表
func (s *LingmaWikiStorageService) GetCatalogsByRepoIDAndStatus(repoID string, status string) ([]*definition.AgentWikiCatalog, error) {
	query := `
		SELECT id, repo_id, name, description, prompt, parent_id, 
		       "order", progress_status, dependent_files, keywords, 
		       workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_catalog 
		WHERE repo_id = ? AND progress_status = ?
		ORDER BY layer_level,"order"
	`

	rows, err := s.db.Query(query, repoID, status)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalogs by status: %w", err)
	}
	defer rows.Close()

	var catalogs []*definition.AgentWikiCatalog
	for rows.Next() {
		catalog := &definition.AgentWikiCatalog{}
		err := rows.Scan(
			&catalog.ID,
			&catalog.RepoID,
			&catalog.Name,
			&catalog.Description,
			&catalog.Prompt,
			&catalog.ParentID,
			&catalog.Order,
			&catalog.ProgressStatus,
			&catalog.DependentFiles,
			&catalog.Keywords,
			&catalog.WorkspacePath,
			&catalog.GmtCreate,
			&catalog.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan catalog: %w", err)
		}
		catalogs = append(catalogs, catalog)
	}

	return catalogs, nil
}

// GetReadmeByID 根据ID获取Readme
func (s *LingmaWikiStorageService) GetReadmeByID(readmeID string) (*definition.AgentWikiReadme, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_readme 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, readmeID)

	readme := &definition.AgentWikiReadme{}
	err := row.Scan(
		&readme.ID,
		&readme.RepoID,
		&readme.Content,
		&readme.WorkspacePath,
		&readme.GmtCreate,
		&readme.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki readme by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki readme: %w", err)
	}

	return readme, nil
}

// CreateReadme 创建Readme
func (s *LingmaWikiStorageService) CreateReadme(readme *definition.AgentWikiReadme) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if readme.GmtCreate.IsZero() {
		readme.GmtCreate = now
	}
	if readme.GmtModified.IsZero() {
		readme.GmtModified = now
	}

	query := `
		INSERT INTO agent_wiki_readme 
		(id, repo_id, content, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		readme.ID,
		readme.RepoID,
		readme.Content,
		readme.WorkspacePath,
		readme.GmtCreate,
		readme.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki readme: %v", err)
		return fmt.Errorf("failed to create wiki readme: %w", err)
	}

	return nil
}

// UpdateReadme 更新Readme
func (s *LingmaWikiStorageService) UpdateReadme(readme *definition.AgentWikiReadme) error {
	readme.GmtModified = time.Now()

	query := `
		UPDATE agent_wiki_readme 
		SET repo_id = ?, content = ?, workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		readme.RepoID,
		readme.Content,
		readme.WorkspacePath,
		readme.GmtModified,
		readme.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki readme: %v", err)
		return fmt.Errorf("failed to update wiki readme: %w", err)
	}

	return nil
}

// DeleteReadme 删除Readme
func (s *LingmaWikiStorageService) DeleteReadme(readmeID string) error {
	query := `DELETE FROM agent_wiki_readme WHERE id = ?`

	_, err := s.db.Exec(query, readmeID)
	if err != nil {
		log.Errorf("Failed to delete wiki readme: %v", err)
		return fmt.Errorf("failed to delete wiki readme: %w", err)
	}

	return nil
}

// GetOverviewByID 根据ID获取Overview
func (s *LingmaWikiStorageService) GetOverviewByID(overviewID string) (*definition.AgentWikiOverview, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_overview 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, overviewID)

	overview := &definition.AgentWikiOverview{}
	err := row.Scan(
		&overview.ID,
		&overview.RepoID,
		&overview.Content,
		&overview.WorkspacePath,
		&overview.GmtCreate,
		&overview.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki overview by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki overview: %w", err)
	}

	return overview, nil
}

// CreateOverview 创建Overview
func (s *LingmaWikiStorageService) CreateOverview(overview *definition.AgentWikiOverview) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if overview.GmtCreate.IsZero() {
		overview.GmtCreate = now
	}
	if overview.GmtModified.IsZero() {
		overview.GmtModified = now
	}

	query := `
		INSERT INTO agent_wiki_overview 
		(id, repo_id, content, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		overview.ID,
		overview.RepoID,
		overview.Content,
		overview.WorkspacePath,
		overview.GmtCreate,
		overview.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki overview: %v", err)
		return fmt.Errorf("failed to create wiki overview: %w", err)
	}

	// Overview现在只存储在agent_wiki_overview表中，不再创建对应的wiki item

	return nil
}

// UpdateOverview 更新Overview
func (s *LingmaWikiStorageService) UpdateOverview(overview *definition.AgentWikiOverview) error {
	overview.GmtModified = time.Now()

	query := `
		UPDATE agent_wiki_overview 
		SET repo_id = ?, content = ?, workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		overview.RepoID,
		overview.Content,
		overview.WorkspacePath,
		overview.GmtModified,
		overview.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki overview: %v", err)
		return fmt.Errorf("failed to update wiki overview: %w", err)
	}

	return nil
}

// DeleteOverview 删除Overview
func (s *LingmaWikiStorageService) DeleteOverview(overviewID string) error {
	query := `DELETE FROM agent_wiki_overview WHERE id = ?`

	_, err := s.db.Exec(query, overviewID)
	if err != nil {
		log.Errorf("Failed to delete wiki overview: %v", err)
		return fmt.Errorf("failed to delete wiki overview: %w", err)
	}

	return nil
}

// GetWikiItemByID 根据ID获取WikiItem
func (s *LingmaWikiStorageService) GetWikiItemByID(itemID string) (*definition.AgentWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_item 
		WHERE id = ?
	`

	row := s.db.QueryRow(query, itemID)

	item := &definition.AgentWikiItem{}
	err := row.Scan(
		&item.ID,
		&item.CatalogID,
		&item.Content,
		&item.Title,
		&item.Description,
		&item.Extend,
		&item.ProgressStatus,
		&item.RepoID,
		&item.WorkspacePath,
		&item.GmtCreate,
		&item.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki item by ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki item: %w", err)
	}

	// 解密wiki item content
	if err := decryptWikiItem(item); err != nil {
		log.Warnf("Decrypt wiki item error. item id: %s, error: %v", item.ID, err)
	}

	return item, nil
}

// GetWikiItemByCatalogID 根据CatalogID获取WikiItem
func (s *LingmaWikiStorageService) GetWikiItemByCatalogID(catalogID string) (*definition.AgentWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_item 
		WHERE catalog_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, catalogID)

	item := &definition.AgentWikiItem{}
	err := row.Scan(
		&item.ID,
		&item.CatalogID,
		&item.Content,
		&item.Title,
		&item.Description,
		&item.Extend,
		&item.ProgressStatus,
		&item.RepoID,
		&item.WorkspacePath,
		&item.GmtCreate,
		&item.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki item by catalog ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki item: %w", err)
	}

	// 解密wiki item content
	if err := decryptWikiItem(item); err != nil {
		log.Warnf("Decrypt wiki item error. item id: %s, error: %v", item.ID, err)
	}

	return item, nil
}

// CreateWikiItem 创建WikiItem
func (s *LingmaWikiStorageService) CreateWikiItem(item *definition.AgentWikiItem) error {
	now := time.Now()

	// 确保创建和修改时间已设置
	if item.GmtCreate.IsZero() {
		item.GmtCreate = now
	}
	if item.GmtModified.IsZero() {
		item.GmtModified = now
	}

	// 加密wiki item content
	if err := encryptWikiItem(item); err != nil {
		log.Errorf("Encrypt wiki item error. item id: %s, error: %v", item.ID, err)
	}

	query := `
		INSERT INTO agent_wiki_item 
		(id, catalog_id, content, title, description, extend, 
		progress_status, repo_id, workspace_path, gmt_create, gmt_modified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		item.ID,
		item.CatalogID,
		item.Content,
		item.Title,
		item.Description,
		item.Extend,
		item.ProgressStatus,
		item.RepoID,
		item.WorkspacePath,
		item.GmtCreate,
		item.GmtModified,
	)

	if err != nil {
		log.Errorf("Failed to create wiki item: %v", err)
		return fmt.Errorf("failed to create wiki item: %w", err)
	}

	// 处理knowledge关系
	ctx := context.Background()
	if item.WorkspacePath != "" && item.Content != "" {
		knowledge.OnWikiItemCreated(ctx, s.db, item.WorkspacePath, item)
	}

	return nil
}

// UpdateWikiItem 更新WikiItem
func (s *LingmaWikiStorageService) UpdateWikiItem(item *definition.AgentWikiItem) error {
	item.GmtModified = time.Now()

	// 加密wiki item content
	if err := encryptWikiItem(item); err != nil {
		log.Errorf("Encrypt wiki item error when update. item id: %s, error: %v", item.ID, err)
	}

	query := `
		UPDATE agent_wiki_item 
		SET catalog_id = ?, content = ?, title = ?, description = ?, 
		    extend = ?, progress_status = ?, repo_id = ?, 
		    workspace_path = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(
		query,
		item.CatalogID,
		item.Content,
		item.Title,
		item.Description,
		item.Extend,
		item.ProgressStatus,
		item.RepoID,
		item.WorkspacePath,
		item.GmtModified,
		item.ID,
	)

	if err != nil {
		log.Errorf("Failed to update wiki item: %v", err)
		return fmt.Errorf("failed to update wiki item: %w", err)
	}

	// 处理knowledge关系
	ctx := context.Background()
	if item.WorkspacePath != "" && item.Content != "" {
		knowledge.OnWikiItemUpdated(ctx, s.db, item.WorkspacePath, item)
	}

	return nil
}

// DeleteWikiItem 删除WikiItem
func (s *LingmaWikiStorageService) DeleteWikiItem(itemID string) error {
	// 先获取item信息用于清理关系
	item, err := s.GetWikiItemByID(itemID)
	if err != nil {
		log.Warnf("Failed to get wiki item before deletion: %v", err)
	}

	query := `DELETE FROM agent_wiki_item WHERE id = ?`

	_, err = s.db.Exec(query, itemID)
	if err != nil {
		log.Errorf("Failed to delete wiki item: %v", err)
		return fmt.Errorf("failed to delete wiki item: %w", err)
	}

	// 清理knowledge关系
	if item != nil && item.WorkspacePath != "" {
		ctx := context.Background()
		knowledge.OnWikiItemDeleted(ctx, s.db, item.WorkspacePath, itemID)
	}

	return nil
}

// GetWikiItemIdsByRepoAndWorkspace 根据仓库ID和工作区路径获取所有wiki item的ID列表
func (s *LingmaWikiStorageService) GetWikiItemIdsByRepoAndWorkspace(workspacePath string) ([]string, error) {
	query := `
		SELECT id
		FROM agent_wiki_item 
		WHERE workspace_path = ?
		ORDER BY gmt_create ASC
	`

	rows, err := s.db.Query(query, workspacePath)
	if err != nil {
		log.Errorf("Failed to query wiki item IDs by repo and workspace: %v", err)
		return nil, fmt.Errorf("failed to query wiki item IDs: %w", err)
	}
	defer rows.Close()

	var ids []string
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			log.Errorf("Failed to scan wiki item ID: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item ID: %w", err)
		}
		ids = append(ids, id)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ids, nil
}

// 根据workspacePath 查询所有 wiki_items
func (s *LingmaWikiStorageService) GetWikiItemsByWorkspacePath(workspacePath string) ([]*definition.AgentWikiItem, error) {
	query := `
		SELECT id, catalog_id, content, title, description, extend, 
		       progress_status, repo_id, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_item 
		WHERE workspace_path = ? and progress_status='completed'
	`

	rows, err := s.db.Query(query, workspacePath)
	if err != nil {
		log.Errorf("Failed to query wiki items by workspace path: %v", err)
		return nil, fmt.Errorf("failed to query wiki items: %w", err)
	}
	defer rows.Close()

	var items []*definition.AgentWikiItem
	for rows.Next() {
		item := &definition.AgentWikiItem{}
		if err := rows.Scan(&item.ID, &item.CatalogID, &item.Content, &item.Title, &item.Description, &item.Extend, &item.ProgressStatus, &item.RepoID, &item.WorkspacePath, &item.GmtCreate, &item.GmtModified); err != nil {
			log.Errorf("Failed to scan wiki item: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item: %w", err)
		}
		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	// 解密所有wiki items的content
	decryptWikiItems(items)

	return items, nil
}

// GetExistingDocumentCatalogues 获取现有的文档目录结构
func (s *LingmaWikiStorageService) GetExistingDocumentCatalogues(workspacePath string) (*[]definition.DocumentCatalog, error) {
	// 获取仓库信息
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	items, err := s.GetCatalogsByRepoID(repo.ID)
	if err != nil {
		return nil, err
	}
	docCatalogues := ConvertToDocumentCatalogs(items, workspacePath)
	return &docCatalogues, nil
}

// GetExistingOptimizedCatalogues 获取现有的优化后的目录结构
func (s *LingmaWikiStorageService) GetExistingOptimizedCatalogues(workspacePath string) (*string, error) {
	// 获取仓库信息
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get repo: %w", err)
	}

	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		referenceCatalogItemsString := workspaceTreeIndexer.WorkspaceTree.GetTree(WorkspaceTreeTokenLimit)
		return &referenceCatalogItemsString, nil
	}
	if repo == nil {
		return nil, fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	if repo.OptimizedCatalog == "" {
		return nil, fmt.Errorf("optimized catalog is empty for workspace: %s", workspacePath)
	}

	var optimizedCatalogue string
	err = json.Unmarshal([]byte(repo.OptimizedCatalog), &optimizedCatalogue)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal optimized catalog: %w", err)
	}
	return &optimizedCatalogue, nil
}

// GetCatalogStatusByRepoID 获取repo下所有catalog的状态统计
func (s *LingmaWikiStorageService) GetCatalogStatusByRepoID(repoID string) (map[string]int, error) {
	query := `
		SELECT progress_status, COUNT(*) as count
		FROM agent_wiki_catalog 
		WHERE repo_id = ? 
		GROUP BY progress_status
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to query catalog status: %w", err)
	}
	defer rows.Close()

	statusCount := make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("failed to scan catalog status: %w", err)
		}
		statusCount[status] = count
	}

	return statusCount, nil
}

// GetWikiItemStatusByWorkspace 获取Workspace下所有wiki的状态统计
func (s *LingmaWikiStorageService) GetWikiItemStatusByWorkspace(workspacePath string) (map[string]int, error) {
	query := `
		SELECT progress_status, COUNT(*) as count
		FROM agent_wiki_item
		WHERE workspace_path = ? 
		GROUP BY progress_status
	`

	rows, err := s.db.Query(query, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to query wiki item status: %w", err)
	}
	defer rows.Close()

	statusCount := make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("failed to scan wiki item status: %w", err)
		}
		statusCount[status] = count
	}

	return statusCount, nil
}

// UpdateCatalogAndItemStatus 同时更新catalog和对应item的状态
func (s *LingmaWikiStorageService) UpdateCatalogAndItemStatus(catalogID, status string) error {
	log.Debugf("[UpdateCatalogAndItemStatus] Starting to update catalog %s to status: %s", catalogID, status)

	tx, err := s.db.Begin()
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to begin transaction: %v", err)
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 更新catalog状态
	catalogQuery := `
		UPDATE agent_wiki_catalog 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	result, err := tx.Exec(catalogQuery, status, time.Now(), catalogID)
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to update catalog status: %v", err)
		return fmt.Errorf("failed to update catalog status: %w", err)
	}

	catalogRowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("[UpdateCatalogAndItemStatus] Failed to get catalog rows affected: %v", err)
	} else {
		log.Debugf("[UpdateCatalogAndItemStatus] Updated catalog: %d rows affected", catalogRowsAffected)
	}

	// 更新对应的item状态
	itemQuery := `
		UPDATE agent_wiki_item 
		SET progress_status = ?, gmt_modified = ? 
		WHERE catalog_id = ?
	`
	itemResult, err := tx.Exec(itemQuery, status, time.Now(), catalogID)
	if err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to update item status: %v", err)
		return fmt.Errorf("failed to update item status: %w", err)
	}

	itemRowsAffected, err := itemResult.RowsAffected()
	if err != nil {
		log.Warnf("[UpdateCatalogAndItemStatus] Failed to get item rows affected: %v", err)
	} else {
		if itemRowsAffected == 0 {
			log.Debugf("[UpdateCatalogAndItemStatus] No item found for catalog %s (this is normal for new catalogs)", catalogID)
		} else {
			log.Debugf("[UpdateCatalogAndItemStatus] Updated item: %d rows affected", itemRowsAffected)
		}
	}

	if err = tx.Commit(); err != nil {
		log.Errorf("[UpdateCatalogAndItemStatus] Failed to commit transaction: %v", err)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	log.Debugf("[UpdateCatalogAndItemStatus] Successfully committed transaction for catalog %s", catalogID)

	// 如果更新为completed或failed，检查并更新repo状态
	if status == definition.DeepWikiProgressStatusCompleted || status == definition.DeepWikiProgressStatusFailed {
		// 获取catalog所属的repo
		catalog, err := s.GetCatalogByID(catalogID)
		if err != nil {
			return fmt.Errorf("failed to get catalog for repo status update: %w", err)
		}

		if catalog != nil {
			if err := s.CheckAndUpdateRepoStatus(catalog.RepoID); err != nil {
				// 记录错误但不阻断流程
				log.Errorf("Failed to check and update repo status: %v", err)
			}
		}
	}

	return nil
}

// CheckAndUpdateRepoStatus 检查并更新repo状态
func (s *LingmaWikiStorageService) CheckAndUpdateRepoStatus(repoID string) error {
	// 获取repo下所有catalog的状态统计
	statusCount, err := s.GetCatalogStatusByRepoID(repoID)
	if err != nil {
		return fmt.Errorf("failed to get catalog status: %w", err)
	}

	// 计算总的catalog数量
	totalCatalogs := 0
	for _, count := range statusCount {
		totalCatalogs += count
	}

	if totalCatalogs == 0 {
		// 没有catalog，保持repo当前状态
		return nil
	}

	// 判断repo应该的状态
	var newRepoStatus string
	completedCount := statusCount[definition.DeepWikiProgressStatusCompleted]
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	processingCount := statusCount[definition.DeepWikiProgressStatusProcessing]
	pendingCount := statusCount[definition.DeepWikiProgressStatusPending]

	log.Debugf("Repo %s catalog status breakdown: completed=%d, failed=%d, processing=%d, pending=%d, total=%d",
		repoID, completedCount, failedCount, processingCount, pendingCount, totalCatalogs)

	// 状态判断优先级（考虑补偿更新场景）：
	// 1. Processing: 有正在处理的catalog（包括补偿更新），即使有failed也优先显示processing
	// 2. Failed: 有失败的catalog且没有正在处理的
	// 3. Completed: 所有catalog都成功且无失败
	// 4. Pending: 所有catalog都在等待处理

	if processingCount > 0 {
		// 优先级最高：有正在处理的catalog（可能是补偿更新），即使有failed也显示processing
		newRepoStatus = definition.DeepWikiProgressStatusProcessing
		if failedCount > 0 {
			log.Infof("Repo %s set to PROCESSING: %d catalogs actively processing (including compensation for %d failed catalogs)",
				repoID, processingCount, failedCount)
		} else {
			log.Debugf("Repo %s set to PROCESSING: %d catalogs actively processing", repoID, processingCount)
		}
	} else if pendingCount > 0 && completedCount > 0 {
		// 有pending且有completed的，表示任务正在进行中（部分完成）
		newRepoStatus = definition.DeepWikiProgressStatusProcessing
		if failedCount > 0 {
			log.Infof("Repo %s set to PROCESSING: mixed state with %d pending, %d completed, and %d failed catalogs",
				repoID, pendingCount, completedCount, failedCount)
		} else {
			log.Debugf("Repo %s set to PROCESSING: mixed state with %d pending and %d completed catalogs",
				repoID, pendingCount, completedCount)
		}
	} else if failedCount > 0 {
		// 有失败的catalog且没有正在处理的（补偿已完成或未开始）
		newRepoStatus = definition.DeepWikiProgressStatusFailed
		log.Errorf("Repo %s set to FAILED: %d failed catalogs and no active processing (completed=%d, pending=%d)",
			repoID, failedCount, completedCount, pendingCount)
	} else if completedCount == totalCatalogs {
		// 所有catalog都completed且没有failed的
		newRepoStatus = definition.DeepWikiProgressStatusCompleted
		log.Infof("Repo %s set to COMPLETED: all %d catalogs completed successfully", repoID, totalCatalogs)
	} else if pendingCount == totalCatalogs {
		// 全部都是pending，表示还没有开始处理
		newRepoStatus = definition.DeepWikiProgressStatusPending
		log.Debugf("Repo %s set to PENDING: all %d catalogs waiting to be processed", repoID, totalCatalogs)
	} else {
		// 其他未预期的混合状态，默认为processing以保证安全
		newRepoStatus = definition.DeepWikiProgressStatusProcessing
		log.Warnf("Repo %s set to PROCESSING (fallback): unexpected mixed state (completed=%d, failed=%d, processing=%d, pending=%d)",
			repoID, completedCount, failedCount, processingCount, pendingCount)
	}

	// 获取当前repo状态
	repo, err := s.GetWikiRepoByID(repoID)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found: %s", repoID)
	}

	// 如果状态有变化，则更新
	if repo.ProgressStatus != newRepoStatus {
		oldStatus := repo.ProgressStatus
		repo.ProgressStatus = newRepoStatus
		repo.GmtModified = time.Now()

		if err := s.UpdateWikiRepo(repo); err != nil {
			return fmt.Errorf("failed to update repo status: %w", err)
		}

		// 根据状态变化的重要性记录不同级别的日志
		if newRepoStatus == definition.DeepWikiProgressStatusFailed {
			log.Errorf("REPO STATUS CHANGE: %s -> FAILED - %d failed catalogs with no active processing (completed=%d, pending=%d)",
				oldStatus, failedCount, completedCount, pendingCount)
		} else if newRepoStatus == definition.DeepWikiProgressStatusCompleted {
			log.Infof("REPO STATUS CHANGE: %s -> COMPLETED - All %d catalogs completed successfully",
				oldStatus, totalCatalogs)
		} else if newRepoStatus == definition.DeepWikiProgressStatusProcessing {
			if failedCount > 0 {
				log.Infof("REPO STATUS CHANGE: %s -> PROCESSING - Active processing with %d failed catalogs (compensation likely in progress)",
					oldStatus, failedCount)
			} else {
				log.Debugf("REPO STATUS CHANGE: %s -> PROCESSING (completed=%d, processing=%d, pending=%d, total=%d)",
					oldStatus, completedCount, processingCount, pendingCount, totalCatalogs)
			}
		} else {
			log.Debugf("REPO STATUS CHANGE: %s -> %s (completed=%d, failed=%d, processing=%d, pending=%d)",
				oldStatus, newRepoStatus, completedCount, failedCount, processingCount, pendingCount, totalCatalogs)
		}
	} else {
		log.Debugf("Repo %s status unchanged: %s (completed=%d, failed=%d, processing=%d, pending=%d, total=%d)",
			repoID, repo.ProgressStatus, completedCount, failedCount, processingCount, pendingCount, totalCatalogs)
	}

	return nil
}

// UpdateCatalogStatus 只更新catalog状态（向后兼容）
func (s *LingmaWikiStorageService) UpdateCatalogStatus(catalogID, status string) error {
	query := `
		UPDATE agent_wiki_catalog 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	_, err := s.db.Exec(query, status, time.Now(), catalogID)
	if err != nil {
		return fmt.Errorf("failed to update catalog status: %w", err)
	}
	return nil
}

// UpdateCatalogStatusByName 通过repo ID和名称更新catalog状态
func (s *LingmaWikiStorageService) UpdateCatalogStatusByName(repoID, name, status string) error {
	query := `
		UPDATE agent_wiki_catalog 
		SET progress_status = ?, gmt_modified = ? 
		WHERE repo_id = ? AND name = ?
	`
	_, err := s.db.Exec(query, status, time.Now(), repoID, name)
	if err != nil {
		return fmt.Errorf("failed to update catalog status by name: %w", err)
	}
	return nil
}

// UpdateItemStatus 只更新item状态（向后兼容）
func (s *LingmaWikiStorageService) UpdateItemStatus(itemID, status string) error {
	query := `
		UPDATE agent_wiki_item 
		SET progress_status = ?, gmt_modified = ? 
		WHERE id = ?
	`
	_, err := s.db.Exec(query, status, time.Now(), itemID)
	if err != nil {
		return fmt.Errorf("failed to update item status: %w", err)
	}
	return nil
}

// MarkCatalogAndItemAsProcessing 标记catalog和item为processing状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsProcessing(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusProcessing)
}

// MarkCatalogAndItemAsCompleted 标记catalog和item为completed状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsCompleted(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusCompleted)
}

// MarkCatalogAndItemAsFailed 标记catalog和item为failed状态
func (s *LingmaWikiStorageService) MarkCatalogAndItemAsFailed(catalogID string) error {
	return s.UpdateCatalogAndItemStatus(catalogID, definition.DeepWikiProgressStatusFailed)
}

// GetCatalogsCountByRepoID 获取repo下catalog的数量
func (s *LingmaWikiStorageService) GetCatalogsCountByRepoID(repoID string) (int, error) {
	query := `SELECT COUNT(*) FROM agent_wiki_catalog WHERE repo_id = ?`
	var count int
	err := s.db.QueryRow(query, repoID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count catalogs: %w", err)
	}
	return count, nil
}

// GetWikiItemsCountByRepoID 获取repo下wiki item的数量
func (s *LingmaWikiStorageService) GetWikiItemsCountByRepoID(repoID string) (int, error) {
	query := `
		SELECT COUNT(*) 
		FROM agent_wiki_item wi
		JOIN agent_wiki_catalog wc ON wi.catalog_id = wc.id
		WHERE wc.repo_id = ?
	`
	var count int
	err := s.db.QueryRow(query, repoID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count wiki items: %w", err)
	}
	return count, nil
}

// GetWikiItemIdsByRepoID 获取指定repo下所有wiki item的ID列表
func (s *LingmaWikiStorageService) GetWikiItemIdsByRepoID(repoID string) ([]string, error) {
	query := `
		SELECT id
		FROM agent_wiki_item 
		WHERE repo_id = ?
		ORDER BY gmt_create ASC
	`

	rows, err := s.db.Query(query, repoID)
	if err != nil {
		log.Errorf("Failed to query wiki item IDs by repo ID: %v", err)
		return nil, fmt.Errorf("failed to query wiki item IDs: %w", err)
	}
	defer rows.Close()

	var ids []string
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			log.Errorf("Failed to scan wiki item ID: %v", err)
			return nil, fmt.Errorf("failed to scan wiki item ID: %w", err)
		}
		ids = append(ids, id)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("Error iterating wiki item rows: %v", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return ids, nil
}

// DeleteWikiItemsByRepoID 删除指定repo下的所有wiki items
func (s *LingmaWikiStorageService) DeleteWikiItemsByRepoID(repoID string) error {
	query := `DELETE FROM agent_wiki_item WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete wiki items by repo ID: %v", err)
		return fmt.Errorf("failed to delete wiki items: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Debugf("Deleted %d wiki items for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteCatalogsByRepoID 删除指定repo下的所有catalogs
func (s *LingmaWikiStorageService) DeleteCatalogsByRepoID(repoID string) error {
	query := `DELETE FROM agent_wiki_catalog WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete catalogs by repo ID: %v", err)
		return fmt.Errorf("failed to delete catalogs: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Infof("Deleted %d catalogs for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteReadmeByRepoID 删除指定repo的readme
func (s *LingmaWikiStorageService) DeleteReadmeByRepoID(repoID string) error {
	query := `DELETE FROM agent_wiki_readme WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete readme by repo ID: %v", err)
		return fmt.Errorf("failed to delete readme: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Debugf("Deleted %d readme records for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// DeleteOverviewByRepoID 删除指定repo的overview
func (s *LingmaWikiStorageService) DeleteOverviewByRepoID(repoID string) error {
	query := `DELETE FROM agent_wiki_overview WHERE repo_id = ?`

	result, err := s.db.Exec(query, repoID)
	if err != nil {
		log.Errorf("Failed to delete overview by repo ID: %v", err)
		return fmt.Errorf("failed to delete overview: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Warnf("Failed to get rows affected count: %v", err)
	} else {
		log.Debugf("Deleted %d overview records for repo: %s", rowsAffected, repoID)
	}

	return nil
}

// ============== 中断恢复相关方法 ==============

// GetFailedRepos 获取所有失败状态的repo
func (s *LingmaWikiStorageService) GetFailedRepos() ([]*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusFailed)
	if err != nil {
		return nil, fmt.Errorf("failed to query failed repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.AgentWikiRepo
	for rows.Next() {
		repo := &definition.AgentWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan failed repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetProcessingRepos 获取所有processing状态的repo
func (s *LingmaWikiStorageService) GetProcessingRepos() ([]*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusProcessing)
	if err != nil {
		return nil, fmt.Errorf("failed to query processing repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.AgentWikiRepo
	for rows.Next() {
		repo := &definition.AgentWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan processing repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetCompletedWikiRepos 获取所有已完成状态的repo
func (s *LingmaWikiStorageService) GetCompletedWikiRepos() ([]*definition.AgentWikiRepo, error) {
	query := `
		SELECT id, workspace_path, name, progress_status, optimized_catalog, current_document_structure,
		       catalogue_think_content, recovery_checkpoint, last_commit_id, last_commit_update, gmt_create, gmt_modified
		FROM agent_wiki_repo 
		WHERE progress_status = ?
		ORDER BY gmt_modified DESC
	`

	rows, err := s.db.Query(query, definition.DeepWikiProgressStatusCompleted)
	if err != nil {
		return nil, fmt.Errorf("failed to query completed repos: %w", err)
	}
	defer rows.Close()

	var repos []*definition.AgentWikiRepo
	for rows.Next() {
		repo := &definition.AgentWikiRepo{}
		err := rows.Scan(
			&repo.ID,
			&repo.WorkspacePath,
			&repo.Name,
			&repo.ProgressStatus,
			&repo.OptimizedCatalog,
			&repo.CurrentDocumentStructure,
			&repo.CatalogueThinkContent,
			&repo.RecoveryCheckpoint,
			&repo.LastCommitID,
			&repo.LastCommitUpdate,
			&repo.GmtCreate,
			&repo.GmtModified,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan completed repo: %w", err)
		}
		repos = append(repos, repo)
	}

	return repos, nil
}

// GetReadmeByRepoID 根据RepoID获取README
func (s *LingmaWikiStorageService) GetReadmeByRepoID(repoID string) (*definition.AgentWikiReadme, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_readme 
		WHERE repo_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, repoID)

	readme := &definition.AgentWikiReadme{}
	err := row.Scan(
		&readme.ID,
		&readme.RepoID,
		&readme.Content,
		&readme.WorkspacePath,
		&readme.GmtCreate,
		&readme.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki readme by repo ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki readme: %w", err)
	}

	return readme, nil
}

// GetOverviewByRepoID 根据RepoID获取Overview
func (s *LingmaWikiStorageService) GetOverviewByRepoID(repoID string) (*definition.AgentWikiOverview, error) {
	query := `
		SELECT id, repo_id, content, workspace_path, gmt_create, gmt_modified
		FROM agent_wiki_overview 
		WHERE repo_id = ?
		ORDER BY gmt_create DESC
		LIMIT 1
	`

	row := s.db.QueryRow(query, repoID)

	overview := &definition.AgentWikiOverview{}
	err := row.Scan(
		&overview.ID,
		&overview.RepoID,
		&overview.Content,
		&overview.WorkspacePath,
		&overview.GmtCreate,
		&overview.GmtModified,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		log.Errorf("Failed to get wiki overview by repo ID: %v", err)
		return nil, fmt.Errorf("failed to get wiki overview: %w", err)
	}

	return overview, nil
}

// UpdateRecoveryCheckpoint 更新恢复检查点
func (s *LingmaWikiStorageService) UpdateRecoveryCheckpoint(repoID, checkpoint string) error {
	query := `
		UPDATE agent_wiki_repo 
		SET recovery_checkpoint = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(query, checkpoint, time.Now(), repoID)
	if err != nil {
		log.Errorf("Failed to update recovery checkpoint: %v", err)
		return fmt.Errorf("failed to update recovery checkpoint: %w", err)
	}

	return nil
}

// UpdateCatalogueThinkContent 更新目录思考内容
func (s *LingmaWikiStorageService) UpdateCatalogueThinkContent(repoID, thinkContent string) error {
	query := `
		UPDATE agent_wiki_repo 
		SET catalogue_think_content = ?, gmt_modified = ?
		WHERE id = ?
	`

	_, err := s.db.Exec(query, thinkContent, time.Now(), repoID)
	if err != nil {
		log.Errorf("Failed to update catalogue think content: %v", err)
		return fmt.Errorf("failed to update catalogue think content: %w", err)
	}

	return nil
}

// GetPendingCatalogsByRepoID 获取指定repo下pending状态的catalog列表 - 诊断用
func (s *LingmaWikiStorageService) GetPendingCatalogsByRepoID(repoID string) ([]*definition.AgentWikiCatalog, error) {
	return s.GetCatalogsByRepoIDAndStatus(repoID, definition.DeepWikiProgressStatusPending)
}

// DiagnoseCatalogStatus 诊断catalog状态分布 - 调试用
func (s *LingmaWikiStorageService) DiagnoseCatalogStatus(workspacePath string) error {
	repo, err := s.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		return fmt.Errorf("repo not found for workspace: %s", workspacePath)
	}

	statusCount, err := s.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get catalog status: %w", err)
	}

	log.Debugf("[DiagnoseCatalogStatus] Repo: %s (ID: %s)", repo.Name, repo.ID)
	log.Debugf("[DiagnoseCatalogStatus] Repo Status: %s", repo.ProgressStatus)
	log.Debugf("[DiagnoseCatalogStatus] Catalog Status Distribution:")
	for status, count := range statusCount {
		log.Debugf("[DiagnoseCatalogStatus]   %s: %d", status, count)
	}

	// 详细列出pending状态的catalog
	pendingCatalogs, err := s.GetPendingCatalogsByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[DiagnoseCatalogStatus] Failed to get pending catalogs: %v", err)
	} else if len(pendingCatalogs) > 0 {
		log.Debugf("[DiagnoseCatalogStatus] Pending Catalogs Detail:")
		for i, catalog := range pendingCatalogs {
			log.Debugf("[DiagnoseCatalogStatus]   %d. ID: %s, Name: %s, Description: %s, Created: %s",
				i+1, catalog.ID, catalog.Name, catalog.Description, catalog.GmtCreate.Format("2006-01-02 15:04:05"))
		}
	}

	return nil
}
