package storage

import (
	"cosy/definition"
	"fmt"
	"strings"
)

// ConvertToDocumentCatalogs 将数据库catalog转换为DocumentCatalog格式
func ConvertToDocumentCatalogs(dbCatalogs []*definition.AgentWikiCatalog, repoName string) []definition.DocumentCatalog {
	var documentCatalogs []definition.DocumentCatalog

	for _, dbCatalog := range dbCatalogs {
		documentCatalog := definition.DocumentCatalog{
			Id:            dbCatalog.ID,
			WarehouseId:   repoName,
			DocumentId:    fmt.Sprintf("doc_%s", repoName),
			Description:   dbCatalog.Description,
			Name:          dbCatalog.Name,
			Url:           dbCatalog.Description,
			DependentFile: parseDependentFiles(dbCatalog.DependentFiles),
			ParentId:      stringToPointer(dbCatalog.ParentID),
			Prompt:        dbCatalog.Prompt,
			Order:         dbCatalog.Order,
			Level:         calculateLevel(dbCatalog.ParentID, dbCatalogs),
			FullPath:      buildFullPathFromDB(dbCatalog, dbCatalogs),
		}
		documentCatalogs = append(documentCatalogs, documentCatalog)
	}

	return documentCatalogs
}

// stringToPointer 将字符串转换为指针，空字符串返回nil
func stringToPointer(str string) *string {
	if str == "" {
		return nil
	}
	return &str
}

// parseDependentFiles 解析依赖文件字符串为数组
func parseDependentFiles(dependentFilesStr string) []string {
	if dependentFilesStr == "" {
		return []string{}
	}
	return strings.Split(dependentFilesStr, ",")
}

// calculateLevel 计算catalog的层级
func calculateLevel(parentID string, allCatalogs []*definition.AgentWikiCatalog) int {
	if parentID == "" {
		return 0
	}

	// 递归查找父级的层级
	for _, catalog := range allCatalogs {
		if catalog.ID == parentID {
			return 1 + calculateLevel(catalog.ParentID, allCatalogs)
		}
	}

	return 0 // 如果找不到父级，返回0
}

// buildFullPathFromDB 从数据库记录构建完整路径
func buildFullPathFromDB(catalog *definition.AgentWikiCatalog, allCatalogs []*definition.AgentWikiCatalog) string {
	if catalog.ParentID == "" {
		return catalog.Name
	}

	// 递归构建父级路径
	for _, parent := range allCatalogs {
		if parent.ID == catalog.ParentID {
			parentPath := buildFullPathFromDB(parent, allCatalogs)
			return fmt.Sprintf("%s.%s", parentPath, catalog.Name)
		}
	}

	return catalog.Name // 如果找不到父级，返回自己的名称
}
