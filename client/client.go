package client

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/net/proxy"
)

// 客户端类型常量
const (
	CompletionClient          = "completion"
	ChatClient                = "chat"
	CodeSearchClient          = "code_search"
	DownloadClient            = "download"
	DefaultClient             = "default"
	DocEmbeddingClient        = "doc_embedding"
	DocRerankClient           = "doc_rerank"
	RemoteRetrieverCodeClient = "remote_retriever_code"
	ExtensionDownloadClient   = "extension_download"
	UploadFileClient          = "upload_file"
	FetchContentClient        = "fetch_content"
	CodebaseServerClient      = "codebase_server"
	RemoteAgentClient         = "remote_agent"
	RemoteAgentSSEClient      = "remote_agent_sse"
	WikiClient                = "wiki"
	NesClient                 = "nes"
)

// ClientConfig 客户端配置
type ClientConfig struct {
	Timeout         time.Duration
	MaxConnsPerHost int
}

// ClientManager 客户端管理器
type ClientManager struct {
	clients map[string]*http.Client
	configs map[string]ClientConfig
}

// 全局客户端管理器
var manager *ClientManager

// 初始化客户端管理器
func init() {
	manager = &ClientManager{
		clients: make(map[string]*http.Client),
		configs: make(map[string]ClientConfig),
	}
}

// RegisterClient 注册客户端配置
func (cm *ClientManager) RegisterClient(clientType string, config ClientConfig) {
	cm.configs[clientType] = config
}

// RegisterAndCreateClient 注册并立即创建客户端
func (cm *ClientManager) RegisterAndCreateClient(clientType string, config ClientConfig) {
	cm.configs[clientType] = config
	client := newClientWithTimeout(config.Timeout, config.MaxConnsPerHost)
	cm.clients[clientType] = client
}

// GetClient 获取客户端
func (cm *ClientManager) GetClient(clientType string) *http.Client {
	if client, exists := cm.clients[clientType]; exists {
		return client
	}

	// 如果客户端不存在，返回nil
	log.Warnf("Client type %s not found", clientType)
	return nil
}

// GetAllClients 获取所有注册的客户端
func (cm *ClientManager) GetAllClients() []*http.Client {
	var clients []*http.Client
	for _, client := range cm.clients {
		clients = append(clients, client)
	}
	return clients
}

// UpdateProxyForAllClients 更新所有客户端的代理配置
func (cm *ClientManager) UpdateProxyForAllClients() {
	transportToUse := buildProxyTransport(5)
	if transportToUse != nil {
		log.Infof("Http proxy configure changed")
		for _, client := range cm.clients {
			client.Transport = transportToUse
		}
	}
}

// InitClients 初始化所有客户端
func InitClients() {
	// 注册并立即创建所有客户端
	manager.RegisterAndCreateClient(CompletionClient, ClientConfig{
		Timeout:         5 * time.Second,
		MaxConnsPerHost: 3,
	})

	manager.RegisterAndCreateClient(ChatClient, ClientConfig{
		Timeout:         300 * time.Second,
		MaxConnsPerHost: 3,
	})

	manager.RegisterAndCreateClient(CodeSearchClient, ClientConfig{
		Timeout:         10 * time.Second,
		MaxConnsPerHost: 1,
	})

	manager.RegisterAndCreateClient(DownloadClient, ClientConfig{
		Timeout:         30 * time.Minute,
		MaxConnsPerHost: 3,
	})

	manager.RegisterAndCreateClient(DefaultClient, ClientConfig{
		Timeout:         5 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(DocEmbeddingClient, ClientConfig{
		Timeout:         10 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(DocRerankClient, ClientConfig{
		Timeout:         30 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(RemoteRetrieverCodeClient, ClientConfig{
		Timeout:         5 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(ExtensionDownloadClient, ClientConfig{
		Timeout:         10 * time.Minute,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(UploadFileClient, ClientConfig{
		Timeout:         60 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(FetchContentClient, ClientConfig{
		Timeout:         30 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(CodebaseServerClient, ClientConfig{
		Timeout:         45 * time.Second,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(RemoteAgentClient, ClientConfig{
		Timeout:         10 * time.Second,
		MaxConnsPerHost: 30,
	})

	manager.RegisterAndCreateClient(RemoteAgentSSEClient, ClientConfig{
		Timeout:         0, // 不设置超时
		MaxConnsPerHost: 30,
	})

	manager.RegisterAndCreateClient(WikiClient, ClientConfig{
		Timeout:         10 * time.Minute,
		MaxConnsPerHost: 5,
	})

	manager.RegisterAndCreateClient(NesClient, ClientConfig{
		Timeout:         8 * time.Second,
		MaxConnsPerHost: 3,
	})
}

// 获取注册的http client列表
func GetRegisteredClients() []*http.Client {
	return manager.GetAllClients()
}

// 以下是保持向后兼容的getter函数
func GetCompletionClient() *http.Client {
	return manager.GetClient(CompletionClient)
}

func GetChatClient() *http.Client {
	return manager.GetClient(ChatClient)
}

func GetCodeSearchClient() *http.Client {
	return manager.GetClient(CodeSearchClient)
}

func GetDownloadClient() *http.Client {
	return manager.GetClient(DownloadClient)
}

func GetDefaultClient() *http.Client {
	return manager.GetClient(DefaultClient)
}

func GetDocEmbeddingClient() *http.Client {
	return manager.GetClient(DocEmbeddingClient)
}

func GetDocRerankClient() *http.Client {
	return manager.GetClient(DocRerankClient)
}

func GetRemoteRetrieverCodeClient() *http.Client {
	return manager.GetClient(RemoteRetrieverCodeClient)
}

func GetExtensionDownloadClient() *http.Client {
	return manager.GetClient(ExtensionDownloadClient)
}

func GetUploadFileClient() *http.Client {
	return manager.GetClient(UploadFileClient)
}

func GetFetchContentClient() *http.Client {
	return manager.GetClient(FetchContentClient)
}

func GetCodebaseServerClient() *http.Client {
	return manager.GetClient(CodebaseServerClient)
}

func GetRemoteAgentClient() *http.Client {
	return manager.GetClient(RemoteAgentClient)
}

func GetRemoteAgentSSEClient() *http.Client {
	return manager.GetClient(RemoteAgentSSEClient)
}

func GetWikiClient() *http.Client {
	return manager.GetClient(WikiClient)
}

func GetNesClient() *http.Client {
	return manager.GetClient(NesClient)
}

func newClientWithTimeout(timeout time.Duration, maxConnsPerHost int) *http.Client {
	cli := &http.Client{
		Timeout: timeout,
	}
	transport := buildProxyTransport(maxConnsPerHost)
	if transport != nil {
		cli.Transport = transport
	}
	return cli
}

func UpdateHttpClientProxy() {
	manager.UpdateProxyForAllClients()
}

func DownloadFile(url string, localPath string) error {
	// 创建输出文件
	_ = os.MkdirAll(filepath.Dir(localPath), 0755)
	file, err := os.Create(localPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 发起 HTTP GET 请求
	response, err := GetDownloadClient().Get(url)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	// 检查 HTTP 响应状态码
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status code %d", response.StatusCode)
	}

	// 将响应体写入文件
	_, err = io.Copy(file, response.Body)
	if err != nil {
		return err
	}

	return nil
}

func buildProxyTransport(maxConnsPerHost int) *http.Transport {
	if config.GlobalModelConfig.ProxyMode == definition.ProxyModeSystem {
		if global.HttpsProxy != "" {
			return buildTransportWithProxyUri(global.HttpsProxy, maxConnsPerHost)
		}
	} else if config.GlobalModelConfig.ProxyMode == definition.ProxyModeManual {
		if config.GlobalModelConfig.HttpProxy != "" {
			return buildTransportWithProxyUri(config.GlobalModelConfig.HttpProxy, maxConnsPerHost)
		}
	}
	return newDefaultNoneProxyTransport(maxConnsPerHost)
}

func buildTransportWithProxyUri(proxyUri string, maxConnsPerHost int) *http.Transport {
	proxyUri = strings.TrimSpace(proxyUri)

	proxyUrl, err := url.Parse(proxyUri)
	if err == nil {
		proxyTransport := newDefaultNoneProxyTransport(maxConnsPerHost)

		if proxyUrl.Scheme == "http" || proxyUrl.Scheme == "https" {
			proxyTransport.Proxy = http.ProxyURL(proxyUrl)
		} else if proxyUrl.Scheme == "socks5" {
			dialer, err := proxy.FromURL(proxyUrl, proxy.Direct)
			if err != nil {
				log.Infof("Sock5 proxy ignored")
			} else {
				proxyTransport.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
					return dialer.Dial(network, addr)
				}
			}
		}
		return proxyTransport
	} else {
		log.Warnf("Proxy url is invalid. proxy url=" + proxyUri)
	}
	return nil
}

// 默认无proxy
func newDefaultNoneProxyTransport(maxConnsPerHost int) *http.Transport {
	if maxConnsPerHost <= 0 {
		maxConnsPerHost = 5
	}
	return &http.Transport{
		Proxy:                 nil,
		MaxConnsPerHost:       maxConnsPerHost,
		MaxIdleConns:          3,                // 最大空闲连接数
		MaxIdleConnsPerHost:   3,                // 每个主机的最大空闲连接数
		IdleConnTimeout:       10 * time.Minute, // 空闲连接的超时时间
		ForceAttemptHTTP2:     true,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 2 * time.Second,
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		DisableCompression:    true,
	}
}
