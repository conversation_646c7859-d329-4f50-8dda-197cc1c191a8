package sls

const (
	LocalCompletionStatistics string = "cosy_local_statistics"
	Uninstall                 string = "cosy_uninstall"
	HeartBeat                 string = "cosy_heartbeat"
	RemoteComplete            string = "remote_complete"
	RemotePerformance         string = "remote_performance"

	CloudCompletionStatistics       string = "cloud_completion_statistics"
	CloudCompletionStatisticsRecall string = "cloud_completion_statistics_recall"
	CloudChatStatistics             string = "cloud_chat_statistics"

	// 灵码问答触发相关的各种统计信息（比如意图识别结果）
	LingmaChatTriggerStatistics string = "cosy_lingma_chat_trigger_statistics"
	EventTypeChatTriggerInfo    string = "cosy_lingma_chat_trigger_info"

	// 服务端知识库RAG召回统计
	EventTypeRemoteRagCompletionStatistics string = "cosy_remote_rag_completion_statistics"

	EventTypeChatAgentRequest = "chat-agent-request"

	EventTypeChatAiDeveloperOperate = "chat-ai-developer-operate"

	EventTypeChatAiDeveloperWorkspaceFileOperation = "chat-ai-developer-workspace-file-operation"

	EventTypeChatAiDeveloperDiffInfo = "chat-ai-developer-diffinfo"

	EventTypeChatAiDeveloperClearSnapshot = "chat-ai-developer-clear-snapshot"

	EventTypeChatAiDeveloperCreateSnapshot = "chat-ai-developer-create-snapshot"

	EventTypeChatAiDeveloperStop = "chat-ai-developer-stop"

	EventTypeChatAiDeveloperApplyInfo = "chat-ai-developer-applyinfo"

	EventTypeChatAiDeveloperLintInfo = "chat-ai-developer-lintinfo"

	EventTypeChatAiDeveloperReApplyInfo = "chat-ai-developer-reapplyinfo"

	EventTypeChatAiDeveloperFixPath = "chat-ai-developer-fixpath"

	// Test Agent 埋点事件
	EventTypeChatAiDeveloperTestAgentPlan           = "chat-ai-developer-test-agent-plan"
	EventTypeChatAiDeveloperTestAgentGenerate       = "chat-ai-developer-test-agent-generate"
	EventTypeChatAiDeveloperTestAgentMerge          = "chat-ai-developer-test-agent-merge"
	EventTypeChatAiDeveloperTestAgentRemerge        = "chat-ai-developer-test-agent-remerge"
	EventTypeChatAiDeveloperTestAgentCompileFailure = "chat-ai-developer-test-agent-compile-failure"
	EventTypeChatAiDeveloperTestAgentRunNotPass     = "chat-ai-developer-test-agent-run-not-pass"
	EventTypeChatAiDeveloperTestAgentAccept         = "chat-ai-developer-test-agent-accept"

	EventTypeMonitor            = "monitor"
	EventTypeStableApiMonitor   = "stable_api_monitor"   //用于监控灵码请求的摘要信息，包括：{url}.{isSuccess}.{time}.{errorCode}
	EventTypeStablePanicMonitor = "stable_panic_monitor" //用于监控灵码请求中产生的panic错误

	// Codebase相关
	EventTypeChatCodebaseRecommendFileSingleRound    = "chat-codebase-recommend-file-single-round"
	EventTypeChatCodebaseRecommendFileMultiRound     = "chat-codebase-recommend-file-multi-round"
	EventTypeChatCodebaseRecommendFileQueryResults   = "chat-codebase-recommend-file-query-results"
	EventTypeChatCodebaseFullVectorIndexTrigger      = "chat-codebase-full-vector-index-trigger"
	EventTypeChatCodebaseFullVectorIndexFinish       = "chat-codebase-full-vector-index"
	EventTypeChatCodebaseServerFullVectorIndexFinish = "chat-codebase-server-full-vector-index"
	EventTypeChatCodebaseVectorStoragePanic          = "chat-codebase-vector-storage-panic"
	EventTypeChatCodebaseGraphStageFinish            = "chat-codebase-graph-stage-finish"
	EventTypeChatCodebaseGraphPanic                  = "chat-codebase-graph-panic"
	EventTypeChatCodebaseGraphLocateResult           = "chat-codebase-graph-locate-result"
	EventTypeChatCodebaseGraphTravelResult           = "chat-codebase-graph-travel-result"
	EventTypeChatCodebaseGraphExpandResult           = "chat-codebase-graph-expand-result"

	EventTypeChatCodebaseShareChunkPanic  = "chat-codebase-share-chunk-panic"
	EventTypeChatCodebaseShareChunkStatus = "chat-codebase-share-chunk"
	// 记忆相关
	EventTypeChatAgentMemoryRetrieve = "chat-agent-memory-retrieve"
	EventTypeChatAgentMemoryForget   = "chat-agent-memory-forget"
	EventTypeChatAgentMemoryExtract  = "chat-agent-memory-extract"
	EventTypeChatAgentMemoryEval     = "chat-agent-memory-eval"
	EventTypeChatAgentMemoryDelete   = "chat-agent-memory-delete"
	EventTypeChatAgentMemoryList     = "chat-agent-memory-list"
	EventTypeConsolidateMemory       = "chat-agent-memory-consolidate"

	// Wiki知识评估相关
	EventTypeChatWikiEval = "chat-wiki-eval"
	// Wiki搜索相关
	EventTypeChatCodebaseWikiSearch = "chat-codebase-wiki-search"

	// 工具调用相关
	EventTypeAgentToolCallStatistics      = "agent-tool-call-statistics"
	EventTypeAgentToolCallMCPTool         = "agent-tool-call-mcp-tool"
	EventTypeAgentMCPServerStatistics     = "agent-mcp-server-statistics"
	EventTypeAgentMCPServerAdd            = "agent-mcp-server-add"
	EventTypeAgentPromptABTest            = "agent-prompt-abtest"
	EventTypeAgentRemotePromptRenderError = "agent-prompt-render-error"
	EventTypeAgentMCPServerStartError     = "agent-mcp-server-start-error"

	// NES
	EventTypeInlineEditPerformance = "inline_edit_performance"
	//  内联编辑校验
	EventTypeInlineEditValidate = "inline_edit_validate"
	//agent相关
	EventTypeAgentLLmRequestPerformance    = "chat-ai-developer-llm-performance"
	EventTypeAgentProcessFinish            = "chat-ai-developer-process-finish"
	EventTypeAgentCodeChangeLineRemainRate = "chat-ai-developer-code-change-line-remain-rate"

	// 问答链路耗时追踪
	EventTypeChatChainPerformance = "chat-chain-performance"

	//上下文埋点
	EventTypeChatContextManualImport = "chat-context-manual-import"
	// Project Rule
	EventTypeProjectRuleEffective = "chat-project-rule-effective"
	// Quest Mode任务埋点
	EventTypeQuestTaskCreation  = "cosy_quest-task-creation"
	EventTypeQuestTaskExecution = "cosy_quest-task-execution"

	//单次ask消耗token埋点
	EventTypeChatLLMTokenInfo = "chat-llm-token-info"

	// 搜索记忆工具埋点
	EventTypeSearchMemoryTool = "search-memory-tool"
)
