package sls

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

type LogReporter struct {
	httpClient      *http.Client
	lastPostSuccess bool
	cacheIndex      int
	eventCaches     [][]CosyReportData
	lock            sync.Mutex
}

const ReportTracking = "tracking"
const ReportHeartbeat = "heartbeat"
const ReportTriggerExpireInterval = 5 * time.Minute

var Reporter LogReporter

var asyncReportTimer *cron.Cron

var ReportTriggerMap sync.Map

func cleanReportTriggerMap() {
	ReportTriggerMap.Range(func(key, value interface{}) bool {
		if k, ok := key.(string); ok {
			if v, ok := value.(definition.ReportTriggerInfo); ok {
				if time.Now().Sub(v.RecordTime) > ReportTriggerExpireInterval {
					ReportTriggerMap.Delete(k)
				} else {
					return true // 继续遍历
				}
			} else {
				log.Warnf("ReportTriggerMap value is not ReportTriggerInfo, value is %v", value)
			}
		} else {
			log.Warnf("ReportTriggerMap key is not string, value is %v", value)
		}
		return true // 继续遍历
	})
}

func InitReporter() {
	Reporter = LogReporter{
		httpClient:      client.GetDefaultClient(),
		lastPostSuccess: true,
		cacheIndex:      0,
		eventCaches:     [][]CosyReportData{{}, {}},
		lock:            sync.Mutex{},
	}

	// Add timer, report completion statistics periodically
	asyncReportTimer = cron.New()
	asyncReportTimer.Start()

	// Report every 3 minute
	//单位：分钟
	_, _ = asyncReportTimer.AddFunc("@every 3m", summitAsyncReport)

	_, _ = asyncReportTimer.AddFunc("@every 10m", cleanReportTriggerMap)
}

// Report uploads events to sls. The uploaded event will be stored in ODPS
func Report(eventType, requestId string, extInfo map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Error("sls report Recovered from panic: ", r, string(stack))
		}
	}()

	Reporter.lock.Lock()
	defer Reporter.lock.Unlock()
	Reporter.eventCaches[Reporter.cacheIndex] =
		append(Reporter.eventCaches[Reporter.cacheIndex], ReportData(eventType, requestId, extInfo))
	if global.DebugMode {
		extData, _ := json.Marshal(extInfo)
		log.Debugf("Reporting requestId: %s, eventType: %s with data %s", requestId, eventType, extData)
	}
}

// ReportSync 仅安装/卸载时同步上传
// ReportSync reports given events and waits for the result
func ReportSync(eventType, requestId string, extInfo map[string]string) {
	failedEvents := Reporter.postReports(ReportTracking, []CosyReportData{ReportData(eventType, requestId, extInfo)}, 20)
	if len(failedEvents) > 0 {
		log.Warnf("Failed to report event \"%s\"", eventType)
	}
	if global.DebugMode {
		extData, _ := json.Marshal(extInfo)
		log.Debugf("Reporting %s, with data %s", eventType, string(extData))
	}
}

func summitAsyncReport() {
	Reporter.lock.Lock()
	defer Reporter.lock.Unlock()
	nextIndex := (Reporter.cacheIndex + 1) % 2
	if !Reporter.lastPostSuccess {
		// 如果上一次推送不成功，用更小的BatchSize重试一次
		_ = Reporter.postReports(ReportTracking, Reporter.eventCaches[nextIndex], 15)
		Reporter.eventCaches[nextIndex] = []CosyReportData{}
	}
	totalEventCount := len(Reporter.eventCaches[Reporter.cacheIndex])
	failedEvents := Reporter.postReports(ReportTracking, Reporter.eventCaches[Reporter.cacheIndex], 30)
	log.Infof("Reported %d/%d events", totalEventCount-len(failedEvents), totalEventCount)
	if len(failedEvents) > 0 {
		// 发送埋点失败
		Reporter.lastPostSuccess = false
		Reporter.eventCaches[Reporter.cacheIndex] = failedEvents
	} else {
		// 发送埋点成功，清空缓存
		Reporter.eventCaches[Reporter.cacheIndex] = []CosyReportData{}
		Reporter.lastPostSuccess = true
	}
	Reporter.cacheIndex = nextIndex
}

func (reporter *LogReporter) PostHeartbeat(event CosyReportData) ([]byte, error) {
	req, err := remote.BuildBigModelSignRequest(http.MethodPost, definition.UrlPathReportHeartbeat, event)
	if err != nil {
		return nil, errors.New("unable to build heartbeat request: " + err.Error())
	}
	resp, err := reporter.httpClient.Do(req)
	if err != nil {
		log.Warnf("post heartbeat data failed: %v", err)
		return nil, cosyErrors.ErrRequestTimeout
	}
	defer resp.Body.Close()
	responseBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		return nil, errors.New(fmt.Sprintf("heartbeat failed with code %d: %s", resp.StatusCode, responseBody))
	}
	return responseBody, nil
}

func (reporter *LogReporter) postReports(reportType string, events []CosyReportData, batchSize int) []CosyReportData {
	var validEvents []CosyReportData
	var failedEvents []CosyReportData
	count := 0
	for _, e := range events {
		if e.EventType != "" {
			validEvents = append(validEvents, e)
			count++
		}
		// 达到一批的量，直接发送
		if count >= batchSize {
			if !reporter.doPost(reportType, validEvents) {
				failedEvents = append(failedEvents, validEvents...)
			}
			validEvents = []CosyReportData{}
			count = 0
		}
	}
	// 余下部分作为单独一批发送
	if len(validEvents) > 0 {
		if !reporter.doPost(reportType, validEvents) {
			failedEvents = append(failedEvents, validEvents...)
		}
	}
	return failedEvents
}

func (reporter *LogReporter) doPost(reportType string, validEvents []CosyReportData) bool {
	urlPath := getUrlPathByReportType(reportType)
	if urlPath == "" {
		log.Errorf("reportType is invalid. reportType: %s", reportType)
		return true
	}

	req, err := remote.BuildBigModelSignRequest(http.MethodPost, urlPath, validEvents)
	if err != nil {
		log.Warnf("unable to build statistics request: %s", err.Error())
		return false
	}
	resp, err := reporter.httpClient.Do(req)
	if err != nil {
		log.Warnf("post report data failed: %s", err.Error())
		return false
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		responseBody, _ := io.ReadAll(resp.Body)
		log.Warnf(fmt.Sprintf("report failed with code %d: %s", resp.StatusCode, responseBody))
		return false
	}
	return true
}

func getUrlPathByReportType(reportType string) string {
	switch reportType {
	case ReportTracking:
		return definition.UrlPathReportTracking
	case ReportHeartbeat:
		return definition.UrlPathReportHeartbeat
	default:
		return ""
	}
}

func ReportData(eventType string, requestId string, eventData map[string]string) CosyReportData {
	cachedUserInfo := user.GetCachedUserInfo()
	var userReportData = CosyUserReportData{}
	if cachedUserInfo != nil {
		userReportData.AliyunAid = cachedUserInfo.Aid
		userReportData.AliyunUid = cachedUserInfo.Uid
		userReportData.YxUid = cachedUserInfo.YxUid
		userReportData.OrganizationId = cachedUserInfo.OrgId

		if cachedUserInfo.UserSourceChannel != "" {
			eventData["user_source_channel"] = cachedUserInfo.UserSourceChannel
		}
		if config.GlobalRegionConfig.GetCodebaseRegion() != nil {
			eventData["user_region"] = config.GlobalRegionConfig.GetCodebaseRegion().Region
		}
	}

	experimentalFeatures := make(map[string]interface{})
	ideSeries, ok := eventData["ide_series"]
	if ok {
		ideExperimentItemType, err := experiment.GetExperimentTypeByIdeConfig(definition.IdeConfig{IdeSeries: ideSeries})
		if err == nil {
			ideExpConfig := experiment.ConfigService.GetExperimentConfigs(ideExperimentItemType)
			ideToUploadConfig := buildExpData(ideExperimentItemType, ideExpConfig)
			if ideToUploadConfig != nil {
				for k, v := range ideToUploadConfig {
					experimentalFeatures[k] = v
				}
			}
		}
	}
	clientExpConfig := experiment.ConfigService.GetExperimentConfigs(experiment.ConfigScopeClient)
	clientToUploadConfig := buildExpData(experiment.ConfigScopeClient, clientExpConfig)
	if clientToUploadConfig != nil {
		for k, v := range clientToUploadConfig {
			experimentalFeatures[k] = v
		}
	}
	eventData["expr_features"] = util.ToJsonStr(experimentalFeatures)
	return NewReportData(eventType, util.GetMachineId(true), requestId, userReportData, eventData)
}

func buildExpData(ideExperimentItemType string, configs map[string]string) map[string]string {
	if ideExperimentItemType == "" || len(configs) <= 0 {
		return nil
	}
	newDataMap := make(map[string]string)
	for k, v := range configs {
		newDataMap[ideExperimentItemType+"."+k] = v
	}
	return newDataMap
}
